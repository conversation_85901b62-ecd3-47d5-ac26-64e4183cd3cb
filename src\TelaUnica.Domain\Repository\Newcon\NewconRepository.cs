using AutoMapper;
using Newtonsoft.Json;
using TelaUnica.Domain.Dtos.Request.Config;
using TelaUnica.Domain.Dtos.Request.Newcon;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsNewSegSerasa;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsRetornaDadosClienteBemSemPagto;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsRetornaDadosCotasCliente;
using TelaUnica.Domain.Dtos.Request.Newcon.PrecoBemService.ConsultarEvolucaoPrecoBem;
using TelaUnica.Domain.Dtos.Request.Newcon.Services;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.AgendaService.ConsultarOcorrenciasCota;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.ConsultaParcelasCob;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.CotaService.ConsultarSaldoAcumuladoCota;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.EstruturaVendaService.ConsultarEstruturaVendaCota;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.PosicaoConsorciadoService.ConsultarParcelaAtraso;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.PrecoBemService;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.ValoresPagosService.ConsultarValoresPagos;
using TelaUnica.Domain.Dtos.Request.NewconApi;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsCotas;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsNewSegSerasa;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsRetornaDadosClienteBemSemPagto;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsRetornaDadosCotasCliente;
using TelaUnica.Domain.Dtos.Response.Newcon.Services;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.AgendaService.ConsultarOcorrenciasCota;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.ConsultaParcelasCob;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.CotaService.ConsultarEvolucaoPrecoBem;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.CotaService.ConsultarSaldoAcumuladoCota;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.EstruturaVendaService.ConsultarEstruturaVendaCota;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.PosicaoConsorciadoService.ConsultarParcelaAtraso;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.ValoresPagosService.ConsultarValoresPagos;
using TelaUnica.Domain.Dtos.Response.NewconApi;
using TelaUnica.Domain.Interfaces;
using TelaUnica.Domain.Interfaces.Newcon;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Models;

namespace TelaUnica.Domain.Repository.Newcon;

public class NewconRepository(INewconCotaRepository newconCotaRepository, INewconRoboSemafaroRepository newconRoboSemafaroRepository, IConfigRepository config, ApiHelpers helper, IMapper mapper) : INewconRepository
{
    private readonly INewconCotaRepository _newconCotaRepository = newconCotaRepository;
    private readonly INewconRoboSemafaroRepository _newconRoboSemafaroRepository = newconRoboSemafaroRepository;
    private readonly IConfigRepository _config = config;
    private readonly ApiHelpers _helper = helper;
    private string url = "";
    private readonly IMapper _mapper = mapper;

    public string Url() => _config.GetValueByKey("newcon_url").Result ?? "";

    public async Task<ServiceResponse<StcCotas>> PostCnsCotas(string newconCotaId)
    {
        ServiceResponse<StcCotas> response = new();
        try
        {
            var cota = await _newconCotaRepository.GetById(newconCotaId);

            if (cota is null || cota.DataSicronizacao == DateTime.MinValue || cota.DataSicronizacao <= DateTime.Now.AddDays(-1))
            {
                //Criar agenda e entregar os dados
                var processo = await _newconRoboSemafaroRepository.Create(new NewconRoboSemafaroCreateRequest
                {
                    NewconCotaId = newconCotaId,
                    Status = NewconRoboSemafaroStatus.Pendente,
                    DataProcesso = DateTime.Now
                });
                await ScheduleExecutionNow(new() { automation_name = "TelaUnicaPosicaoConsignadoRandon", pentaho = 0, executableTypeFile = "BAT" });
                if (await MonitorarStatusAsync(processo.Data!.Id) != null) cota = await _newconCotaRepository.GetById(newconCotaId);

                response.Data = _mapper.Map<StcCotas>(cota);
            }
            else
                response.Data = _mapper.Map<StcCotas>(cota);

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<List<NewconRoboSemafaroResponse>>> GetSemafaro()
    {
        return await _newconRoboSemafaroRepository.Get(null, NewconRoboSemafaroStatus.Pendente);
    }

    public async Task<ServiceResponse<NewconRoboSemafaroResponse>> GetSemafaroStatus(Guid id, NewconRoboSemafaroStatus status, string message)
    {
        return new ServiceResponse<NewconRoboSemafaroResponse>() { Data = await _newconRoboSemafaroRepository.GetStatusId(id, status, message) };
    }

    private async Task<NewconRoboSemafaroResponse?> MonitorarStatusAsync(Guid id)
    {
        const int intervaloEmSegundos = 30;
        int tentativas = 0;

        while (true)
        {
            var processo = await _newconRoboSemafaroRepository.GetById(id);

            if (processo!.Status == NewconRoboSemafaroStatus.Concluido || processo.Status == NewconRoboSemafaroStatus.Falha || tentativas == 30)
            {
                Console.WriteLine($"Processo finalizado com status: {processo.Status}");
                return processo;
            }

            Console.WriteLine($"Status atual: {processo.Status}. Aguardando {intervaloEmSegundos} segundos...");
            tentativas++;
            await Task.Delay(TimeSpan.FromSeconds(intervaloEmSegundos));
        }
    }

    public async Task<string> Login()
    {
        url = await _config.GetValueByKey($"manager_url");
        string token = await _config.GetValueByKey($"manager_token");
        string tokenExpire = await _config.GetValueByKey($"manager_token_expires_at");
        string Email = await _config.GetValueByKey($"manager_email");
        string Password = await _config.GetValueByKey($"manager_password");
        string urlLogin = $"{url}/api/v1/auth/login";

        Dictionary<string, string> data = new() { { "email", Email }, { "password", Password } };

        var responseApi = await _helper.PostApi<ApiResponse<RoboLoginResponse>>(urlLogin, data);

        if (responseApi.Success == true && responseApi.Data != null)
        {
            token = responseApi.Data?.Data?.token;
            ConfigUpdateRequest tokenUpdate = new()
            {
                Key = $"manager_token",
                Value = responseApi.Data?.Data?.token,
            };
            await _config.Update(tokenUpdate);
        }
        return token;
    }

    public async Task<ServiceResponse<bool>> ScheduleExecutionNow(RoboSchedule request)
    {
        ServiceResponse<bool> response = new();
        try
        {
            var token = await Login();
            string urlLogin = $"{url}/api/v1/automation/schedule-execution-now";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<ApiResponse<bool?>> responseApi = await _helper.PostApi<ApiResponse<bool?>>(urlLogin, data, token);
            response.Data = responseApi.Success;
            response.Message = responseApi.Data.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Data = false;
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<NewconEnvelope<CnsRetornaDadosClienteBemSemPagtoBodyResponse>>> PostCnsRetornaDadosClienteBemSemPagto(CnsRetornaDadosClienteBemSemPagto request)
    {
        ServiceResponse<NewconEnvelope<CnsRetornaDadosClienteBemSemPagtoBodyResponse>> response = new();
        try
        {
            //var cota = await _newconCotaRepository.GetById(request.NewconCotaId ?? "");

            throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            /*
            ApiResponse<NewconEnvelope<CnsRetornaDadosClienteBemSemPagtoBodyResponse>> responseApi = new();

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;*/

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<NewconEnvelope<CnsRetornaDadosCotasClienteBodyResponse>>> PostCnsRetornaDadosCotasCliente(CnsRetornaDadosCotasCliente request)
    {
        ServiceResponse<NewconEnvelope<CnsRetornaDadosCotasClienteBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/atendimentoNewcon/ws_ConsultaNewCon.asmx";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            NewconEnvelope<CnsRetornaDadosCotasClienteBody> data = new()
            {
                Envelope = new()
                {
                    Body = request
                }
            };

            ApiResponse<NewconEnvelope<CnsRetornaDadosCotasClienteBodyResponse>> responseApi = await _helper.PostApiXml<NewconEnvelope<CnsRetornaDadosCotasClienteBody>, NewconEnvelope<CnsRetornaDadosCotasClienteBodyResponse>>(urlApi, data);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<NewconEnvelope<CnsNewSegSerasaBodyResponse>>> PostCnsNewSegSerasa(CnsNewSegSerasa request)
    {

        ServiceResponse<NewconEnvelope<CnsNewSegSerasaBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/atendimentoNewcon/ws_ConsultaNewSeg.asmx";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            NewconEnvelope<CnsNewSegSerasaBody> data = new()
            {
                Envelope = new()
                {
                    Body = request
                }
            };

            ApiResponse<NewconEnvelope<CnsNewSegSerasaBodyResponse>> responseApi = await _helper.PostApiXml<NewconEnvelope<CnsNewSegSerasaBody>, NewconEnvelope<CnsNewSegSerasaBodyResponse>>(urlApi, data);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeServiceResponse<ElementCotaServiceBodyResponse>>> PostCotaServiceConsultarSaldoAcumuladoCota(ElementCotaServiceRequest request)
    {
        ServiceResponse<ElementEnvelopeServiceResponse<ElementCotaServiceBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/Atendimento/Servicos/CotaService.svc";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopeService<ElementCotaServiceBody> data = new()
            {
                Envelope = new()
                {
                    Element = new()
                    {
                        Element = request
                    }
                }
            };
            var header = new Dictionary<string, string>();
            header.Add("soapAction", "http://tempuri.org/ICotaService/ConsultarSaldoAcumuladoCota");

            ApiResponse<ElementEnvelopeServiceResponse<ElementCotaServiceBodyResponse>> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopeService<ElementCotaServiceBody>, ElementEnvelopeServiceResponse<ElementCotaServiceBodyResponse>>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarEvolucaoPrecoBemBodyResponse>>> PostPrecoBemServiceConsultarEvolucaoPrecoBem(ElementConsultarEvolucaoPrecoBemRequest request)
    {
        ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarEvolucaoPrecoBemBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/Atendimento/Servicos/PrecoBemService.svc";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopePrecoBemService<ElementConsultarEvolucaoPrecoBemBody> data = new()
            {
                Envelope = new()
                {
                    Element = new()
                    {
                        Element = request
                    }
                }
            };
            var header = new Dictionary<string, string>
            {
                { "soapAction", "http://tempuri.org/IPrecoBemService/ConsultarEvolucaoPrecoBem" }
            };

            ApiResponse<ElementEnvelopeServiceResponse<ElementConsultarEvolucaoPrecoBemBodyResponse>> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopePrecoBemService<ElementConsultarEvolucaoPrecoBemBody>,
                ElementEnvelopeServiceResponse<ElementConsultarEvolucaoPrecoBemBodyResponse>>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarEstruturaVendaCotaBodyResponse>>> PostEstruturaVendaService(ElementConsultarEstruturaVendaCotaRequest request)
    {
        ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarEstruturaVendaCotaBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/Atendimento/Servicos/EstruturaVendaService.svc";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopeService<ElementConsultarEstruturaVendaCotaBody> data = new()
            {
                Envelope = new()
                {
                    Element = new()
                    {
                        Element = request
                    }
                }
            };
            var header = new Dictionary<string, string>
            {
                { "soapAction", "http://tempuri.org/IEstruturaVendaService/ConsultarEstruturaVendaCota" }
            };

            ApiResponse<ElementEnvelopeServiceResponse<ElementConsultarEstruturaVendaCotaBodyResponse>> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopeService<ElementConsultarEstruturaVendaCotaBody>,
                        ElementEnvelopeServiceResponse<ElementConsultarEstruturaVendaCotaBodyResponse>>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarOcorrenciasCotaBodyResponse>>> PostAgendaServiceConsultarOcorrenciasCota(ElementConsultarOcorrenciasCotaRequest request)
    {
        ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarOcorrenciasCotaBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/Atendimento/Servicos/AgendaService.svc";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopeService<ElementConsultarOcorrenciasCotaBody> data = new()
            {
                Envelope = new()
                {
                    Element = new()
                    {
                        Element = request
                    }
                }
            };
            var header = new Dictionary<string, string>
            {
                { "soapAction", "http://tempuri.org/IAgendaService/ConsultarOcorrenciasCota" }
            };

            ApiResponse<ElementEnvelopeServiceResponse<ElementConsultarOcorrenciasCotaBodyResponse>> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopeService<ElementConsultarOcorrenciasCotaBody>, ElementEnvelopeServiceResponse<ElementConsultarOcorrenciasCotaBodyResponse>>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarValoresPagosBodyResponse>>> PostValoresPagosServiceConsultarValoresPagos(ElementConsultarValoresPagosRequest request)
    {
        ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarValoresPagosBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/Atendimento/Servicos/NWSRVAT_ValoresPagos.svc";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopeService<ElementConsultarValoresPagosBody> data = new()
            {
                Envelope = new()
                {
                    Element = new()
                    {
                        Element = request
                    }
                }
            };
            var header = new Dictionary<string, string>
            {
                { "SOAPAction", "https://www.newconsoftware.com.br/Newcon.Servicos.Atendimento/INWSRVAT_ValoresPagos/ConsultarValoresPagos" }
            };

            ApiResponse<ElementEnvelopeServiceResponse<ElementConsultarValoresPagosBodyResponse>> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopeService<ElementConsultarValoresPagosBody>, ElementEnvelopeServiceResponse<ElementConsultarValoresPagosBodyResponse>>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarParcelaAtrasoBodyResponse>>> PostPosicaoConsorciadoServiceConsultarParcelaAtraso(ElementConsultarParcelaAtrasoRequest request)
    {
        ServiceResponse<ElementEnvelopeServiceResponse<ElementConsultarParcelaAtrasoBodyResponse>> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/Atendimento/Servicos/NWSRVAT_PosicaoConsorciado.svc";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopeService<ElementConsultarParcelaAtrasoBody> data = new()
            {
                Envelope = new()
                {
                    Element = new()
                    {
                        Element = request
                    }
                }
            };
            var header = new Dictionary<string, string>
            {
                { "SOAPAction", "http://tempuri.org/INWSRVAT_PosicaoConsorciado/ConsultarParcelaAtraso" }
            };

            ApiResponse<ElementEnvelopeServiceResponse<ElementConsultarParcelaAtrasoBodyResponse>> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopeService<ElementConsultarParcelaAtrasoBody>, ElementEnvelopeServiceResponse<ElementConsultarParcelaAtrasoBodyResponse>>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<ElementEnvelopeCobServiceResponse>> PostConsultaParcelasCob(ElementConsultaParcelasCob request)
    {
        ServiceResponse<ElementEnvelopeCobServiceResponse> response = new();
        try
        {
            // throw new("M�todo n�o implementado, favor verificar com o administrador do sistema.");
            
            string urlApi = $"{Url()}/atendimentonewcon/ws_ConsultaParcelasCob.asmx";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            ElementEnvelopeCobService<ElementConsultaParcelasCobBody> data = new()
            {
                Envelope = new()
                {
                    Element = request
                },
                Header = new()
                {
                    Token = new()
                    {
                        CodigoToken = _config.GetValueByKey("newcon_api_token").Result ?? ""
                    }
                }
            };
            var header = new Dictionary<string, string>
            {
                { "SOAPAction", "http://tempuri.org/cnsParcelasInterfaceCobradorReg7" }
            };

            ApiResponse<ElementEnvelopeCobServiceResponse> responseApi =
                await _helper.PostApiXmlServiceNewCon<ElementEnvelopeCobService<ElementConsultaParcelasCobBody>, ElementEnvelopeCobServiceResponse>(urlApi, data, header);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;
            

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }
}