using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.Datacob.NegociacaoCalculoDetalhe;
using TelaUnica.Domain.Dtos.Response.Datacob;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models.Datacob;
using Microsoft.AspNetCore.Http;
using System.Data;
using TelaUnica.Domain.Enums;

namespace TelaUnica.Domain.Interfaces.Datacob;

public interface IDatacobDapperRepository
{
    Task<UserDatacob?> GetUserById(int id, GvcRodobens? ActiveConnection);
    Task<ServiceResponse<List<UserDatacob>>> GetUsers(GvcRodobens? ActiveConnection);
    Task<UserDatacob?> GetUserByUsername(string username, GvcRodobens conn = GvcRodobens.GVC);
    Task<UserDatacob?> GetUserByLogin(string username, GvcRodobens crm = GvcRodobens.GVC);
    Task<ServiceResponse<List<ClientDatacob>>> GetClients(GvcRodobens? ActiveConnection, int? GroupId);
    Task<ServiceResponse<List<StatusDatacob>>> GetStatus(GvcRodobens? ActiveConnection, int? Nr_Agrupamento);
    Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosFinanciados(GvcRodobens ActiveConnection, string? documento, string? nome, string? contrato, string? email, string? telefone, int? clientId, int? groupId, int? aberto, int? agrupamentoId, int? idStatusContrato, int? cyber, string? fase, int? contratoId);
    Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosFinanciadoContrato(GvcRodobens ActiveConnection, int agrupamentoId, int? aberto = 1);
    Task<ServiceResponse<List<GroupDatacob>>> GetGroups(GvcRodobens? ActiveConnection);
    Task<ServiceResponse<DatacobDadosFinanciadoAdjacente>> GetFinanInfos(int IdFinanciado, string? numeroContrato = "");
    Task<ServiceResponse<List<ContratosAtivosDatacob>>> GetContratosAtivos(int IdFinanciado, int IdAgrupamento, int? Ativo, string? statusInstallment = "");
    Task<ServiceResponse<List<DetalhesContratosDatacob>>> GetDetalhesContratos(int IdFinanciado, int IdGrupo);
    Task<ServiceResponse<List<DadosBensDatacob>>> GetDadosBens(int IdContrato);
    Task<ServiceResponse<List<DadosAvalistasDatacob>>> GetDadosAvalistas(int IdContrato);
    Task<ServiceResponse<List<DatacobGrupoContratoAbertoResponse>>> GetGrupoContratoAberto(string Cpfcnpj, int IdAgrupamento);
    Task<ServiceResponse<List<NegociacoesDatacob>>> GetNegociacoes(int IdAgrupamento, int? IdParcela, bool? DetalheParcela, string? numeroContrato = "", int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<NegociacaoParcelasDatacob>>> GetNegociacaoParcelas(int IdNegociacao, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<NegociacoesDetalhesDatacob>>> GetNegociacaoDetalhes(int IdNegociacao);
    Task<ServiceResponse<NegDetalheDatacob>> GetNegociacaoCalculo(int IdNegociacao, GvcRodobens? crm);
    Task<ServiceResponse<List<NegociacaoAcordosDatacob>>> GetNegociacaoAcordos(int IdAgrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<NegociacaoAcordosParcelasDatacob>>> GetNegociacaoAcordosParcelas(int IdAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<ParcelasAcordoBoletoDatacob>>> GetParcelasAcordoBoleto(int IdParcelaAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<ParcelasAcordoReciboDatacob>>> GetParcelasAcordoRecibo(int IdParcelaAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<ParcelasAcordoNegociacaoDatacob>>> GetParcelasAcordoNegociacao(int IdParcelaAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>>> GetParcelasNegociacaoBoleto(int IdNegociacao, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<ParcelasNegociacaoReciboDatacob>>> GetParcelasNegociacaoRecibo(int IdNegociacao);
    Task<ServiceResponse<List<ListaCustasDatacob>>> GetCustas(int IdContrato);
    Task<ServiceResponse<List<HistoricoAtendimentoDatacob>>> GetHistoricoAtendimentoDatacob(int Id_Agrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<NegociacaoParcelasDetalhesDatacob>> GetNegociacaoParcelaDetalhes(int IdParcela, int IdNegociacao);
    Task<ServiceResponse<List<OcorrenciaDatacob>>> GetOcorrencias(int? groupId, int? linkedGroupId);
    Task<ServiceResponse<List<OcorrenciaDatacob>>> GetOcorrenciasCrm(GvcRodobens crm);
    Task<ServiceResponse<List<HistoricoResumoDatacob>>> GetHistoricoResumoDatacob(int Id_Agrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<ParametroCalculo>>> GetParametroCalculo(int Grupo, string? Fase);
    Task<ServiceResponse<List<TipoParcelaDatacob>>> GetTipoParcela();
    Task<ServiceResponse<List<ContratosDatacob>>> GetContratos(int IdAgrupamento);
    Task<ServiceResponse<List<FaseDatacob>>> GetFases();
    Task<ServiceResponse<CalculoAcordoDatacob>> GetAcordoCalculoData(int IdAcordo, GvcRodobens? crm);
    Task<ServiceResponse<List<CidadeDatacob>>> GetCidadeUf();
    Task<ServiceResponse<HistoricoIdDatacob>> GetUltimoHistorico(int ContratoId, string OcorrenciaCod, GvcRodobens? crm = null);
    Task<ServiceResponse<HistoricoIdDatacob>> GetUltimoHistoricoInt(int ContratoId, int OcorrenciaId, GvcRodobens? crm = null);
    Task<ServiceResponse<TelefoneIdDatacob>> GetTelefoneId(string Telefone, int IdAgrupamento);
    Task<ServiceResponse<List<IndicadoresCampanhaDatacob>>> GetIndicadoresCampanha(int IdContrato);
    Task<ServiceResponse<GvcRodobens?>> FindCrm(int IdContrato, string documento);
    Task<ServiceResponse<NegociacaoAbertaRpaDatacob>> GetNegOpenRpa(int IdAgrupamento, int IdUsuario, decimal VlNeg, GvcRodobens crm);
    Task<ServiceResponse<NegociacaoAbertaRpaDatacob>> GetNegOpenCrm(int IdAgrupamento, int IdUsuario, decimal VlNeg, DateTime DtNegociacao);
    Task<ServiceResponse<NegociacaoStatusRpaDatacob>> GetNegById(int IdNegociacao, GvcRodobens crm);
    Task<ServiceResponse<List<IndicadoresOcorrenciaDatacob>>> GetIndicadoresOcorrencia(int IdAgrupamento);
    Task<ServiceResponse<NrNegociacaoDatacob>> GetNrNegociacao(int IdAgrupamento);
    Task<ServiceResponse<List<OutrosContratosDatacob>>> GetOutrosContratos(int IdGrupo, string Documento, GvcRodobens CrmAtual);
    Task<ServiceResponse<List<DatacobBoletosContratoResponse>>> GetBoletosContrato(int IdAgrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<DatacobCheckStatusDnrResponse>> GetCheckStatusContrato(int IdAgrupamento);
    Task<List<DetalhesParcelasDatacob>> GetDetalhesParcelas(List<int> Parcelas);
    Task<ServiceResponse<List<MotivoDevolucaoDatacob>>> GetMotivoDevolucao();
    Task<ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>>> GetListarBoleto(int IdAgrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null);
    Task<ServiceResponse<List<FilterSafraCampaignDatacob>>> FilterSafraCampaign(int daysDelay, int amountInstall, SafraContractType type);
    Task<byte[]> FilterSafraCampaignAndExportToExcel(int daysDelay, int amountInstall, SafraContractType type);

    Task<ServiceResponse<DadosCadastraisDatacob>> BuscaDadosFinanciadoContratoSafra(int contractId);
    Task<ServiceResponse<PrimeiraParcelaSafraDatacob>> PrimeiraParcelaSafra(int contractId);
    Task<ServiceResponse<List<FilterSafraCampaignDatacob>>> FilterSafraCampaignExcel(IFormFile file);
    Task<byte[]> FilterSafraCampaignExcelAndExportToExcel(IFormFile file);
    Task<ServiceResponse<EmailDatacob>> EmailFinanciadoValido(int financedId);
    Task<ServiceResponse<List<EmailDatacob>>> EmailFinanciadoValido(List<int> financedIds);
    Task<ServiceResponse<List<FinanciadoSimplesDatacob>>> FinanciadoSimples(List<int> financedIds);
    Task<ServiceResponse<FinanciadoSimplesDatacob>> FinanciadoSimplesById(GvcRodobens crm, int financiadoId);
    Task<List<ParcelaIdDatacob>> GetIdsParcelas(int IdContrato, List<int> nrParcelas);
    Task<ServiceResponse<List<BoletosUsuariosDatacob>>> BuscaBoletosUsuarioLogado(GvcRodobens crm, DateTime inicio, DateTime fim);
    Task<ServiceResponse<List<BoletosUsuariosDatacob>>> BuscaBoletosUsuarioLogado(GvcRodobens crm, int idUsuario, DateTime inicio, DateTime fim);
    byte[] VisaoBoletosExportToPdf(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data);
    byte[] VisaoBoletosExportToCsv(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data);
    Task<byte[]> VisaoBoletosExportToExcel(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data);
    Task<ServiceResponse<List<ControleUsuarioDatacob>>> GetControleUsuario(int idUsuario, DateTime dtInicio, DateTime dtFim, GvcRodobens crm, int? idGrupo, string? status = null, DateTime? dtVencimento = null, string? nr_Boleto = null);
    Task<ServiceResponse<List<ControleUsuarioDatacob>>> GetControleUsuario(DateTime dtInicio, DateTime dtFim, GvcRodobens crm, int? idGrupo, string? status = null, DateTime? dtVencimento = null, string? nr_Boleto = null);
    Task<byte[]> ControleUsuarioExcel(List<ControleUsuarioDatacob> data);
    Task<ServiceResponse<List<ContratoPadraoDatacob>>> GetListContratoPadrao(GvcRodobens crm, int IdContrato);
    Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosMailing(GvcRodobens crm, string groupId, string? fase, int? dayDueStart, int? dayDueEnd, float? debtValueStart, float? debtValueEnd);

    Task<ServiceResponse<List<DataDashboadContract>>> GetDashboadContracts(GvcRodobens? crm, int? groupId, DateTime? startDate, DateTime? endDate);
    Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosMailingFromFile(IFormFile file, GvcRodobens crm, string groupId, string? fase, int? dayDueStart, int? dayDueEnd, float? debtValueStart, float? debtValueEnd);
    Task<IDbConnection?> GetLinkedConnection(ConfigIntegrationType type, GvcRodobens currentCrm, int currentGroupId, int integratedGroupId, ConfigIntegrationActionType actionType = ConfigIntegrationActionType.Consult);
    Task<GvcRodobens?> GetLinkedCrm(ConfigIntegrationType type, GvcRodobens currentCrm, int currentGroupId, int integratedGroupId, ConfigIntegrationActionType actionType = ConfigIntegrationActionType.Insert);
    Task<ServiceResponse<InfoContratoSimplesDatacob>> SimpleInfoByContratctId(GvcRodobens crm, int contratoId);
}
