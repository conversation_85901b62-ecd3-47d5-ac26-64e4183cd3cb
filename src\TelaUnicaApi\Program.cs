global using Microsoft.EntityFrameworkCore;
global using TelaUnica.Infra.Data.EF.Models;
global using TelaUnica.Infra.Data.EF.Data;
global using AutoMapper;
using Hangfire;
using Hangfire.Console;
using Hangfire.SqlServer;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Swashbuckle.AspNetCore.Filters;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.Authorization;
using TelaUnica.Api.Policies;
using TelaUnica.Domain.Utils;
using TelaUnica.Domain.Interfaces;
using TelaUnica.Domain.Repository;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Interfaces.Mktzap;
using TelaUnica.Domain.Repository.Datacob;
using TelaUnica.Domain.Repository.Mktzap;
using TelaUnica.Domain.Repository.Tactium;
using TelaUnica.Domain.Interfaces.Newcon;
using TelaUnica.Domain.Interfaces.Tactium;
using TelaUnica.Domain.Repository.Newcon;
using TelaUnica.Infra.Projuris.Interfaces.Desdobramentos;
using TelaUnica.Infra.Projuris.Repositories.Desdobramentos;
using TelaUnica.Infra.Projuris.Interfaces.Processos;
using TelaUnica.Infra.Projuris.Repositories.Processos;
using TelaUnica.Infra.Projuris.Repositories.Custas;
using TelaUnica.Infra.Projuris.Interfaces.Custas;
using TelaUnica.Infra.Projuris.Interfaces.Documentos;
using TelaUnica.Infra.Projuris.Repositories.Eventos;
using TelaUnica.Infra.Projuris.Interfaces.Eventos;
using TelaUnica.Infra.ManagerRPA.Interfaces;
using TelaUnica.Infra.ManagerRPA.Repositories;
using AppCyberSafraConsultaSaldo = TelaUnica.Domain.Application.UsesCases.CyberSafra.Acordo.ConsultaSaldo;
using AppCyberSafraConsultarElegibilidade = TelaUnica.Domain.Application.UsesCases.CyberSafra.Acordo.ConsultarElegibilidade;
using AppCyberSafraSimular = TelaUnica.Domain.Application.UsesCases.CyberSafra.Acordo.Simular;
using AppCyberSafraCadastrar = TelaUnica.Domain.Application.UsesCases.CyberSafra.Acordo.Cadastrar;
using AppCyberSafraCancelar = TelaUnica.Domain.Application.UsesCases.CyberSafra.Acordo.Cancelar;
using AppCyberSafraBoleto = TelaUnica.Domain.Application.UsesCases.CyberSafra.Boleto.GerarBoleto;
using AppCyberSafraObterBoleto = TelaUnica.Domain.Application.UsesCases.CyberSafra.Boleto.ObterBase64Boleto;
using AppCyberSafraEmail = TelaUnica.Domain.Application.UsesCases.CyberSafra.Email.Enviar;
using AppSafraFunnel = TelaUnica.Domain.Application.UsesCases.Safra.Funnel;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Financed.Create;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Financed.Get;
using TelaUnica.Domain.Repository.Negotiation.Financed;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Contracts.Get;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Contracts.Create;
using TelaUnica.Domain.Repository.Negotiation.Contracts;
using TelaUnica.Domain.Repository.Negotiation.Agreements;
using TelaUnica.Domain.Application.UsesCases.Negotiation.AgreementTickets.Delete;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Agreements.Get;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Agreements.Process;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Agreements.Integration;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Agreements.Create;
using TelaUnica.Domain.Application.UsesCases.Negotiation.Agreements.Update;
using Telephony = TelaUnica.Domain.Application.UsesCases.Telephony;
using TelaUnica.Domain.Application.UsesCases.Negotiation.AgreementInstallments.Update;
using TelaUnica.Domain.Application.UsesCases.GVCManager.CyberSafra.CreateActions;
using System.Globalization;
using Newcon = TelaUnica.Domain.Application.UsesCases.Newcon;
using TelaUnica.Domain.Repository.Safra.Funnel;
using GroupCalcDatacob = TelaUnica.Domain.Application.UsesCases.GroupCalculationDatacob;
using TelaUnica.Domain.Repository.Newcon.CustomerServiceMainData;
using TelaUnica.Domain.Repository.Newcon.EvolucaoDosPrecos;
using TelaUnica.Domain.Repository.Newcon.PriceEvolution;
using TelaUnica.Domain.Repository.Tactium.PauseTactium;
using PM = TelaUnica.Domain.Application.UsesCases.Telephony.PauseTactium;
using Support = TelaUnica.Domain.Application.UsesCases.Support;
using SupportRepository = TelaUnica.Domain.Repository.Support;
using NewconAPIRepository = TelaUnica.Domain.Repository.Newcon.NewconAPI;
using NewconAPI = TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI;
using NewconAccessRep = TelaUnica.Domain.Repository.Newcon.NewconAccess;
using DatacobInvoicesRep = TelaUnica.Domain.Repository.Datacob.DatacobInvoices;
using System.Text.Json.Serialization;
using Barramento = TelaUnica.Domain.Application.UsesCases.Barramento;
using TelaUnica.Domain.Repository.Service;
using DatacobOccurrenceRep = TelaUnica.Domain.Repository.Datacob.DatacobOccurrence;
using DatacobPhoneCallRep = TelaUnica.Domain.Repository.Datacob.DatacobPhoneCall;
using TelaUnica.Domain.Factorys.Datacob;
using CalculationsParametersTelaUnica = TelaUnica.Domain.Repository.TelaUnica.CalculationsParameters;
using TelaUnica.Domain.Application.UsesCases.CalculationParameter.Get;
using CalculationsParametersDatacob = TelaUnica.Domain.Repository.Datacob.CalculationsParameters;
using MktzapCompany = TelaUnica.Domain.Repository.Mktzap.MktzapCompany;
using BoletoEmailDatacob = TelaUnica.Domain.Application.UsesCases.BoletoEmailDatacob;
using TelaUnica.Domain.Application.UsesCases.GVCManager.SaveOccurrence.List;
using TelaUnica.Domain.Application.UsesCases.GVCManager.SaveOccurrence.Insert;
using RpaOccurrenceTriggers = TelaUnica.Domain.Repository.TelaUnica.RpaOccurrenceTriggers;
using RpaOccurrenceTriggersUC = TelaUnica.Domain.Application.UsesCases.RpaOccurrenceTriggers;
using TelaUnica.Domain.Application.UsesCases.GVCManager.SaveOccurrence.InsertLast;
using TransferCampaignRepo = TelaUnica.Domain.Repository.Olos.TransferCampaign;
using TransferCampaignUC = TelaUnica.Domain.Application.UsesCases.TransferCampaign;
using TelaUnica.Domain.Application.UsesCases.GVCManager.SaveOccurrence.Resend;
using AgentCallRepo = TelaUnica.Domain.Repository.TelaUnica.AgentCall;
using AgentCall = TelaUnica.Domain.Application.UsesCases.AgentCall;
using BTGTicketRepo = TelaUnica.Domain.Repository.TelaUnica.TicketBTG;
using BTGRepo = TelaUnica.Domain.Repository.BTG;
using BTGCodParamRepo = TelaUnica.Domain.Repository.TelaUnica.CodParamBTG;
using BTGTicket = TelaUnica.Domain.Application.UsesCases.BTG.Ticket;
using UserCrmRepo = TelaUnica.Domain.Repository.TelaUnica.UserCrm;
using UserCrm = TelaUnica.Domain.Application.UsesCases.UserCrm;
using TelaUnica.Domain.Repository.Negotiation.TradingRestrictions;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TradingRestriction.Create;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TradingRestriction.Get;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TradingRestriction.Delete;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TradingRestriction.Update;
using DcSaveNegRepo = TelaUnica.Domain.Repository.TelaUnica.DatacobSaveNegotiation;
using DcSaveNeg = TelaUnica.Domain.Application.UsesCases.DatacobSaveNegotiation;
using DcJokerTicketRepo = TelaUnica.Domain.Repository.TelaUnica.DatacobJokerTicket;
using DcJokerTicket = TelaUnica.Domain.Application.UsesCases.DatacobJokerTicket;
using CDCustas = TelaUnica.Domain.Application.UsesCases.CustasDatacob;
using BGProjurisRepo = TelaUnica.Domain.Repository.Projuris.BaseGeral;
using BGProjuris = TelaUnica.Domain.Application.UsesCases.Projuris.BaseGeral;
using DCProjurisRepo = TelaUnica.Domain.Repository.Projuris.DesdobramentoClone;
using DCProjuris = TelaUnica.Domain.Application.UsesCases.Projuris.DesdobramentoClone;
using CTProjurisRepo = TelaUnica.Domain.Repository.Projuris.Custa;
using CTProjuris = TelaUnica.Domain.Application.UsesCases.Projuris.Custa;
using DMProjurisRepo = TelaUnica.Domain.Repository.Projuris.Documento;
using DMProjuris = TelaUnica.Domain.Application.UsesCases.Projuris.Documento;
using EVProjurisRepo = TelaUnica.Domain.Repository.Projuris.Evento;
using EVProjuris = TelaUnica.Domain.Application.UsesCases.Projuris.Evento;
using LogErrorAppRepo = TelaUnica.Domain.Repository.TelaUnica.LogErrorApp;
using LogErrorApp = TelaUnica.Domain.Application.UsesCases.LogErrorApp;
using TelaUnica.Api.Middleware.LogApiMiddleware;
using LogApiRepo = TelaUnica.Domain.Repository.TelaUnica.LogApi;
using LogApi = TelaUnica.Domain.Application.UsesCases.LogApi;
using MissedOccurrenceRepo = TelaUnica.Domain.Repository.TelaUnica.MissedOccurrence;
using MissedOccurrence = TelaUnica.Domain.Application.UsesCases.MissedOccurrence;
using CorJuridicoParamRepo = TelaUnica.Domain.Repository.TelaUnica.CorJuridicoParam;
using CorJuridicoParam = TelaUnica.Domain.Application.UsesCases.CorJuridicoParam;
using TelaUnica.Domain.Application.UsesCases.Newcon.SendSimulationEmail;
using TelaUnica.Infra.Projuris.Interfaces.Entidades;
using TelaUnica.Infra.Projuris.Repositories.Entidades;
using SafraCampaignPermissionsRepo = TelaUnica.Domain.Repository.TelaUnica.SafraCampaignPermissions;
using SafraCampaignPermissions = TelaUnica.Domain.Application.UsesCases.Safra.Campaign.Permissions;
using SafraCampaignRepo = TelaUnica.Domain.Repository.TelaUnica.SafraCampaign;
using SafraCampaign = TelaUnica.Domain.Application.UsesCases.Safra.Campaign;
using BbcAuthRepo = TelaUnica.Domain.Repository.TelaUnica.BBC.BbcAuth;
using NotifRepo = TelaUnica.Domain.Repository.TelaUnica.Notification;
using Notif = TelaUnica.Domain.Application.UsesCases.Notification;
using BbcProductsRepo = TelaUnica.Domain.Repository.TelaUnica.BBC.BbcProducts;
using BbcProducts = TelaUnica.Domain.Application.UsesCases.BBC.BbcProducts;
using BbcConfigsRepo = TelaUnica.Domain.Repository.TelaUnica.BBC.BbcConfigs;
using BbcUC = TelaUnica.Domain.Application.UsesCases.BBC;
using Agreement = TelaUnica.Domain.Repository.TelaUnica.Agreement;
using BbcInstallmentParamRepo = TelaUnica.Domain.Repository.TelaUnica.BbcInstallmentParam;
using BbcInstallmentParam = TelaUnica.Domain.Application.UsesCases.BbcInstallmentParam;
using BbcDiscountParamRepo = TelaUnica.Domain.Repository.TelaUnica.BbcDiscountParam;
using BbcDiscountParam = TelaUnica.Domain.Application.UsesCases.BbcDiscountParam;
using UserControlTicket = TelaUnica.Domain.Application.UsesCases.UserControlTicket;
using UserControlTicketRepo = TelaUnica.Domain.Repository.TelaUnica.UserControlTicket;
using Microsoft.AspNetCore.Http.Features;
using EmailOccurrence = TelaUnica.Domain.Application.UsesCases.EmailOccurrence;
using AdditionEntrySimulationRepo = TelaUnica.Domain.Repository.Simulation.AdditionEntry;
using AdditionEntrySimulation = TelaUnica.Domain.Application.UsesCases.Simulation.AdditionEntry;
using DilutionEntrySimulationRepo = TelaUnica.Domain.Repository.Simulation.DilutionEntry;
using DilutionEntrySimulation = TelaUnica.Domain.Application.UsesCases.Simulation.DilutionEntry;

using CustomerHistoryRepo = TelaUnica.Domain.Repository.TelaUnica.CustomerHistory;
using CustomerHistory = TelaUnica.Domain.Application.UsesCases.CustomerHistory;
using OccurrenceRulesRepo = TelaUnica.Domain.Repository.TelaUnica.OccurrenceRules;
using OccurrenceRules = TelaUnica.Domain.Application.UsesCases.OccurrenceRules;
using TelaUnica.Api.Middleware.Services;
using TelaUnica.Api.Middleware;
using TelaUnica.Domain.Interfaces.Services;
using TelaUnica.Domain.Application.UsesCases.SendEmail.JobSendEmail;
using TelaUnica.Domain.Repository.Services;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TUNegociacaoCalculoLivre.Create;
using TelaUnica.Domain.Repository.Negotiation.TUNegociacaoCalculoLivre;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TUNegociacaoCalculoLivre.Get;
using TelaUnica.Domain.Application.UsesCases.Negotiation.TUNegociacaoCalculoLivre.Update;

using AcordoManualUC = TelaUnica.Domain.Application.UsesCases.AcordoManual;


using PrestadoresServicosUC = TelaUnica.Domain.Application.UsesCases.PrestadoresServicos;
using PrestadoresServicosRepo = TelaUnica.Domain.Repository.TelaUnica.PrestadoresServicos;
using TelaUnica.Domain.Repository.CartasETermos;
using UCFilaGet = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Get;
using UCFilaUpdate = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao;
using UCFilaCartasETemos = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao;
using TipoTermosRepo = TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;
using UCTipoTermos = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo;
using ConteudoTermosRepo = TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using UCConteudoTermos = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo;
using InfosTermosRepo = TelaUnica.Domain.Repository.CartasETermos.Termos.PedidoInfos;
using UCInfosTermos = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos;

namespace TelaUnica.Api;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        //Add factories to the container.
        //defines which repository implementation to use from datacobs
        builder.Services.AddScoped<IDatacobFactory, DatacobFactory>();
        builder.Services.AddScoped<CalculationsParametersDatacob.CalculationsParametersDatacobGvcRepository>();
        builder.Services.AddScoped<CalculationsParametersDatacob.CalculationsParametersDatacobRodobensRepository>();

        // Add services to the container.
        builder.Services.AddDbContext<DataContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"), b => b.MigrationsAssembly("TelaUnica.Api")));
        builder.Services.AddDbContext<DcRodobensContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("DcRodobensConnection")));
        builder.Services.AddDbContext<DcGvcContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("DcGvcConnection")));
        builder.Services.AddDbContext<MktzapStageContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("MktzapStageConnection")));
        builder.Services.AddDbContext<ProjurisStageContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("ProjurisStageConnection")));
        builder.Services.AddDbContext<NewconContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("NewconConnection"), serverOptionsAction => serverOptionsAction.CommandTimeout(600)));
        builder.Services.AddDbContext<OlosStageContext>(options => options.UseSqlServer(builder.Configuration.GetConnectionString("OlosConnection")));

        var MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        builder.Services.AddCors(options =>
        {
            options.AddPolicy(name: MyAllowSpecificOrigins,
                              policy =>
                              {
                                  policy.WithOrigins("*");
                                  policy.WithMethods("POST", "GET", "PUT", "DELETE");
                                  policy.WithHeaders("*");
                              });
        });

        builder.Services.AddControllers().AddJsonOptions(o => o.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()));
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();

        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "Tela Unica API", Version = "v1" });

            c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
            {
                Description = """Padrão de autorização utilizando Bearer. Exemplo: "bearer {token}" """,
                In = ParameterLocation.Header,
                Name = "Authorization",
                Type = SecuritySchemeType.ApiKey
            });
            c.OperationFilter<SecurityRequirementsOperationFilter>();
            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    new List<string>()
                }
            });
        });


        CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("pt-BR");
        CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo("pt-BR");

        //builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
        builder.Services.AddAutoMapper(typeof(Program).Assembly);

        builder.Services.AddScoped<IDatacobDapperRepository, DatacobDapperRepository>();

        builder.Services.AddScoped<IAuthRepository, AuthRepository>();
        builder.Services.AddScoped<IRoleRepository, RoleRepository>();
        builder.Services.AddScoped<IModuleRepository, ModuleRepository>();
        builder.Services.AddScoped<IUserRepository, UserRepository>();
        builder.Services.AddScoped<IConfigRepository, ConfigRepository>();
        builder.Services.AddScoped<IConfigWebsocketRepository, ConfigWebsocketRepository>();
        builder.Services.AddScoped<IDatacobRepository, DatacobRepository>();
        builder.Services.AddScoped<IGroupRepository, GroupRepository>();
        builder.Services.AddScoped<ISubModuleRepository, SubModuleRepository>();
        builder.Services.AddScoped<ILogIntegracaoRepository, LogIntegracaoRepository>();
        builder.Services.AddScoped<IMktzapRepository, MktzapRepository>();
        builder.Services.AddScoped<IMktzapApiRepository, MktzapApiRepository>();
        builder.Services.AddScoped<INewconRepository, NewconRepository>();
        builder.Services.AddScoped<ITactiumRepository, TactiumRepository>();
        builder.Services.AddScoped<ITactiumTokenRepository, TactiumTokenRepository>();
        builder.Services.AddScoped<MapperComplements>();
        builder.Services.AddScoped<ApiHelpers>();
        builder.Services.AddScoped<DatacobHelpers>();
        builder.Services.AddScoped<SqlDatacobHelpers>();
        builder.Services.AddScoped<SqlMktzapHelpers>();
        builder.Services.AddScoped<NewconHelpers>();
        builder.Services.AddScoped<TactiumHelpers>();
        builder.Services.AddScoped<NewconSqlHelpers>();
        builder.Services.AddSingleton<IAuthorizationHandler, AdminHandler>();
        builder.Services.AddScoped<IDatacobApiRepository, DatacobApiRepository>();
        builder.Services.AddScoped<INewconCalcRepository, NewconCalcRepository>();
        builder.Services.AddScoped<IAccumulatedContractBalancesRepository, AccumulatedContractBalancesRepository>();
        builder.Services.AddScoped<ISalesStructureRepository, SalesStructureRepository>();
        builder.Services.AddScoped<INewconMainScreenRepository, NewconMainScreenRepository>();
        builder.Services.AddScoped<IPriceGoodRepository, PriceGoodRepository>();
        builder.Services.AddScoped<IScheduleRespository, ScheduleRespository>();
        builder.Services.AddScoped<IPositionConsortiumRepository, PositionConsortiumRepository>();
        builder.Services.AddScoped<ICallCampaignRepository, CallCampaignRepository>();
        builder.Services.AddScoped<ITelephonyWebsocketRepository, TelephonyWebsocketRepository>();
        builder.Services.AddScoped<IServiceRepository, ServiceRepository>();
        // Projuris
        builder.Services.AddScoped<IDesdobramentoRepository, DesdobramentoRepository>();
        builder.Services.AddScoped<IProcessosRepository, ProcessosRepository>();
        builder.Services.AddScoped<ICustasRepository, CustasRepository>();
        builder.Services.AddScoped<IDocumentosRepository, DocumentosRepository>();
        builder.Services.AddScoped<IEventosRepository, EventosRepository>();
        builder.Services.AddScoped<IEntidadesRepository, EntidadesRepository>();
        builder.Services.AddScoped<ITelephonyRepository, TelephonyRepository>();
        builder.Services.AddScoped<IManagerApiRepository, ManagerApiRepository>();

        //Cyber Safra
        builder.Services.AddScoped<AppCyberSafraConsultaSaldo.IConsultaSaldo, AppCyberSafraConsultaSaldo.ConsultaSaldo>();
        builder.Services.AddScoped<AppCyberSafraConsultarElegibilidade.IConsultarElegibilidade, AppCyberSafraConsultarElegibilidade.ConsultarElegibilidade>();
        builder.Services.AddScoped<AppCyberSafraSimular.ISimularAcordo, AppCyberSafraSimular.SimularAcordo>();
        builder.Services.AddScoped<AppCyberSafraCadastrar.ICadastrarAcordo, AppCyberSafraCadastrar.CadastrarAcordo>();
        builder.Services.AddScoped<AppCyberSafraCancelar.ICancelarAcordo, AppCyberSafraCancelar.CancelarAcordo>();
        builder.Services.AddScoped<AppCyberSafraBoleto.IGerarBoleto, AppCyberSafraBoleto.GerarBoleto>();
        builder.Services.AddScoped<AppCyberSafraObterBoleto.IObterBase64Boleto, AppCyberSafraObterBoleto.ObterBase64Boleto>();
        builder.Services.AddScoped<AppCyberSafraEmail.IEnviar, AppCyberSafraEmail.Enviar>();

        //Safra
        builder.Services.AddScoped<IFunnelSafraRepository, FunnelSafraRepository>();
        builder.Services.AddScoped<AppSafraFunnel.Create.ICreateFunnelSafra, AppSafraFunnel.Create.CreateFunnelSafra>();


        //Negociação
        builder.Services.AddScoped<IFinancedRepository, FinancedRepository>();
        builder.Services.AddScoped<IFinancedGetByID, FinancedGetById>();
        builder.Services.AddScoped<IFinancedGetByCpf, FinancedGetByCpf>();
        builder.Services.AddScoped<IFinancedCreate, FinancedCreate>();

        builder.Services.AddScoped<IContractsRepository, ContractsRepository>();
        builder.Services.AddScoped<IContractsGetById, ContractsGetById>();
        builder.Services.AddScoped<IContractsGetByContratcId, ContractsGetByContratcId>();
        builder.Services.AddScoped<IContratoCreate, ContratoCreate>();

        builder.Services.AddScoped<IAgreementTicketRepository, AgreementTicketRepository>();
        builder.Services.AddScoped<IAgreementTicketsDelete, AgreementTicketsDelete>();
        builder.Services.AddScoped<IAgreementRepository, AgreementRepository>();
        builder.Services.AddScoped<IAgreementsGetById, AgreementsGetById>();
        builder.Services.AddScoped<IAgreementsGetByContratcId, AgreementsGetByContratcId>();
        builder.Services.AddScoped<IAgreementsProcess, AgreementsProcess>();
        builder.Services.AddScoped<IAgreementsIntegration, AgreementsIntegration>();
        builder.Services.AddScoped<IAgreementsCreate, AgreementsCreate>();
        builder.Services.AddScoped<IAgreementsGetListByCpfCnpj, AgreementsGetListByCpfCnpj>();
        builder.Services.AddScoped<IAgreementsUpdateStatus, AgreementsUpdateStatus>();
        builder.Services.AddScoped<IAgreementsUpdateStatusDataCob, AgreementsUpdateStatusDataCob>();
        builder.Services.AddScoped<IAgreementsUpdateMotivo, AgreementsUpdateMotivo>();
        builder.Services.AddScoped<IUpdateIdAgreementsDataCob, UpdateIdAgreementsDataCob>();
        builder.Services.AddScoped<IAgreementInstallmentsUpdateStatusDataCob, AgreementInstallmentsUpdateStatusDataCob>();
        builder.Services.AddScoped<IAgreementInstallmentRepository, AgreementInstallmentRepository>();
        builder.Services.AddScoped<ITUNegociacaoCalculoLivreCreate, TUNegociacaoCalculoLivreCreate>();
        builder.Services.AddScoped<ITUNegociacaoCalculoLivreGetListPending, TUNegociacaoCalculoLivreGetListPending>();
        builder.Services.AddScoped<ITUNegociacaoCalculoLivreUpdateStatus, TUNegociacaoCalculoLivreUpdateStatus>();
        builder.Services.AddScoped<ITUNegociacaoCalculoLivreGetListByContract, TUNegociacaoCalculoLivreGetListByContract>();

        builder.Services.AddScoped<ITUNegociacaoCalculoLivreRepository, TUNegociacaoCalculoLivreRepository>();

        /*Custas Datacob*/
        /*Use Cases*/
        builder.Services.AddScoped<CDCustas.ListarCustas.IListarCustas, CDCustas.ListarCustas.ListarCustas>();
        builder.Services.AddScoped<CDCustas.InserirCustas.IInserirCustas, CDCustas.InserirCustas.InserirCustas>();
        builder.Services.AddScoped<CDCustas.ListPending.IListPending, CDCustas.ListPending.ListPending>();
        builder.Services.AddScoped<CDCustas.ChangeStatus.IChangeStatus, CDCustas.ChangeStatus.ChangeStatus>();


        builder.Services.AddScoped<IRestrictionCreate, RestrictionCreate>();
        builder.Services.AddScoped<IRestrictionGetList, RestrictionGetList>();
        builder.Services.AddScoped<IRestrictionDelete, RestrictionDelete>();
        builder.Services.AddScoped<IRestrictionsRepository, RestrictionsRepository>();
        builder.Services.AddScoped<IRestrictionAnalysisRequestGetList, RestrictionAnalysisRequestGetList>();
        builder.Services.AddScoped<IRestrictionAnalysisRequestCreate, RestrictionAnalysisRequestCreate>();
        builder.Services.AddScoped<IRestrictionAnalysisRequestPendingGet, RestrictionAnalysisRequestPendingGet>();
        builder.Services.AddScoped<IRestrictionAnalysisRequestUpdateStatus, RestrictionAnalysisRequestUpdateStatus>();
        builder.Services.AddScoped<IRestrictionAnalysisRequestRepository, RestrictionAnalysisRequestRepository>();
        builder.Services.AddScoped<IRestrictionAnalysisRequestGet, RestrictionAnalysisRequestGet>();

        //GVCManager
        builder.Services.AddScoped<IAgreenetsActions, AgreenetsActions>();
        builder.Services.AddScoped<IListSaveOccurrence, ListSaveOccurrence>();
        builder.Services.AddScoped<IInsertSaveOccurrence, InsertSaveOccurrence>();
        builder.Services.AddScoped<IInsertLast, InsertLast>();
        builder.Services.AddScoped<IResendOccurrence, ResendOccurrence>();


        //Telefonia
        builder.Services.AddScoped<Telephony.Autenticar.IAutenticar, Telephony.Autenticar.Autenticar>();
        builder.Services.AddScoped<Telephony.Pause.Listar.IListarPausa, Telephony.Pause.Listar.ListarPausa>();
        builder.Services.AddScoped<Telephony.Pause.Acionar.IAcionarPausa, Telephony.Pause.Acionar.AcionarPausa>();
        builder.Services.AddScoped<Telephony.Ligacao.Acionar.IAcionarLigacao, Telephony.Ligacao.Acionar.AcionarLigacao>();
        builder.Services.AddScoped<Telephony.Ligacao.Desligar.IDesligarLigacao, Telephony.Ligacao.Desligar.DesligarLigacao>();
        builder.Services.AddScoped<Telephony.Ligacao.Confirmar.IConfirmarLigacao, Telephony.Ligacao.Confirmar.ConfirmarLigacao>();
        builder.Services.AddScoped<Telephony.Tabulacao.Listar.IListarTabulacao, Telephony.Tabulacao.Listar.ListarTabulacao>();
        builder.Services.AddScoped<Telephony.Tabulacao.Acionar.IAcionarTabulacao, Telephony.Tabulacao.Acionar.AcionarTabulacao>();
        builder.Services.AddScoped<Telephony.Pause.Sair.ISairPausa, Telephony.Pause.Sair.SairPausa>();
        builder.Services.AddScoped<Telephony.Deslogar.IDeslogar, Telephony.Deslogar.Deslogar>();
        builder.Services.AddScoped<Telephony.Manual.Sair.ISairManual, Telephony.Manual.Sair.SairManual>();
        builder.Services.AddScoped<Telephony.Ligacao.Transferir.ITransferirLigacao, Telephony.Ligacao.Transferir.TransferirLigacao>();
        builder.Services.AddScoped<Telephony.Pause.LigarPausado.ILigarPausado, Telephony.Pause.LigarPausado.LigarPausado>();

        builder.Services.AddScoped<IPauseTactiumRepository, PauseTactiumRepository>();
        builder.Services.AddScoped<PM.List.IList, PM.List.List>();
        builder.Services.AddScoped<PM.Create.ICreate, PM.Create.Create>();
        builder.Services.AddScoped<PM.Update.IUpdate, PM.Update.Update>();
        builder.Services.AddScoped<PM.Delete.IDelete, PM.Delete.Delete>();


        //Newcon
        builder.Services.AddScoped<Newcon.Commercial.IStructure, Newcon.Commercial.Structure>();
        builder.Services.AddScoped<Newcon.Balance.IAccumulated, Newcon.Balance.Accumulated>();
        builder.Services.AddScoped<Newcon.DebitAccount.IDebitAccount, Newcon.DebitAccount.DebitAccount>();
        builder.Services.AddScoped<Newcon.ValuesToBeReturned.IValuesReturned, Newcon.ValuesToBeReturned.ValuesReturned>();
        builder.Services.AddScoped<Newcon.BillingPlan.IBillingPlan, Newcon.BillingPlan.BillingPlan>();
        builder.Services.AddScoped<Newcon.Schedule.Protocol.IByProtocol, Newcon.Schedule.Protocol.ByProtocol>();
        builder.Services.AddScoped<Newcon.Assets.Guarantee.IGuarantee, Newcon.Assets.Guarantee.Guarantee>();
        builder.Services.AddScoped<Newcon.Assets.Contract.IContract, Newcon.Assets.Contract.Contract>();
        builder.Services.AddScoped<Newcon.Delay.IDelay, Newcon.Delay.Delay>();
        builder.Services.AddScoped<Newcon.Partners.IPartners, Newcon.Partners.Partners>();
        builder.Services.AddScoped<Newcon.Commercial.Details.IDetails, Newcon.Commercial.Details.Details>();
        builder.Services.AddScoped<Newcon.ValuesToBeReturned.Details.IDetails, Newcon.ValuesToBeReturned.Details.Details>();
        builder.Services.AddScoped<Newcon.PaidValues.IPaidValues, Newcon.PaidValues.PaidValues>();
        builder.Services.AddScoped<Newcon.Schedule.ISchedule, Newcon.Schedule.Schedule>();
        builder.Services.AddScoped<Newcon.SalesPoint.ISalesPoint, Newcon.SalesPoint.SalesPoint>();
        builder.Services.AddScoped<Newcon.SalesTeam.ISalesTeam, Newcon.SalesTeam.SalesTeam>();

        builder.Services.AddScoped<Newcon.CustomerServiceMainData.ICustomerServiceMainData, Newcon.CustomerServiceMainData.CustomerServiceMainData>();
        builder.Services.AddScoped<ICustomerServiceMainDataRepository, CustomerServiceMainDataRepository>();
        builder.Services.AddScoped<Newcon.PriceEvolution.IPriceEvolution, Newcon.PriceEvolution.PriceEvolution>();
        builder.Services.AddScoped<IPriceEvolutionRepository, PriceEvolutionRepository>();

        builder.Services.AddScoped<ISendSimulationEmail, SendSimulationEmail>();

        //GroupCalculationDatacob
        builder.Services.AddScoped<GroupCalcDatacob.Create.ICreate, GroupCalcDatacob.Create.Create>();
        builder.Services.AddScoped<GroupCalcDatacob.List.IList, GroupCalcDatacob.List.List>();
        builder.Services.AddScoped<GroupCalcDatacob.Delete.IDelete, GroupCalcDatacob.Delete.Delete>();
        builder.Services.AddScoped<GroupCalcDatacob.Update.IUpdate, GroupCalcDatacob.Update.Update>();

        //Support
        //Repository
        builder.Services.AddScoped<SupportRepository.Department.IDepartmentRepository, SupportRepository.Department.DepartmentRepository>();
        //UseCases
        builder.Services.AddScoped<Support.SendEmail.ISendEmail, Support.SendEmail.SendEmail>();
        builder.Services.AddScoped<Support.Department.Create.ICreateDepartment, Support.Department.Create.CreateDepartment>();
        builder.Services.AddScoped<Support.Department.Delete.IDeleteDepartment, Support.Department.Delete.DeleteDepartment>();
        builder.Services.AddScoped<Support.Department.GetByDescription.IGetByDescriptionDepartment, Support.Department.GetByDescription.GetByDescriptionDepartment>();
        builder.Services.AddScoped<Support.Department.GetById.IGetByIdDepartment, Support.Department.GetById.GetByIdDepartment>();
        builder.Services.AddScoped<Support.Department.List.IListDepartment, Support.Department.List.ListDepartment>();
        builder.Services.AddScoped<Support.Department.Update.IUpdateDepartment, Support.Department.Update.UpdateDepartment>();

        //NewconAPI
        //Repository
        builder.Services.AddScoped<NewconAPIRepository.INewconAPIRepository, NewconAPIRepository.NewconAPIRepository>();
        //UseCases
        builder.Services.AddScoped<NewconAPI.Create.ICreateNewconAPI, NewconAPI.Create.CreateNewconAPI>();
        builder.Services.AddScoped<NewconAPI.Delete.IDeleteNewconAPI, NewconAPI.Delete.DeleteNewconAPI>();
        builder.Services.AddScoped<NewconAPI.GetByName.IGetByNameNewconAPI, NewconAPI.GetByName.GetByNameNewconAPI>();
        builder.Services.AddScoped<NewconAPI.GetById.IGetByIdNewconAPI, NewconAPI.GetById.GetByIdNewconAPI>();
        builder.Services.AddScoped<NewconAPI.List.IListNewconAPI, NewconAPI.List.ListNewconAPI>();
        builder.Services.AddScoped<NewconAPI.Update.IUpdateNewconAPI, NewconAPI.Update.UpdateNewconAPI>();

        builder.Services.AddScoped<NewconAccessRep.INewconAccessRepository, NewconAccessRep.NewconAccessRepository>();
        builder.Services.AddScoped<DatacobInvoicesRep.IDatacobInvoicesRepository, DatacobInvoicesRep.DatacobInvoicesRepository>();
        builder.Services.AddScoped<DatacobOccurrenceRep.IDatacobOccurrenceRepository, DatacobOccurrenceRep.DatacobOccurrenceRepository>();
        builder.Services.AddScoped<DatacobPhoneCallRep.IDatacobPhoneCallRepository, DatacobPhoneCallRep.DatacobPhoneCallRepository>();

        //RpaOccurrenceTriggers
        builder.Services.AddScoped<RpaOccurrenceTriggers.IRpaOccurrenceTriggersRepository, RpaOccurrenceTriggers.RpaOccurrenceTriggersRepository>();
        //UseCases
        builder.Services.AddScoped<RpaOccurrenceTriggersUC.Create.ICreate, RpaOccurrenceTriggersUC.Create.Create>();
        builder.Services.AddScoped<RpaOccurrenceTriggersUC.Update.IUpdate, RpaOccurrenceTriggersUC.Update.Update>();
        builder.Services.AddScoped<RpaOccurrenceTriggersUC.Delete.IDelete, RpaOccurrenceTriggersUC.Delete.Delete>();
        builder.Services.AddScoped<RpaOccurrenceTriggersUC.List.IList, RpaOccurrenceTriggersUC.List.List>();
        builder.Services.AddScoped<RpaOccurrenceTriggersUC.ListByCrm.IListByCrm, RpaOccurrenceTriggersUC.ListByCrm.ListByCrm>();

        //TransferCampaign
        builder.Services.AddScoped<TransferCampaignRepo.ITransferCampaignRepository, TransferCampaignRepo.TransferCampaignRepository>();
        //UseCases
        builder.Services.AddScoped<TransferCampaignUC.Create.ICreate, TransferCampaignUC.Create.Create>();
        builder.Services.AddScoped<TransferCampaignUC.Update.IUpdate, TransferCampaignUC.Update.Update>();
        builder.Services.AddScoped<TransferCampaignUC.Delete.IDelete, TransferCampaignUC.Delete.Delete>();
        builder.Services.AddScoped<TransferCampaignUC.List.IList, TransferCampaignUC.List.List>();
        builder.Services.AddScoped<TransferCampaignUC.ListByCrm.IListByCrm, TransferCampaignUC.ListByCrm.ListByCrm>();
        builder.Services.AddScoped<TransferCampaignUC.GetByClient.IGetByClient, TransferCampaignUC.GetByClient.GetByClient>();

        //AgentCall
        builder.Services.AddScoped<AgentCallRepo.IAgentCallRepository, AgentCallRepo.AgentCallRepository>();
        //UseCases
        builder.Services.AddScoped<AgentCall.ListByGrouping.IListByGrouping, AgentCall.ListByGrouping.ListByGrouping>();
        builder.Services.AddScoped<AgentCall.ListByUser.IListByUser, AgentCall.ListByUser.ListByUser>();

        builder.Services.AddScoped<AgentCall.ListAll.IListAll, AgentCall.ListAll.ListAll>();

        //BTG
        builder.Services.AddScoped<BTGTicketRepo.ITicketBTGRepository, BTGTicketRepo.TicketBTGRepository>();
        builder.Services.AddScoped<BTGCodParamRepo.ICodParamBTGRepository, BTGCodParamRepo.CodParamBTGRepository>();
        builder.Services.AddScoped<BTGRepo.BtgAttemptBatch.IBtgAttemptBatchRepository, BTGRepo.BtgAttemptBatch.BtgAttemptBatchRepository>();
        //UseCases
        builder.Services.AddScoped<BTGTicket.Create.ICreate, BTGTicket.Create.Create>();
        builder.Services.AddScoped<BTGTicket.AttemptBatch.IAttemptBatch, BTGTicket.AttemptBatch.AttemptBatch>();


        //Barramento
        builder.Services.AddScoped<Barramento.ConsultaCep.IConsultaCepRepository, Barramento.ConsultaCep.ConsultaCepRepository>();
        builder.Services.AddScoped<Barramento.Ocomon.OpenCallTicket.IOpenCallTicket, Barramento.Ocomon.OpenCallTicket.OpenCallTicket>();

        //Calculations Parameters
        builder.Services.AddScoped<CalculationsParametersTelaUnica.ICalculationsParametersRepository, CalculationsParametersTelaUnica.CalculationsParametersRepository>();
        builder.Services.AddScoped<IGetCalculationParameterByIdClientDatacob, GetCalculationParameterByIdClientDatacob>();

        //MktzapCompany
        builder.Services.AddScoped<MktzapCompany.IMktzapCompanyRepository, MktzapCompany.MktzapCompanyRepository>();

        // BoletoEmail
        builder.Services.AddScoped<BoletoEmailDatacob.ISendBoletoEmail, BoletoEmailDatacob.SendBoletoEmail>();

        //UserCrm
        builder.Services.AddScoped<UserCrmRepo.IUserCrmRepository, UserCrmRepo.UserCrmRepository>();
        //UseCases
        builder.Services.AddScoped<UserCrm.Create.ICreate, UserCrm.Create.Create>();
        builder.Services.AddScoped<UserCrm.Update.IUpdate, UserCrm.Update.Update>();
        builder.Services.AddScoped<UserCrm.Delete.IDelete, UserCrm.Delete.Delete>();

        //DatacobSaveNegotiation
        builder.Services.AddScoped<DcSaveNegRepo.IDatacobSaveNegotiationRepository, DcSaveNegRepo.DatacobSaveNegotiationRepository>();
        //UseCases
        builder.Services.AddScoped<DcSaveNeg.RPA.ListPending.IListPending, DcSaveNeg.RPA.ListPending.ListPending>();
        builder.Services.AddScoped<DcSaveNeg.RPA.ChangeStatus.IChangeStatus, DcSaveNeg.RPA.ChangeStatus.ChangeStatus>();
        builder.Services.AddScoped<DcSaveNeg.RPA.Link.ILink, DcSaveNeg.RPA.Link.Link>();
        builder.Services.AddScoped<DcSaveNeg.CRM.Create.ICreate, DcSaveNeg.CRM.Create.Create>();
        builder.Services.AddScoped<DcSaveNeg.CRM.List.IList, DcSaveNeg.CRM.List.List>();
        builder.Services.AddScoped<DcSaveNeg.CRM.ListByGrouping.IListByGrouping, DcSaveNeg.CRM.ListByGrouping.ListByGrouping>();
        builder.Services.AddScoped<DcSaveNeg.CRM.UpdateStatus.IUpdateStatus, DcSaveNeg.CRM.UpdateStatus.UpdateStatus>();
        builder.Services.AddScoped<DcSaveNeg.CRM.Execute.IExecute, DcSaveNeg.CRM.Execute.Execute>();
        builder.Services.AddScoped<DcSaveNeg.CRM.ApprovalList.IApprovalList, DcSaveNeg.CRM.ApprovalList.ApprovalList>();

        //DatacobJokerTicket
        builder.Services.AddScoped<DcJokerTicketRepo.IDatacobJokerTicketRepository, DcJokerTicketRepo.DatacobJokerTicketRepository>();
        //UseCases
        builder.Services.AddScoped<DcJokerTicket.RPA.ListPending.IListPending, DcJokerTicket.RPA.ListPending.ListPending>();
        builder.Services.AddScoped<DcJokerTicket.RPA.ChangeStatus.IChangeStatus, DcJokerTicket.RPA.ChangeStatus.ChangeStatus>();
        builder.Services.AddScoped<DcJokerTicket.CRM.Create.ICreate, DcJokerTicket.CRM.Create.Create>();
        builder.Services.AddScoped<DcJokerTicket.CRM.List.IList, DcJokerTicket.CRM.List.List>();
        builder.Services.AddScoped<DcJokerTicket.CRM.ListByGrouping.IListByGrouping, DcJokerTicket.CRM.ListByGrouping.ListByGrouping>();

        // Projuris =======================================================================================================================

        //BaseGeral
        builder.Services.AddScoped<BGProjurisRepo.IBaseGeralRepository, BGProjurisRepo.BaseGeralRepository>();
        //UseCases
        builder.Services.AddScoped<BGProjuris.Insert.IInsert, BGProjuris.Insert.Insert>();

        //DesdobramentoClone
        builder.Services.AddScoped<DCProjurisRepo.IDesdobramentoCloneRepository, DCProjurisRepo.DesdobramentoCloneRepository>();
        //UseCases
        builder.Services.AddScoped<DCProjuris.Insert.IInsert, DCProjuris.Insert.Insert>();

        //Documentos
        builder.Services.AddScoped<DMProjurisRepo.IDocumentoRepository, DMProjurisRepo.DocumentoRepository>();
        //UseCases
        builder.Services.AddScoped<DMProjuris.Insert.IInsert, DMProjuris.Insert.Insert>();

        //Custas
        builder.Services.AddScoped<CTProjurisRepo.ICustasProjurisRepository, CTProjurisRepo.CustasProjurisRepository>();
        //UseCases
        builder.Services.AddScoped<CTProjuris.Insert.IInsert, CTProjuris.Insert.Insert>();

        //Eventos
        builder.Services.AddScoped<EVProjurisRepo.IEventoRepository, EVProjurisRepo.EventoRepository>();
        //UseCases
        builder.Services.AddScoped<EVProjuris.Insert.IInsert, EVProjuris.Insert.Insert>();

        // END Projuris =======================================================================================================================[

        //LogErrorApp
        builder.Services.AddScoped<LogErrorAppRepo.ILogErrorAppRepository, LogErrorAppRepo.LogErrorAppRepository>();
        //UseCases
        builder.Services.AddScoped<LogErrorApp.Insert.IInsert, LogErrorApp.Insert.Insert>();
        builder.Services.AddScoped<LogErrorApp.InsertLocalStorage.IInsertLocalStorage, LogErrorApp.InsertLocalStorage.InsertLocalStorage>();

        //LogApi
        builder.Services.AddScoped<LogApiRepo.ILogApiRepository, LogApiRepo.LogApiRepository>();
        //UseCases
        builder.Services.AddScoped<LogApi.Create.ICreate, LogApi.Create.Create>();

        //MissedOccurrence
        builder.Services.AddScoped<MissedOccurrenceRepo.IMissedOccurrenceRepository, MissedOccurrenceRepo.MissedOccurrenceRepository>();
        //UseCases
        builder.Services.AddScoped<MissedOccurrence.Insert.IInsert, MissedOccurrence.Insert.Insert>();

        //CorJuridicoParam
        builder.Services.AddScoped<CorJuridicoParamRepo.ICorJuridicoParamRepository, CorJuridicoParamRepo.CorJuridicoParamRepository>();
        //UseCases
        builder.Services.AddScoped<CorJuridicoParam.Insert.IInsert, CorJuridicoParam.Insert.Insert>();
        builder.Services.AddScoped<CorJuridicoParam.Update.IUpdate, CorJuridicoParam.Update.Update>();
        builder.Services.AddScoped<CorJuridicoParam.Delete.IDelete, CorJuridicoParam.Delete.Delete>();
        builder.Services.AddScoped<CorJuridicoParam.List.IList, CorJuridicoParam.List.List>();

        //SafraCampaignPermissions
        builder.Services.AddScoped<SafraCampaignPermissionsRepo.ISafraCampaignPermissionsRepository, SafraCampaignPermissionsRepo.SafraCampaignPermissionsRepository>();
        //UseCases
        builder.Services.AddScoped<SafraCampaignPermissions.Insert.IInsert, SafraCampaignPermissions.Insert.Insert>();
        builder.Services.AddScoped<SafraCampaignPermissions.Update.IUpdate, SafraCampaignPermissions.Update.Update>();
        builder.Services.AddScoped<SafraCampaignPermissions.Delete.IDelete, SafraCampaignPermissions.Delete.Delete>();
        builder.Services.AddScoped<SafraCampaignPermissions.List.IList, SafraCampaignPermissions.List.List>();

        //SafraCampaign
        builder.Services.AddScoped<SafraCampaignRepo.ISafraCampaignRepository, SafraCampaignRepo.SafraCampaignRepository>();
        builder.Services.AddScoped<SafraCampaignRepo.ISafraCampaignItemRepository, SafraCampaignRepo.SafraCampaignItemRepository>();
        //UseCases
        builder.Services.AddScoped<SafraCampaign.Create.ICreate, SafraCampaign.Create.Create>();
        builder.Services.AddScoped<SafraCampaign.Get.IGet, SafraCampaign.Get.Get>();
        builder.Services.AddScoped<SafraCampaign.Get.IGetList, SafraCampaign.Get.GetList>();
        builder.Services.AddScoped<SafraCampaign.Get.IGetListRpa, SafraCampaign.Get.GetListRpa>();
        builder.Services.AddScoped<SafraCampaign.Delete.IDelete, SafraCampaign.Delete.Delete>();
        builder.Services.AddScoped<SafraCampaign.Delete.IDeleteItem, SafraCampaign.Delete.DeleteItem>();
        builder.Services.AddScoped<SafraCampaign.Update.IUpdateItem, SafraCampaign.Update.UpdateItem>();
        builder.Services.AddScoped<SafraCampaign.Job.IJob, SafraCampaign.Job.Job>();
        builder.Services.AddScoped<SafraCampaign.Job.IJobJuridico, SafraCampaign.Job.JobJuridico>();

        //BBC =======================================================================================================================

        //BbcAuth
        builder.Services.AddScoped<BbcAuthRepo.IBbcAuthRepository, BbcAuthRepo.BbcAuthRepository>();

        //BbcProducts
        builder.Services.AddScoped<BbcProductsRepo.IBbcProductsRepository, BbcProductsRepo.BbcProductsRepository>();
        //UseCases
        builder.Services.AddScoped<BbcProducts.List.IList, BbcProducts.List.List>();

        //BbcConfigs
        builder.Services.AddScoped<BbcConfigsRepo.IBbcConfigsRepository, BbcConfigsRepo.BbcConfigsRepository>();

        //BBC
        //UseCases 
        builder.Services.AddScoped<BbcUC.Balance.Check.ICheck, BbcUC.Balance.Check.Check>();
        builder.Services.AddScoped<BbcUC.Simulation.Eligible.IEligible, BbcUC.Simulation.Eligible.Eligible>();
        builder.Services.AddScoped<BbcUC.Simulation.Simulate.ISimulate, BbcUC.Simulation.Simulate.Simulate>();
        builder.Services.AddScoped<BbcUC.Deal.Register.IRegister, BbcUC.Deal.Register.Register>();
        builder.Services.AddScoped<BbcUC.Deal.Check.ICheckDeal, BbcUC.Deal.Check.CheckDeal>();
        builder.Services.AddScoped<BbcUC.Deal.List.IListDeal, BbcUC.Deal.List.ListDeal>();
        builder.Services.AddScoped<BbcUC.Ticket.Get.IGetTicket, BbcUC.Ticket.Get.GetTicket>();

        //Notification
        builder.Services.AddScoped<NotifRepo.INotificationRepository, NotifRepo.NotificationRepository>();
        //UseCases
        builder.Services.AddScoped<Notif.Create.ICreate, Notif.Create.Create>();
        builder.Services.AddScoped<Notif.CreateBatchUser.ICreateBatchUser, Notif.CreateBatchUser.CreateBatchUser>();
        builder.Services.AddScoped<Notif.CreateByGroup.ICreateByGroup, Notif.CreateByGroup.CreateByGroup>();
        builder.Services.AddScoped<Notif.Update.IUpdate, Notif.Update.Update>();
        builder.Services.AddScoped<Notif.List.IList, Notif.List.List>();
        builder.Services.AddScoped<Notif.ListByUser.IListByUser, Notif.ListByUser.ListByUser>();
        builder.Services.AddScoped<Notif.Delete.IDelete, Notif.Delete.Delete>();

        //Agreement
        builder.Services.AddScoped<Agreement.Agreement.IAgreementRepository, Agreement.Agreement.AgreementRepository>();
        builder.Services.AddScoped<Agreement.AgreementSimulation.IAgreementSimulationRepository, Agreement.AgreementSimulation.AgreementSimulationRepository>();


        //BbcInstallmentParam
        builder.Services.AddScoped<BbcInstallmentParamRepo.IBbcInstallmentParamRepository, BbcInstallmentParamRepo.BbcInstallmentParamRepository>();
        //UseCases
        builder.Services.AddScoped<BbcInstallmentParam.Create.ICreate, BbcInstallmentParam.Create.Create>();
        builder.Services.AddScoped<BbcInstallmentParam.Update.IUpdate, BbcInstallmentParam.Update.Update>();
        builder.Services.AddScoped<BbcInstallmentParam.List.IList, BbcInstallmentParam.List.List>();
        builder.Services.AddScoped<BbcInstallmentParam.Delete.IDelete, BbcInstallmentParam.Delete.Delete>();

        //BbcDiscountParam
        builder.Services.AddScoped<BbcDiscountParamRepo.IBbcDiscountParamRepository, BbcDiscountParamRepo.BbcDiscountParamRepository>();
        //UseCases
        builder.Services.AddScoped<BbcDiscountParam.Create.ICreate, BbcDiscountParam.Create.Create>();
        builder.Services.AddScoped<BbcDiscountParam.Update.IUpdate, BbcDiscountParam.Update.Update>();
        builder.Services.AddScoped<BbcDiscountParam.List.IList, BbcDiscountParam.List.List>();
        builder.Services.AddScoped<BbcDiscountParam.Delete.IDelete, BbcDiscountParam.Delete.Delete>();

        //Topic
        builder.Services.AddScoped<ITopicRepository, TopicRepository>();
        builder.Services.AddScoped<ITopicDocumentRepository, TopicDocumentRepository>();

        builder.Services.AddScoped<IConfigurationCrmRepository, ConfigurationCrmRepository>();
        builder.Services.AddScoped<IMailingRepository, MailingRepository>();
        builder.Services.AddScoped<IMailingContractRepository, MailingContractRepository>();
        builder.Services.AddScoped<IMailingUserRepository, MailingUserRepository>();

        builder.Services.AddScoped<IIntegrationConfigurationRepository, IntegrationConfigurationRepository>();

        //UserControlTicket
        builder.Services.AddScoped<UserControlTicketRepo.IUserControlTicketRepository, UserControlTicketRepo.UserControlTicketRepository>();
        //UseCases
        builder.Services.AddScoped<UserControlTicket.CreateOrEdit.ICreateOrEdit, UserControlTicket.CreateOrEdit.CreateOrEdit>();
        builder.Services.AddScoped<UserControlTicket.ListByCreateDate.IListByCreateDate, UserControlTicket.ListByCreateDate.ListByCreateDate>();
        builder.Services.AddScoped<UserControlTicket.SendEmail.ISendEmail, UserControlTicket.SendEmail.SendEmail>();

        //EmailOccurrence
        //UseCases
        builder.Services.AddScoped<EmailOccurrence.SendEmail.ISendEmailEO, EmailOccurrence.SendEmail.SendEmailEO>();

        //AdditionEntrySimulation
        builder.Services.AddScoped<AdditionEntrySimulationRepo.IAdditionEntrySimulationRepository, AdditionEntrySimulationRepo.AdditionEntrySimulationRepository>();
        builder.Services.AddScoped<AdditionEntrySimulationRepo.Param.IAdditionEntrySimulationParamRepository, AdditionEntrySimulationRepo.Param.AdditionEntrySimulationParamRepository>();
        //UseCases
        builder.Services.AddScoped<AdditionEntrySimulation.Create.IAdditionEntryCreate, AdditionEntrySimulation.Create.AdditionEntryCreate>();
        builder.Services.AddScoped<AdditionEntrySimulation.Param.Create.IAdditionEntryParamCreate, AdditionEntrySimulation.Param.Create.AdditionEntryParamCreate>();
        builder.Services.AddScoped<AdditionEntrySimulation.Param.Get.IAdditionEntryParamGet, AdditionEntrySimulation.Param.Get.AdditionEntryParamGet>();
        builder.Services.AddScoped<AdditionEntrySimulation.Param.Delete.IAdditionEntryParamDelete, AdditionEntrySimulation.Param.Delete.AdditionEntryParamDelete>();
        builder.Services.AddScoped<AdditionEntrySimulation.Param.Update.IAdditionEntryParamUpdate, AdditionEntrySimulation.Param.Update.AdditionEntryParamUpdate>();

        //DilutionEntrySimulation
        builder.Services.AddScoped<DilutionEntrySimulationRepo.IDilutionEntrySimulationRepository, DilutionEntrySimulationRepo.DilutionEntrySimulationRepository>();
        //UseCases
        builder.Services.AddScoped<DilutionEntrySimulation.Create.IDilutionEntryCreate, DilutionEntrySimulation.Create.DilutionEntryCreate>();

        //CustomerHistory
        builder.Services.AddScoped<CustomerHistoryRepo.ICustomerHistoryRepository, CustomerHistoryRepo.CustomerHistoryRepository>();
        //UseCases
        builder.Services.AddScoped<CustomerHistory.List.IList, CustomerHistory.List.List>();

        //OccurrenceRules
        builder.Services.AddScoped<OccurrenceRulesRepo.IOccurrenceRulesRepository, OccurrenceRulesRepo.OccurrenceRulesRepository>();
        //UseCases
        builder.Services.AddScoped<OccurrenceRules.Get.IGetOR, OccurrenceRules.Get.GetOR>();
        builder.Services.AddScoped<OccurrenceRules.Create.ICreateOR, OccurrenceRules.Create.CreateOR>();
        builder.Services.AddScoped<OccurrenceRules.Update.IUpdateOR, OccurrenceRules.Update.UpdateOR>();
        builder.Services.AddScoped<OccurrenceRules.Delete.IDeleteOR, OccurrenceRules.Delete.DeleteOR>();
        builder.Services.AddScoped<OccurrenceRules.SendEmail.ISendEmailOR, OccurrenceRules.SendEmail.SendEmailOR>();

        //newcon
        builder.Services.AddScoped<INewconAssembleiaRepository, NewconAssembleiaRepository>();
        builder.Services.AddScoped<INewconBemObjetoRepository, NewconBemObjetoRepository>();
        builder.Services.AddScoped<INewconBemObjetoFgtsRepository, NewconBemObjetoFgtsRepository>();
        builder.Services.AddScoped<INewconBemObjetoFgtsMensalRepository, NewconBemObjetoFgtsMensalRepository>();
        builder.Services.AddScoped<INewconBemObjetoPagamentoRepository, NewconBemObjetoPagamentoRepository>();
        builder.Services.AddScoped<INewconBemObjetoRendimentoRepository, NewconBemObjetoRendimentoRepository>();
        builder.Services.AddScoped<INewconBemObjetoRendimentoMensalRepository, NewconBemObjetoRendimentoMensalRepository>();
        builder.Services.AddScoped<INewconClienteRepository, NewconClienteRepository>();
        builder.Services.AddScoped<INewconClienteBancoRepository, NewconClienteBancoRepository>();
        builder.Services.AddScoped<INewconContemplacaoRepository, NewconContemplacaoRepository>();
        builder.Services.AddScoped<INewconCotaRepository, NewconCotaRepository>();
        builder.Services.AddScoped<INewconCotaAgendaRepository, NewconCotaAgendaRepository>();
        builder.Services.AddScoped<INewconCotaAtrasoRepository, NewconCotaAtrasoRepository>();
        builder.Services.AddScoped<INewconCotaCobrancaRepository, NewconCotaCobrancaRepository>();
        builder.Services.AddScoped<INewconCotaCobrancaParcelaRepository, NewconCotaCobrancaParcelaRepository>();
        builder.Services.AddScoped<INewconCotaDadoCotaRepository, NewconCotaDadoCotaRepository>();
        builder.Services.AddScoped<INewconCotaDadoCotaBancoRepository, NewconCotaDadoCotaBancoRepository>();
        builder.Services.AddScoped<INewconCotaDifParcelaRepository, NewconCotaDifParcelaRepository>();
        builder.Services.AddScoped<INewconCotaEnvolvidoRepository, NewconCotaEnvolvidoRepository>();
        builder.Services.AddScoped<INewconCotaEnvolvidoCotaRepository, NewconCotaEnvolvidoCotaRepository>();
        builder.Services.AddScoped<INewconCotaEnvolvidoVigenciaRepository, NewconCotaEnvolvidoVigenciaRepository>();
        builder.Services.AddScoped<INewconCotaNegociacaoRepository, NewconCotaNegociacaoRepository>();
        builder.Services.AddScoped<INewconCotaNegociacaoParcelaRepository, NewconCotaNegociacaoParcelaRepository>();
        builder.Services.AddScoped<INewconCotaReportavelRepository, NewconCotaReportavelRepository>();
        builder.Services.AddScoped<INewconCotaRepresentanteRepository, NewconCotaRepresentanteRepository>();
        builder.Services.AddScoped<INewconCotaSaldoDevedorRepository, NewconCotaSaldoDevedorRepository>();
        builder.Services.AddScoped<INewconCotaSaldoDevedorOutrosValorRepository, NewconCotaSaldoDevedorOutrosValorRepository>();
        builder.Services.AddScoped<INewconCotaTitularidadeRepository, NewconCotaTitularidadeRepository>();
        builder.Services.AddScoped<INewconCotaTitularidadeVigenciaRepository, NewconCotaTitularidadeVigenciaRepository>();
        builder.Services.AddScoped<INewconCotaValoresPagoRepository, NewconCotaValoresPagoRepository>();
        builder.Services.AddScoped<INewconCotaValorParcelaRepository, NewconCotaValorParcelaRepository>();
        builder.Services.AddScoped<INewconComissionadoRepository, NewconComissionadoRepository>();
        builder.Services.AddScoped<INewconDesclassificacaoRepository, NewconDesclassificacaoRepository>();
        builder.Services.AddScoped<INewconDesclassificacaoFaseRepository, NewconDesclassificacaoFaseRepository>();
        builder.Services.AddScoped<INewconDifGrupoRepository, NewconDifGrupoRepository>();
        builder.Services.AddScoped<INewconEmpresaRepository, NewconEmpresaRepository>();
        builder.Services.AddScoped<INewconFilialRepository, NewconFilialRepository>();
        builder.Services.AddScoped<INewconPessoaRepository, NewconPessoaRepository>();
        builder.Services.AddScoped<INewconPessoaContatoRepository, NewconPessoaContatoRepository>();
        builder.Services.AddScoped<INewconPessoaEnderecoRepository, NewconPessoaEnderecoRepository>();
        builder.Services.AddScoped<INewconPlanoVendaRepository, NewconPlanoVendaRepository>();
        builder.Services.AddScoped<INewconPontoEntregaRepository, NewconPontoEntregaRepository>();
        builder.Services.AddScoped<INewconPontoVendaRepository, NewconPontoVendaRepository>();
        builder.Services.AddScoped<INewconRecuperacaoRepository, NewconRecuperacaoRepository>();
        builder.Services.AddScoped<INewconSuspensaoRepository, NewconSuspensaoRepository>();
        builder.Services.AddScoped<INewconUnidadeRepository, NewconUnidadeRepository>();
        builder.Services.AddScoped<INewconEquipeVendaRepository, NewconEquipeVendaRepository>();
        builder.Services.AddScoped<INewconRoboSemafaroRepository, NewconRoboSemafaroRepository>();

        /* Prestadores de Servicos */
        builder.Services.AddScoped<PrestadoresServicosRepo.IPrestadoresServicosRepository, PrestadoresServicosRepo.PrestadoresServicosRepository>();
        // UseCases
        builder.Services.AddScoped<PrestadoresServicosUC.Create.IPrestadoresServicosCreate, PrestadoresServicosUC.Create.PrestadoresServicosCreate>();
        builder.Services.AddScoped<PrestadoresServicosUC.Get.IPrestadoresServicosGet, PrestadoresServicosUC.Get.PrestadoresServicosGet>();
        builder.Services.AddScoped<PrestadoresServicosUC.Update.IPrestadoresServicosUpdate, PrestadoresServicosUC.Update.PrestadoresServicosUpdate>();
        builder.Services.AddScoped<PrestadoresServicosUC.Delete.IPrestadoresServicosDelete, PrestadoresServicosUC.Delete.PrestadoresServicosDelete>();

        /**** Middleware ****/
        builder.Services.AddMemoryCache();
        builder.Services.AddSingleton<BruteForceService>();
        /**** End Middleware ****/
        //SendEmail
        builder.Services.AddScoped<ISendFromEmailRepository, SendFromEmailRepository>();
        builder.Services.AddScoped<ISendFromEmailAttemptRepository, SendFromEmailAttemptRepository>();
        builder.Services.AddScoped<ISendFromEmailAttachmentRepository, SendFromEmailAttachmentRepository>();

        builder.Services.AddScoped<IJobSendEmail, JobSendEmail>();

        builder.Services.AddScoped<ISessionService, SessionService>();

        builder.Services.AddScoped<AcordoManualUC.Create.IAcordoManualCreate, AcordoManualUC.Create.AcordoManualCreate>();
        builder.Services.AddScoped<IAcordoManualRepository, AcordoManualRepository>();
        builder.Services.AddScoped<IAcordoManualParcelaRepository, AcordoManualParcelaRepository>();

        //Cartas e Termos
        builder.Services.AddScoped<IPedidoCartasETermosRepository, PedidoCartasETermosRepository>();
        //UseCases
        builder.Services.AddScoped<UCFilaGet.IFilaAprovacaoCartasETermosGet, UCFilaGet.FilaAprovacaoCartasETermosGet>();
        builder.Services.AddScoped<UCFilaUpdate.Update.IFilaAprovacaoCartasETermosUpdateStatus, UCFilaUpdate.Update.FilaAprovacaoCartasETermosUpdateStatus>();
        builder.Services.AddScoped<UCFilaCartasETemos.Generate.ByPedido.IFilaAprovacaoCETGenerateByPedido, UCFilaCartasETemos.Generate.ByPedido.FilaAprovacaoCETGenerateByPedido>();
        builder.Services.AddScoped<UCFilaCartasETemos.Generate.Preview.IFilaAprovacaoCETGeneratePreview, UCFilaCartasETemos.Generate.Preview.FilaAprovacaoCETGeneratePreview>();
        builder.Services.AddScoped<UCFilaCartasETemos.Generate.Custom.IFilaAprovacaoCETGenerateCustom, UCFilaCartasETemos.Generate.Custom.FilaAprovacaoCETGenerateCustom>();
        builder.Services.AddScoped<UCFilaCartasETemos.Create.IFilaAprovacaoCartasETermosCreate, UCFilaCartasETemos.Create.FilaAprovacaoCartasETermosCreate>();

        //Tipos de Termos
        builder.Services.AddScoped<TipoTermosRepo.ITipoTermoRepository, TipoTermosRepo.TipoTermoRepository>();
        //UseCases
        builder.Services.AddScoped<UCTipoTermos.List.ITipoTermosList, UCTipoTermos.List.TipoTermosList>();
        builder.Services.AddScoped<UCTipoTermos.Create.ITipoTermosCreate, UCTipoTermos.Create.TipoTermosCreate>();
        builder.Services.AddScoped<UCTipoTermos.Remove.ITipoTermosRemove, UCTipoTermos.Remove.TipoTermosRemove>();
        builder.Services.AddScoped<UCTipoTermos.Update.ITipoTermosUpdate, UCTipoTermos.Update.TipoTermosUpdate>();

        //Conteudo dos Termos
        builder.Services.AddScoped<ConteudoTermosRepo.IConteudoTermoRepository, ConteudoTermosRepo.ConteudoTermoRepository>();
        //UseCases
        builder.Services.AddScoped<UCConteudoTermos.List.IConteudoTermosList, UCConteudoTermos.List.ConteudoTermosList>();
        builder.Services.AddScoped<UCConteudoTermos.ListSimplified.IConteudoTermosListSimplified, UCConteudoTermos.ListSimplified.ConteudoTermosListSimplified>();
        builder.Services.AddScoped<UCConteudoTermos.Create.IConteudoTermosCreate, UCConteudoTermos.Create.ConteudoTermosCreate>();
        builder.Services.AddScoped<UCConteudoTermos.Remove.IConteudoTermosRemove, UCConteudoTermos.Remove.ConteudoTermosRemove>();
        builder.Services.AddScoped<UCConteudoTermos.Update.IConteudoTermosUpdate, UCConteudoTermos.Update.ConteudoTermosUpdate>();

        //Infos dos Termos
        builder.Services.AddScoped<InfosTermosRepo.IPedidoTermoInfosRepository, InfosTermosRepo.PedidoTermoInfosRepository>();
        //UseCases
        builder.Services.AddScoped<UCInfosTermos.List.IInfosTermosList, UCInfosTermos.List.InfosTermosList>();
        builder.Services.AddScoped<UCInfosTermos.Create.IInfosTermosCreate, UCInfosTermos.Create.InfosTermosCreate>();
        builder.Services.AddScoped<UCInfosTermos.Remove.IInfosTermosRemove, UCInfosTermos.Remove.InfosTermosRemove>();
        builder.Services.AddScoped<UCInfosTermos.Update.IInfosTermosUpdate, UCInfosTermos.Update.InfosTermosUpdate>();



        builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(builder.Configuration.GetSection("AppSettings:Token").Value!)),
                    ValidateIssuer = false,
                    ValidateAudience = false
                };
            });

        builder.Services.AddAuthorization(options =>
        {
            options.AddPolicy("Admin", policy => policy.Requirements.Add(new AdminRequirement()));

            options.AddPolicy("AdminOrManager", policy =>
                policy.RequireAssertion(context =>
                    context.User.IsInRole("Admin") || context.User.IsInRole("Manager")));
        });

        builder.Services.AddHttpContextAccessor();

        builder.Services.AddHangfire(config =>
        {
            config.UseStorage(new SqlServerStorage(builder.Configuration.GetConnectionString("DefaultConnection"), new SqlServerStorageOptions
            {
                // Prefixo para as tabelas criadas pelo Hangfire no banco de dados
                SchemaName = "Hangfire",
                // Intervalo de tempo para polling das filas
                QueuePollInterval = TimeSpan.FromSeconds(25),
                // Intervalo para verificar a expiração dos trabalhos
                JobExpirationCheckInterval = TimeSpan.FromHours(1),
                // Limite de itens na lista de trabalhos no dashboard
                DashboardJobListLimit = 5000,
                // Intervalo para agregar contadores
                CountersAggregateInterval = TimeSpan.FromMinutes(5)
            }));
            config.UseConsole();
        });

        builder.Services.AddHangfireServer();

        builder.Services.Configure<FormOptions>(options =>
        {
            options.MultipartBodyLengthLimit = 104857600; // 100 MB
        });

        var app = builder.Build();

        app.UseHttpsRedirection();

        /***** Middleware *****/
        app.UseMiddleware<LogApiMiddleware>();
        app.UseMiddleware<BruteForceProtectionMiddleware>();
        /***** End Middleware *****/

        app.UseCors(MyAllowSpecificOrigins);

        app.UseAuthentication();

        app.UseAuthorization();

        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.MapControllers();

        app.UseStaticFiles();

        if (!app.Environment.IsDevelopment())
        {
            app.Use(async (context, next) =>
            {
                if (context.Request.Path.StartsWithSegments("/swagger"))
                {
                    if (!context.User.Identity.IsAuthenticated)
                    {
                        context.Response.StatusCode = 401;
                        return;
                    }
                }
                await next();
            });
        }

        app.Run();
    }
}