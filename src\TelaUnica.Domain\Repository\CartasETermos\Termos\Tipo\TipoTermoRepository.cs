using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Data;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;

public class TipoTermoRepository(DataContext context, IHttpContextAccessor httpContextAccessor) : ITipoTermoRepository
{
    private readonly DataContext _context = context;
    public readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public async Task Delete(TipoTermo aggregate, CancellationToken cancellationToken)
    {
        aggregate.Delete();
        _context.TipoTermo.Update(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<TipoTermo> Get(Guid id, CancellationToken cancellationToken)
    {
        return await _context.TipoTermo.FirstOrDefaultAsync(x => x.Id == id, cancellationToken) ?? throw new("TipoTermo not found");
    }

    public async Task<List<TipoTermo>> GetList(CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var items = await _context.TipoTermo
                 .Include(t => t.ConteudoTermo)
                 .AsNoTracking()
                 .ToListAsync();

            return items;
        }, []);
    }


    public async Task<object> Insert(TipoTermo aggregate, CancellationToken cancellationToken)
    {
        _context.TipoTermo.Add(aggregate);
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task Update(TipoTermo aggregate, CancellationToken cancellationToken)
    {
        aggregate.Update();
        _context.TipoTermo.Update(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
