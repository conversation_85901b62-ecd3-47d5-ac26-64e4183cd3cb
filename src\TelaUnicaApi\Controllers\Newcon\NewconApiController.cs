using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsCotas;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsNewSegSerasa;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsNewSegSerasa;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsCotas;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsRetornaDadosCotasCliente;
using TelaUnica.Domain.Dtos.Request.Newcon;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsRetornaDadosCotasCliente;
using TelaUnica.Domain.Dtos.Response.Newcon.CnsRetornaDadosClienteBemSemPagto;
using TelaUnica.Domain.Dtos.Request.Newcon.CnsRetornaDadosClienteBemSemPagto;
using TelaUnica.Domain.Interfaces.Newcon;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.Newcon.Services.ConsultaParcelasCob;
using TelaUnica.Domain.Dtos.Request.Newcon.Services.ConsultaParcelasCob;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.List;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.Create;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.Update;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.Delete;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.GetById;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.GetByName;
using TelaUnica.Domain.Application.UsesCases.Newcon.NewconAPI.Common;
using TelaUnica.Domain.Dtos.Request.NewconApi;
using TelaUnica.Domain.Dtos.Response.NewconApi;

namespace TelaUnica.Api.Controllers.Newcon;

[ApiController]
[Authorize]
[Route("api/Newcon")]
public class NewconApiController : ControllerBase
{
    public readonly INewconRepository _api;
    public readonly IListNewconAPI _list;
    public readonly ICreateNewconAPI _create;
    public readonly IUpdateNewconAPI _update;
    public readonly IDeleteNewconAPI _delete;
    public readonly IGetByIdNewconAPI _byId;
    public readonly IGetByNameNewconAPI _byName;
    public NewconApiController(
        INewconRepository api,
        IListNewconAPI list,
        ICreateNewconAPI create,
        IUpdateNewconAPI update,
        IDeleteNewconAPI delete,
        IGetByIdNewconAPI byId,
        IGetByNameNewconAPI byName
    )
    {
        _api = api;
        _list = list;
        _create = create;
        _update = update;
        _delete = delete;
        _byId = byId;
        _byName = byName;
    }


    [HttpGet]
    public async Task<ActionResult<ServiceResponse<IEnumerable<OutputNewconAPI>>>> GetList(CancellationToken cancellationToken) => Ok(await _list.Handle(cancellationToken));

    [HttpGet("{Id:Guid}")]
    public async Task<ActionResult<ServiceResponse<OutputNewconAPI>>> GetById(Guid Id, CancellationToken cancellationToken) => Ok(await _byId.Handle(new() { Id = Id }, cancellationToken));

    [HttpGet("{Name}")]
    public async Task<ActionResult<ServiceResponse<OutputNewconAPI>>> GetByName(string Name, CancellationToken cancellationToken) => Ok(await _byName.Handle(new() { Name = Name }, cancellationToken));

    [HttpGet("semafaro")]
    public async Task<ActionResult<ServiceResponse<List<NewconRoboSemafaroResponse>>>> GetSemafaro() => Ok(await _api.GetSemafaro());

    [HttpGet("status/{id}/{status}/{message}")]
    public async Task<ActionResult<ServiceResponse<NewconRoboSemafaroResponse>>> GetSemafaroStatus(Guid id, NewconRoboSemafaroStatus status, string message) => Ok(await _api.GetSemafaroStatus(id, status, message));

    [HttpPost]
    public async Task<ActionResult<ServiceResponse<OutputNewconAPI>>> PostCreate(NewconCotaRequest request, CancellationToken cancellationToken) => Ok(await _create.Handle(request, cancellationToken));

    [HttpPost("CnsCotas")]
    public async Task<ActionResult<ServiceResponse<StcCotas>>> PostCnsCotas(string newconCotaId) => Ok(await _api.PostCnsCotas(newconCotaId));

    [HttpPost("CnsRetornaDadosClienteBemSemPagto")]
    public async Task<ActionResult<ServiceResponse<NewconEnvelope<CnsRetornaDadosClienteBemSemPagtoBodyResponse>>>> PostCnsRetornaDadosClienteBemSemPagto(CnsRetornaDadosClienteBemSemPagto request) => Ok(await _api.PostCnsRetornaDadosClienteBemSemPagto(request));

    [HttpPost("CnsRetornaDadosCotasCliente")]
    public async Task<ActionResult<ServiceResponse<NewconEnvelope<CnsRetornaDadosCotasClienteBodyResponse>>>> PostCnsRetornaDadosCotasCliente(CnsRetornaDadosCotasCliente request) => Ok(await _api.PostCnsRetornaDadosCotasCliente(request));

    [HttpPost("CnsNewSegSerasa")]
    public async Task<ActionResult<ServiceResponse<NewconEnvelope<CnsNewSegSerasaBodyResponse>>>> PostCnsNewSegSerasa(CnsNewSegSerasa request) => Ok(await _api.PostCnsNewSegSerasa(request));

    [HttpPost("ConsultaParcelasCob")]
    public async Task<ActionResult<ServiceResponse<ElementEnvelopeCobServiceResponse>>> PostConsultaParcelasCob(ElementConsultaParcelasCob request) => Ok(await _api.PostConsultaParcelasCob(request));

}