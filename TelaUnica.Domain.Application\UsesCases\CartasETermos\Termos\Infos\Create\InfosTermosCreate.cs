using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.PedidoInfos;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Utils;
using TelaUnica.Domain.Repository.CartasETermos;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Create;

public class InfosTermosCreate(
    IPedidoTermoInfosRepository repo,
    IPedidoCartasETermosRepository repoPedido,
    ITipoTermoRepository repoTipo,
    IDatacobDapperRepository dapperDCRep
) : IInfosTermosCreate
{
    public IPedidoTermoInfosRepository _repo = repo;
    public IPedidoCartasETermosRepository _repoPedido = repoPedido;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;
    public ITipoTermoRepository _repoTipo = repoTipo;

    public async Task<ServiceResponse<bool>> Handle(InfosTermoCreateInput input, CancellationToken cancellationToken)
    {
        ServiceResponse<bool> response = new(true);
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            input.Validate();
            var pedido = await _repoPedido.Get(input.PedidoId, cancellationToken) ?? throw new("Pedido não encontrado.");
            _ = await _repoTipo.Get(input.TipoTermoId, cancellationToken) ?? throw new("Tipo de termo não encontrado.");
            var InfosTermo = await _repo.GetByPedido(input.PedidoId, cancellationToken);
            if (InfosTermo != null) throw new("Infos de termo já existente.");

            var model = input.ToModel();
            _ = await _repo.Insert(model, cancellationToken) ?? throw new();
            await _repoPedido.UpdateStatus(pedido.Id, "Aguardando aprovação", cancellationToken);
        }, (ex) => response.SetFailure($"Falha ao inserir Informações do termo! {ex.Message}", false));
        return response;
    }
}
