using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Dtos.Request.Datacob;

public class DatacobAcordoConfirmarRequest
{
    public int IdContrato { get; set; }
    public decimal ValorEntrada { get; set; }
    public int QtdeParcelas { get; set; }
    public DateTime DataPagtoEntrada { get; set; }
    public decimal? ValorParcela { get; set; }
    public DateTime? DataPagtoParcelas { get; set; }
    public DateTime? DataNegociacao { get; set; }
    public string? Email { get; set; }
    public string? Ddd { get; set; }
    public string? Fone { get; set; }
    public List<int>? Parcelas { get; set; }
    public int? ModalidadeNegociacao { get; set; }
    public GvcRodobens? Crm { get; set; }
}