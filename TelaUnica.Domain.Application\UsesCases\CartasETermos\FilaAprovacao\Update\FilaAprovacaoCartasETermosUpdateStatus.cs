﻿using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.CartasETermos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Update;

public class FilaAprovacaoCartasETermosUpdateStatus(
    IPedidoCartasETermosRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IFilaAprovacaoCartasETermosUpdateStatus
{
    public IPedidoCartasETermosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<bool>> Handle(Guid Id, string status, CancellationToken cancellationToken)
    {
        
        var response = new ServiceResponse<bool>();
        try
        {
            
            var res = await _repo.UpdateStatus(Id,status,cancellationToken);
            if (!res)
            {
                response.Success = false;
                response.Message = "Falha ao atualizar o status do pedido.";
                return response;
            }
            response.Success = true;
            response.Data = true; // Assuming the update was successful
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }
        return response;
    }
}
