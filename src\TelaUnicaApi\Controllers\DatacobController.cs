using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TelaUnica.Domain.Application.UsesCases.BoletoEmailDatacob;
using TelaUnica.Domain.Application.UsesCases.CalculationParameter.common;
using TelaUnica.Domain.Application.UsesCases.CalculationParameter.Get;
using TelaUnica.Domain.Dtos.Request.Datacob;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.Datacob;
using TelaUnica.Domain.Dtos.Response.Datacob.Api;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models.Datacob;
using static TelaUnica.Api.Controllers.FileController;

namespace TelaUnica.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class DatacobController : ControllerBase
{
    public readonly IDatacobRepository _datacobRepository;
    public readonly IDatacobApiRepository _api;
    public readonly IGetCalculationParameterByIdClientDatacob _calculationParamters;
    public readonly ISendBoletoEmail _sendBoletoEmailDatacob;
    public readonly IDatacobDapperRepository _dapperDcRep;
    public DatacobController(IDatacobRepository datacobRepository
        , IDatacobApiRepository api
        , IGetCalculationParameterByIdClientDatacob calculationParamters
        , ISendBoletoEmail sendBoletoEmailDatacob
        , IDatacobDapperRepository dapperDcRep

        )
    {
        _datacobRepository = datacobRepository;
        _api = api;
        _calculationParamters = calculationParamters;
        _sendBoletoEmailDatacob = sendBoletoEmailDatacob;
        _dapperDcRep = dapperDcRep;
    }

    [HttpPost("Login")]
    public async Task<ActionResult<ServiceResponse<DatacobLoginResponse>>> PostLogin() => Ok(await _api.LoginApi());

    [HttpGet("Datacobs")]
    public async Task<ActionResult<ServiceResponse<List<DatacobAccess>>>> GetDatacobs() => Ok(await _datacobRepository.GetDatacobs());

    [HttpGet("Users")]
    public async Task<ActionResult<ServiceResponse<List<UserDatacob>>>> GetUserDatacobRodobens(GvcRodobens? ActiveConnection) => Ok(await _dapperDcRep.GetUsers(ActiveConnection));

    [HttpGet("Clients")]
    public async Task<ActionResult<ServiceResponse<List<UserDatacob>>>> GetClientsDatacobRodobens(GvcRodobens? ActiveConnection, int? GroupId) => Ok(await _dapperDcRep.GetClients(ActiveConnection, GroupId));

    [HttpGet("Status")]
    public async Task<ActionResult<ServiceResponse<List<StatusDatacob>>>> GetStatusDatacobRodobens(GvcRodobens? ActiveConnection, int? Nr_Agrupamento) => Ok(await _dapperDcRep.GetStatus(ActiveConnection, Nr_Agrupamento));

    [HttpGet("BuscaDadosFinanciados")]
    public async Task<ActionResult<ServiceResponse<List<DadosCadastraisDatacob>>>> Get(GvcRodobens ActiveConnection, string? documento, string? nome, string? contrato, string? email, string? telefone, int? clientId, int? groupId, int? aberto, int? agrupamentoId, int? id_Status_Contrato, int? cyber, string? fase, int? contratoId) => Ok(await _dapperDcRep.BuscaDadosFinanciados(ActiveConnection, documento, nome, contrato, email, telefone, clientId, groupId, aberto, agrupamentoId, id_Status_Contrato, cyber, fase, contratoId));

    [HttpGet("BuscaDadosFinanciadoContrato")]
    public async Task<ActionResult<ServiceResponse<List<DadosCadastraisDatacob>>>> GetBuscaDadosFinanciadoContrato(GvcRodobens ActiveConnection, int AgrupamentoId, int? Aberto) => Ok(await _dapperDcRep.BuscaDadosFinanciadoContrato(ActiveConnection, AgrupamentoId, Aberto));

    [HttpGet("Groups")]
    public async Task<ActionResult<ServiceResponse<List<UserDatacob>>>> GetGroupsDatacobRodobens(GvcRodobens? ActiveConnection) => Ok(await _dapperDcRep.GetGroups(ActiveConnection));

    [HttpGet("DadosFinanciadoAdjacente")]
    public async Task<ActionResult<ServiceResponse<DatacobDadosFinanciadoAdjacente>>> GetDadosFinanciadoAdjacente(int IdFinanciado, string numeroContrato = "") => Ok(await _dapperDcRep.GetFinanInfos(IdFinanciado, numeroContrato));

    [HttpGet("ContratosAtivos")]
    public async Task<ActionResult<ServiceResponse<List<ContratosAtivosDatacob>>>> GetContratosAtivos(int IdFinanciado, int IdAgrupamento, int? Ativo, [FromQuery] string? statusInstallment = "") => Ok(await _dapperDcRep.GetContratosAtivos(IdFinanciado, IdAgrupamento, Ativo, statusInstallment));

    [HttpGet("DetalhesContratos")]
    public async Task<ActionResult<ServiceResponse<List<DetalhesContratosDatacob>>>> GetDetalhesContratos(int IdFinanciado, int idGrupo) => Ok(await _dapperDcRep.GetDetalhesContratos(IdFinanciado, idGrupo));

    [HttpGet("DadosBens")]
    public async Task<ActionResult<ServiceResponse<List<DadosBensDatacob>>>> GetDadosBens(int IdContrato) => Ok(await _dapperDcRep.GetDadosBens(IdContrato));

    [HttpGet("DadosCadastrais/Financiado")]
    public async Task<ActionResult<ServiceResponse<DatacobDadosCadastraisResponse>>> GetDadosCadastrais(string cpfCnpj, GvcRodobens? crm) => Ok(await _api.DadosCadastrais(cpfCnpj, crm));

    [HttpPost("DadosCadastrais/Financiado")]
    public async Task<ActionResult<ServiceResponse<DatacobResponse>>> PostDadosCadastrais(DatacobInsertDadosCadastraisRequest request) => Ok(await _api.PostDadosCadastrais(request));

    [HttpGet("DadosAvalistas")]
    public async Task<ActionResult<ServiceResponse<List<DadosAvalistasDatacob>>>> GetDadosAvalistas(int IdAgrupamento) => Ok(await _dapperDcRep.GetDadosAvalistas(IdAgrupamento));

    [HttpGet("GrupoContratoAberto")]
    public async Task<ActionResult<ServiceResponse<List<DatacobGrupoContratoAbertoResponse>>>> GetGrupoContatoAberto(string Cpfcnpj, int IdAgrupamento) => Ok(await _dapperDcRep.GetGrupoContratoAberto(Cpfcnpj, IdAgrupamento));

    [HttpGet("Negociacoes")]
    public async Task<ActionResult<ServiceResponse<List<NegociacoesDatacob>>>> GetNegociacoes(int IdAgrupamento, int? IdParcela, bool? DetalheParcela = true, string? numeroContrato = "") => Ok(await _dapperDcRep.GetNegociacoes(IdAgrupamento, IdParcela, DetalheParcela, numeroContrato));

    [HttpGet("Negociacoes/Parcelas")]
    public async Task<ActionResult<ServiceResponse<List<NegociacaoParcelasDatacob>>>> GetNegociacaoParcelas(int IdNegociacao, string numeroContrato) => Ok(await _dapperDcRep.GetNegociacaoParcelas(IdNegociacao, numeroContrato));

    [HttpGet("Negociacoes/Parcelas/Boleto")]
    public async Task<ActionResult<ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>>>> GetParcelasNegociacaoBoleto(int IdNegociacao, string numeroContrato) => Ok(await _dapperDcRep.GetParcelasNegociacaoBoleto(IdNegociacao, numeroContrato));

    [HttpGet("Negociacoes/Parcelas/Recibo")]
    public async Task<ActionResult<ServiceResponse<List<ParcelasNegociacaoReciboDatacob>>>> GetParcelasNegociacaoRecibo(int IdNegociacao) => Ok(await _dapperDcRep.GetParcelasNegociacaoRecibo(IdNegociacao));

    [HttpGet("Negociacoes/Detalhes")]
    public async Task<ActionResult<ServiceResponse<List<NegociacoesDetalhesDatacob>>>> GetNegociacaoDetalhes(int IdNegociacao) => Ok(await _dapperDcRep.GetNegociacaoDetalhes(IdNegociacao));

    [HttpGet("Negociacoes/Calculo")]
    public async Task<ActionResult<ServiceResponse<NegociacoesCalculoDatacob>>> GetNegociacaoCalculo(int IdNegociacao, GvcRodobens? crm) => Ok(await _dapperDcRep.GetNegociacaoCalculo(IdNegociacao, crm));

    [HttpPost("Negociacoes/CalcularNegociacao")]
    public async Task<ActionResult<ServiceResponse<DatacobNegociacaoDtoResponse>>> PostCalcularNegociacao(DatacobCalcularNegociacaoRequest request) => Ok(await _api.PostCalcularNegociacao(request));

    [HttpPost("Negociacoes/GerarBoleto")]
    public async Task<ActionResult<ServiceResponse<List<DatacobGerarBoletoNegociacaoResponse>>>> PostGerarBoleto(DatacobGerarBoletoNegociacaoRequest request) => Ok(await _api.PostGerarBoletoNegociacao(request));

    [HttpGet("Acordos")]
    public async Task<ActionResult<ServiceResponse<List<NegociacaoAcordosDatacob>>>> GetNegociacaoAcordos(int IdAgrupamento, string numeroContrato) => Ok(await _dapperDcRep.GetNegociacaoAcordos(IdAgrupamento, numeroContrato));

    [HttpGet("Acordos/Parcelas")]
    public async Task<ActionResult<ServiceResponse<List<NegociacaoAcordosParcelasDatacob>>>> GetNegociacaoAcordosParcelas(int IdAcordo, string numeroContrato) => Ok(await _dapperDcRep.GetNegociacaoAcordosParcelas(IdAcordo, numeroContrato));

    [HttpGet("Acordos/Parcelas/Boleto")]
    public async Task<ActionResult<ServiceResponse<List<ParcelasAcordoBoletoDatacob>>>> GetParcelasAcordoBoleto(int IdParcelaAcordo, string numeroContrato) => Ok(await _dapperDcRep.GetParcelasAcordoBoleto(IdParcelaAcordo, numeroContrato));

    [HttpGet("Acordos/Parcelas/Recibo")]
    public async Task<ActionResult<ServiceResponse<List<ParcelasAcordoReciboDatacob>>>> GetParcelasAcordoRecibo(int IdParcelaAcordo, string numeroContrato) => Ok(await _dapperDcRep.GetParcelasAcordoRecibo(IdParcelaAcordo, numeroContrato));

    [HttpGet("Acordos/Parcelas/Negociacao")]
    public async Task<ActionResult<ServiceResponse<List<ParcelasAcordoNegociacaoDatacob>>>> GetParcelasAcordoNegociacao(int IdParcelaAcordo, string numeroContrato) => Ok(await _dapperDcRep.GetParcelasAcordoNegociacao(IdParcelaAcordo, numeroContrato));

    [HttpPost("Acordos/Calcular")]
    public async Task<ActionResult<ServiceResponse<DatacobAcordoCalculoResponse>>> PostAcordoCalcular(DatacobAcordoCalculoRequest request) => Ok(await _api.PostAcordoCalcular(request));

    [HttpPost("Acordos/Confirmar")]
    public async Task<ActionResult<ServiceResponse<DatacobAcordoConfirmarResponse>>> PostAcordoConfirmar(DatacobAcordoConfirmarRequest request) => Ok(await _api.PostAcordoConfirmar(request));

    [HttpPost("Acordos/GerarBoleto")]
    public async Task<ActionResult<ServiceResponse<DatacobAcordoGerarBoletoResponse>>> PostAcordoGerarBoleto(DatacobAcordoGerarBoletoRequest request) => Ok(await _api.PostAcordoGerarBoleto(request));

    [HttpDelete("Acordos/Cancelar")]
    public async Task<ActionResult<ServiceResponse<string>>> DeleteAcordoCancelar(DatacobAcordoCancelarRequest request) => Ok(await _api.DeleteAcordoCancelar(request));

    [HttpDelete("CancelarBoleto")]
    public async Task<ActionResult<ServiceResponse<string>>> DeleteCancelarBoleto(DatacobCancelarBoletoRequest request) => Ok(await _api.DeleteCancelarBoleto(request));

    [HttpDelete("CancelarBoletoId")]
    public async Task<ActionResult<ServiceResponse<string>>> DeleteCancelarBoletoId(DatacobCancelarBoletoIdRequest request) => Ok(await _api.DeleteCancelarBoletoId(request));

    [HttpGet("DownloadBoleto")]
    public async Task<ActionResult<ServiceResponse<string>>> GetBoleto(int IdBoleto, GvcRodobens? crm) => Ok(await _api.GetBoleto(IdBoleto, crm));

    [HttpGet("Custas")]
    public async Task<ActionResult<ServiceResponse<List<ListaCustasDatacob>>>> GetCustas(int IdContrato) => Ok(await _dapperDcRep.GetCustas(IdContrato));

    [HttpGet("HistoricoAtendimento")]
    public async Task<ActionResult<ServiceResponse<List<HistoricoAtendimentoDatacob>>>> GetHistoricoAtendimentoDatacob(int Id_Agrupamento, string numeroContrato) => Ok(await _dapperDcRep.GetHistoricoAtendimentoDatacob(Id_Agrupamento, numeroContrato));

    [HttpPost("HistoricoAdicionar")]
    public async Task<ActionResult<ServiceResponse<string>>> PostHistoricoAdicionar(DatacobHistoricoAdicionarTURequest request) => Ok(await _api.PostHistoricoAdicionar(request));

    [HttpGet("Negociacoes/Parcela/Calculo")]
    public async Task<ActionResult<ServiceResponse<NegociacaoParcelasDetalhesDatacob>>> GetParcelaDetalhes(int IdParcela, int IdNegociacao) => Ok(await _dapperDcRep.GetNegociacaoParcelaDetalhes(IdParcela, IdNegociacao));

    [HttpPost("GerarRecibo")]
    public async Task<ActionResult<ServiceResponse<DatacobGerarReciboResponse>>> PostGerarRecibo(DatacobGerarReciboRequest request) => Ok(await _api.PostGerarRecibo(request));

    [HttpGet("Ocorrencias")]
    public async Task<ActionResult<ServiceResponse<DatacobGerarReciboResponse>>> GetOcorrencias() => Ok(await _dapperDcRep.GetOcorrencias());

    [HttpGet("Ocorrencias/{crm}")]
    public async Task<ActionResult<ServiceResponse<DatacobGerarReciboResponse>>> GetOcorrencias(GvcRodobens crm) => Ok(await _dapperDcRep.GetOcorrenciasCrm(crm));

    [HttpGet("HistoricoResumo")]
    public async Task<ActionResult<ServiceResponse<List<HistoricoResumoDatacob>>>> GetHistoricoResumoDatacob(int Id_Agrupamento, string numeroContrato) => Ok(await _dapperDcRep.GetHistoricoResumoDatacob(Id_Agrupamento, numeroContrato));

    [HttpGet("Parametro/Calculo")]
    public async Task<ActionResult<ServiceResponse<OutputCalculationParameter>>> GetParametroCalculo([FromQuery] InputByIdCliente request, CancellationToken cancellationToken)
        => Ok(await _calculationParamters.Handle(request, cancellationToken));

    [HttpGet("TipoEmail")]
    public async Task<ActionResult<ServiceResponse<List<DatacobTiposResponse>>>> GetTipoEmail() => Ok(await _api.GetTipoEmail());

    [HttpGet("TipoEndereco")]
    public async Task<ActionResult<ServiceResponse<List<DatacobTiposResponse>>>> GetTipoEndereco() => Ok(await _api.GetTipoEndereco());

    [HttpGet("TipoTelefone")]
    public async Task<ActionResult<ServiceResponse<List<DatacobTiposResponse>>>> GetTipoTelefone() => Ok(await _api.GetTipoTelefone());

    [HttpGet("TipoParcela")]
    public async Task<ActionResult<ServiceResponse<List<TipoParcelaDatacob>>>> GetTipoParcela() => Ok(await _dapperDcRep.GetTipoParcela());

    [HttpGet("TipoReferencia")]
    public ActionResult<ServiceResponse<List<TipoParcelaDatacob>>> GetTipoReferencia() => Ok(_datacobRepository.GetTipoReferencia());

    [HttpGet("Fases")]
    public async Task<ActionResult<ServiceResponse<List<FaseDatacob>>>> GetFases() => Ok(await _dapperDcRep.GetFases());

    [HttpGet("Acordo/Calcular/Data/{IdAcordo}")]
    public async Task<ActionResult<ServiceResponse<CalculoAcordoDatacob>>> GetAcordoCalculoData(int IdAcordo, [FromQuery] GvcRodobens? crm) => Ok(await _dapperDcRep.GetAcordoCalculoData(IdAcordo, crm));

    [HttpGet("Cidades")]
    public async Task<ActionResult<ServiceResponse<List<CidadeDatacob>>>> GetCidadeUf() => Ok(await _dapperDcRep.GetCidadeUf());

    [HttpPost("Boletos/Email")]
    public async Task<ActionResult<ServiceResponse<string>>> PostBoletosEmail(DatacobBoletoEmailRequest request) => Ok(await _sendBoletoEmailDatacob.Handle(request));

    [HttpGet("IndicadoresCampanha")]
    public async Task<ActionResult<ServiceResponse<List<IndicadoresCampanhaDatacob>>>> GetIndicadoresCampanha(int IdContrato) => Ok(await _dapperDcRep.GetIndicadoresCampanha(IdContrato));
    [HttpGet("Negociacao/ConsultarDivida")]
    public async Task<ActionResult<ServiceResponse<DatacobConsultarDividaResponse>>> GetConsultarDivida(string cpfCnpj, int idGrupo, string? numeroContrato, GvcRodobens? crm) => Ok(await _api.GetConsultarDivida(cpfCnpj, idGrupo, numeroContrato, crm));

    [HttpGet("FindCrm")]
    public async Task<ActionResult<ServiceResponse<GvcRodobens?>>> FindCrm(int IdContrato, string documento) => Ok(await _dapperDcRep.FindCrm(IdContrato, documento));

    [HttpGet("Ocorrencias/Indicadores")]
    public async Task<ActionResult<ServiceResponse<List<IndicadoresOcorrenciaDatacob>>>> GetOcorrenciasIndicadores(int IdAgrupamento) => Ok(await _dapperDcRep.GetIndicadoresOcorrencia(IdAgrupamento));

    [HttpGet("Negociacoes/Count")]
    public async Task<ActionResult<ServiceResponse<NrNegociacaoDatacob>>> GetNrNegociacao(int IdAgrupamento) => Ok(await _dapperDcRep.GetNrNegociacao(IdAgrupamento));

    [HttpGet("Financiado/OutrosContratos")]
    public async Task<ActionResult<ServiceResponse<NrNegociacaoDatacob>>> GetOutrosContratos(string doc, int idGrupo, GvcRodobens crm) => Ok(await _dapperDcRep.GetOutrosContratos(idGrupo, doc, crm));

    [HttpGet("Contrato/Boletos")]
    public async Task<ActionResult<ServiceResponse<DatacobBoletosContratoResponse>>> GetBoletosContrato(int IdAgrupamento, string numeroContrato) => Ok(await _dapperDcRep.GetBoletosContrato(IdAgrupamento, numeroContrato));

    [HttpGet("Contrato/Boletos/Listar")]
    public async Task<ActionResult<ServiceResponse<ParcelasNegociacaoBoletoDatacob>>> GetBoletosContratoListar(int IdAgrupamento, string numeroContrato) => Ok(await _dapperDcRep.GetListarBoleto(IdAgrupamento, numeroContrato));

    [HttpPost("NegociacaoLivre")]
    public async Task<ActionResult<ServiceResponse<DatacobGerarNegociacaoLivreResponse>>> PostGerarNegociacaoLivre(DatacobGerarNegociacaoLivreRequest request) => Ok(await _api.PostGerarNegociacaoLivre(request));

    [HttpPost("Negociacoes/GerarBoleto/NegociacaoSalva")]
    public async Task<ActionResult<ServiceResponse<DatacobGerarBoletoNegociacaoResponse>>> PostGerarBoletoNegociacaoSalva(DatacobGerarBoletoNegociacaoSalvaRequest request) => Ok(await _api.PostGerarBoletoNegociacaoSalva(request));

    [HttpGet("Contrato/CheckStatus/{IdAgrupamento}")]
    public async Task<ActionResult<ServiceResponse<DatacobCheckStatusDnrResponse>>> GetCheckStatus(int IdAgrupamento) => Ok(await _dapperDcRep.GetCheckStatusContrato(IdAgrupamento));

    [HttpGet("Custas/Listar")]
    public async Task<ActionResult<ServiceResponse<List<ListaCustasDatacob>>>> GetCustasListar(int IdContrato, GvcRodobens? crm) => Ok(await _api.GetCustas(IdContrato, crm));

    [HttpPost("Custas/Cadastrar")]
    public async Task<ActionResult<ServiceResponse<DatacobCustasResponse>>> PostCustasCadastrar(DatacobCustasRequest<DatacobCadastrarCustasRequest> request) => Ok(await _api.PostCadastrarCustas(request));

    [HttpPost("Custas/Atualizar")]
    public async Task<ActionResult<ServiceResponse<DatacobCustasResponse>>> PostCustasAtualizar(DatacobCustasRequest<DatacobAtualizarCustasRequest> request) => Ok(await _api.PostAtualizarCustas(request));

    [HttpPost("Custas/Devolver")]
    public async Task<ActionResult<ServiceResponse<DatacobCustasResponse>>> PostCustasDevolver(DatacobCustasRequest<DatacobDevolverCustasRequest> request) => Ok(await _api.PostDevolverCustas(request));

    [HttpPost("Custas/Reativar")]
    public async Task<ActionResult<ServiceResponse<DatacobCustasResponse>>> PostCustasReativar(DatacobCustasRequest<DatacobReativarCustasRequest> request) => Ok(await _api.PostReativarCustas(request));

    [HttpGet("Custas/MotivoDevolucao")]
    public async Task<ActionResult<ServiceResponse<List<MotivoDevolucaoDatacob>>>> GetMotivoDevolucao() => Ok(await _dapperDcRep.GetMotivoDevolucao());

    [HttpGet("Safra/Campaign/Filter")]
    public async Task<ActionResult<ServiceResponse<List<FilterSafraCampaignDatacob>>>> FilterSafraCampaign(int daysDelay, int amountInstall, SafraContractType type) => Ok(await _dapperDcRep.FilterSafraCampaign(daysDelay, amountInstall, type));

    [HttpGet("Safra/Campaign/ExportToExcel")]
    public async Task<ActionResult> FilterSafraCampaignAndExportToExcel(int daysDelay, int amountInstall, SafraContractType type)
    {
        var excelFile = await _dapperDcRep.FilterSafraCampaignAndExportToExcel(daysDelay, amountInstall, type);

        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "data.xlsx");

    }


    [HttpPost("Safra/Campaign/Filter/Excel")]
    public async Task<ActionResult<ServiceResponse<List<FilterSafraCampaignDatacob>>>> FilterSafraCampaignExcel(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return BadRequest("Nenhum arquivo foi enviado.");
        if (!file.FileName.EndsWith(".xlsx") && !file.FileName.EndsWith(".xls"))
            return BadRequest("Arquivo inválido.");
        return Ok(await _dapperDcRep.FilterSafraCampaignExcel(file));
    }

    [HttpPost("Safra/Campaign/Filter/Excel/ExportToExcel")]
    public async Task<ActionResult> FilterSafraCampaignExcelAndExportToExcel(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return BadRequest("Nenhum arquivo foi enviado.");
        if (!file.FileName.EndsWith(".xlsx") && !file.FileName.EndsWith(".xls"))
            return BadRequest("Arquivo inválido.");
        var excelFile = await _dapperDcRep.FilterSafraCampaignExcelAndExportToExcel(file);

        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "data.xlsx");

    }

    [HttpGet("VisaoBoletos")]
    public async Task<ActionResult<ServiceResponse<List<BoletosUsuariosDatacob>>>> BuscaBoletosUsuarioLogado(GvcRodobens crm, DateTime inicio, DateTime fim) => Ok(await _dapperDcRep.BuscaBoletosUsuarioLogado(crm, inicio, fim));

    [HttpGet("VisaoBoletos/{idUsuario}")]
    public async Task<ActionResult<ServiceResponse<List<BoletosUsuariosDatacob>>>> BuscaBoletosUsuarioLogado(int idUsuario, GvcRodobens crm, DateTime inicio, DateTime fim) => Ok(await _dapperDcRep.BuscaBoletosUsuarioLogado(crm, idUsuario, inicio, fim));

    [HttpPost("VisaoBoletos/ExportToPdf")]
    public ActionResult VisaoBoletosExportToPdf(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data)
    {
        var excelFile = _dapperDcRep.VisaoBoletosExportToPdf(data);

        return File(excelFile, "application/pdf", $"VisaoBoleto{DateTime.Now:yyyyMMddHHmmss}.pdf");

    }

    [HttpPost("VisaoBoletos/ExportToCsv")]
    public ActionResult VisaoBoletosExportToCsv(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data)
    {
        var excelFile = _dapperDcRep.VisaoBoletosExportToCsv(data);

        return File(excelFile, "text/csv", $"VisaoBoleto{DateTime.Now:yyyyMMddHHmmss}.csv");

    }

    [HttpPost("VisaoBoletos/ExportToExcel")]
    public async Task<ActionResult> VisaoBoletosExportToExcel(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data)
    {
        var excelFile = await _dapperDcRep.VisaoBoletosExportToExcel(data);
        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"VisaoBoleto{DateTime.Now:yyyyMMddHHmmss}.xlsx");
    }
    [HttpGet("ControleUsuario/{idUsuario}")]
    public async Task<ActionResult<ServiceResponse<List<MotivoDevolucaoDatacob>>>> GetControleUsuario(int idUsuario, DateTime dtInicio, DateTime dtFim, GvcRodobens crm, int? idGrupo, string? status, DateTime? dtVencimento, string? nr_Boleto = null)
        => Ok(await _dapperDcRep.GetControleUsuario(idUsuario, dtInicio, dtFim, crm, idGrupo, status, dtVencimento, nr_Boleto));

    [HttpGet("ControleUsuario")]
    public async Task<ActionResult<ServiceResponse<List<MotivoDevolucaoDatacob>>>> GetControleUsuario(DateTime dtInicio, DateTime dtFim, GvcRodobens crm, int? idGrupo, string? status, DateTime? dtVencimento, string? nr_Boleto = null)
        => Ok(await _dapperDcRep.GetControleUsuario(dtInicio, dtFim, crm, idGrupo, status, dtVencimento, nr_Boleto));

    [HttpPost("ControleUsuario/ExportToExcel")]
    public async Task<ActionResult> ControleUsuarioExcel(List<ControleUsuarioDatacob> data)
    {
        var excelFile = await _dapperDcRep.ControleUsuarioExcel(data);

        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "data.xlsx");

    }

    [HttpGet("DadosAuxiliares/{idContract}")]
    public async Task<ActionResult<ServiceResponse<List<ContratoPadraoDatacob>>>> GetDadosAuxiliares(GvcRodobens crm, int idContract)
    {
        return Ok(await _dapperDcRep.GetListContratoPadrao(crm, idContract));
    }

    [HttpGet("BuscaDadosMailings")]
    public async Task<ActionResult<ServiceResponse<List<DadosCadastraisDatacob>>>> GetDadosMailing(GvcRodobens crm, string groupId, string? faseId, int? dayDueStart, int? dayDueEnd, float? debtValueStart, float? debtValueEnd) => Ok(await _dapperDcRep.BuscaDadosMailing(crm, groupId, faseId, dayDueStart, dayDueEnd, debtValueStart, debtValueEnd));

    [HttpPost("BuscaDadosMailings/FromFile")]
    public async Task<ActionResult<ServiceResponse<List<DadosCadastraisDatacob>>>> GetDadosMailingFromFile(
        [FromForm] GvcRodobens crm, [FromForm] string groupId, [FromForm] string? faseId, [FromForm] int? dayDueStart, [FromForm] int? dayDueEnd, [FromForm] float? debtValueStart, [FromForm] float? debtValueEnd,
        [FromForm] FormFileModel file)
    {
        if (file == null || file.FormFile.Length == 0)
            return BadRequest("Nenhum arquivo foi enviado.");
        if (!file.FormFile.FileName.EndsWith(".xlsx") && !file.FormFile.FileName.EndsWith(".xls"))
            return BadRequest("Arquivo inválido.");

        return Ok(await _dapperDcRep.BuscaDadosMailingFromFile(file.FormFile, crm, groupId, faseId, dayDueStart, dayDueEnd, debtValueStart, debtValueEnd));
    }
}
