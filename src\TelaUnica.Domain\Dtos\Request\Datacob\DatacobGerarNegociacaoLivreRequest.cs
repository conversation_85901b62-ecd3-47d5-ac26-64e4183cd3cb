using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Dtos.Request.Datacob;

public class DatacobGerarNegociacaoLivreRequest
{
    public int IdAgrupamento { get; set; }
    public List<int> Parcelas { get; set; } = [];
    public DateTime DtNegociacao { get; set; }
    public decimal ValorPrincipal { get; set; } = 0;
    public decimal ValorCorrecao { get; set; } = 0;
    public decimal Juros { get; set; } = 0;
    public decimal Multa { get; set; } = 0;
    public decimal ComissaoPermanencia { get; set; } = 0;
    public decimal Honorarios { get; set; } = 0;
    public bool DescontoAutorizado { get; set; } = true;
    public decimal Custas { get; set; } = 0;
    public decimal Notificacao { get; set; } = 0;
    public decimal ValorTarifa { get; set; } = 0;
    public GvcRodobens? Crm { get; set; }
}