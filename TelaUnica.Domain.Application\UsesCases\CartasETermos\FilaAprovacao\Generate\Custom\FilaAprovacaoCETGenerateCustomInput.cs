﻿namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Generate.Custom;

public class FilaAprovacaoCETGenerateCustomInput()
{
    public string Html { get; set; } = string.Empty;
    public string ClientePrincipal { get; set; } = string.Empty;
    public string TipoAcao { get; set; } = string.Empty;
    public string AdversoPrincipal { get; set; } = string.Empty;
    public string GrupoCotaContrato { get; set; } = string.Empty;
    public string NrParcelasVencidas { get; set; } = string.Empty;
    public string ValorParcelasVencidas { get; set; } = string.Empty;
    public string MultaJuros { get; set; } = string.Empty;
    public string Custas { get; set; } = string.Empty;
    public string NrParcelasVincendas { get; set; } = string.Empty;
    public string ValorParcelasVincendas { get; set; } = string.Empty;
    public string Honorarios { get; set; } = string.Empty;
    public string Total { get; set; } = string.Empty;
    public string DataBase { get; set; } = string.Empty;
    public string QtdParcelasAcordadas { get; set; } = string.Empty;
    public string ValorAcordado { get; set; } = string.Empty;
    public string DescricaoVeiculo { get; set; } = string.Empty;
    public string NrAtual { get; set; } = string.Empty;
    public string JurisdicaoAtual { get; set; } = string.Empty;
    public string TipoTermo { get; set; } = string.Empty;
    public string HeaderBase64 { get; set; } = string.Empty;
    public string FooterBase64 { get; set; } = string.Empty;
}
