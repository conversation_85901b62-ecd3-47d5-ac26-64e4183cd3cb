﻿using iText.Html2pdf;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.CartasETermos;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Generate.Custom;

public class FilaAprovacaoCETGenerateCustom(
    IPedidoCartasETermosRepository repo,
    IDatacobDapperRepository dapperDCRep,
    IConteudoTermoRepository repoConteudo
) : IFilaAprovacaoCETGenerateCustom
{
    public IPedidoCartasETermosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;
    public IConteudoTermoRepository _repoConteudo = repoConteudo;

    public ServiceResponse<byte[]> Handle(FilaAprovacaoCETGenerateCustomInput pedido, CancellationToken cancellationToken)
    {

        ServiceResponse<byte[]> response = new();
        ExceptionHelpers.SafeExecute(() =>
        {
            Dictionary<string, string> values = new()
            {
                { "ClientePrincipal", pedido.ClientePrincipal },
                { "TipoAcao", pedido.TipoAcao },
                { "AdversoPrincipal", pedido.AdversoPrincipal },
                { "GrupoCotaContrato", pedido.GrupoCotaContrato },
                { "NrParcelasVencidas", pedido.NrParcelasVencidas },
                { "ValorParcelasVencidas", pedido.ValorParcelasVencidas},
                { "MultaJuros", pedido.MultaJuros},
                { "Custas", pedido.Custas},
                { "NrParcelasVincendas", pedido.NrParcelasVincendas },
                { "ValorParcelasVincendas", pedido.ValorParcelasVincendas},
                { "Honorarios", pedido.Honorarios},
                { "Total", pedido.Total},
                { "DataBase", pedido.DataBase },
                { "QtdParcelasAcordadas", pedido.QtdParcelasAcordadas.ToString() },
                { "ValorAcordado", pedido.ValorAcordado},
                { "DescricaoVeiculo", pedido.DescricaoVeiculo },
                { "NrAtual", pedido.NrAtual },
                { "JurisdicaoAtual", pedido.JurisdicaoAtual },
                { "headerBase64", pedido.HeaderBase64 },
                { "footerBase64", pedido.FooterBase64 }
            };

            var template = StringHelpers.GenerateTemplate(pedido.Html, values);
            response.Data = PdfHelpers.GeneratePdf(template);
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
