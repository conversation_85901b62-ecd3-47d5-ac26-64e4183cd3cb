
using Microsoft.AspNetCore.Mvc;
using TelaUnica.Domain.Dtos.Response;
using UCList = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.List;
using UCCommon = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Common;
using UCCreate = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Create;
using UCRemove = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Remove;
using UCUpdate = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Update;

namespace TelaUnica.Api.Controllers.CartasETermos.Termos;

[ApiController]
[Route("api/CartasETermos/Termos/Infos")]
public class InfosTermosController(
    UCList.IInfosTermosList list,
    UCCreate.IInfosTermosCreate create,
    UCRemove.IInfosTermosRemove remove,
    UCUpdate.IInfosTermosUpdate update
) : ControllerBase
{
    private readonly UCList.IInfosTermosList _list = list;
    private readonly UCCreate.IInfosTermosCreate _create = create;
    private readonly UCRemove.IInfosTermosRemove _remove = remove;
    private readonly UCUpdate.IInfosTermosUpdate _update = update;

    [HttpGet]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.InfosTermosResponse>>>> GetList(CancellationToken cancellationToken)
        => Ok(await _list.Handle(cancellationToken));

    [HttpPost]
    public async Task<ActionResult<ServiceResponse<bool>>> Create([FromBody] UCCreate.InfosTermoCreateInput input, CancellationToken cancellationToken)
        => Ok(await _create.Handle(input, cancellationToken));

    [HttpPut]
    public async Task<ActionResult<ServiceResponse<UCCommon.InfosTermosResponse>>> Update(UCUpdate.InfosTermoUpdateInput input, CancellationToken cancellationToken)
        => Ok(await _update.Handle(input, cancellationToken));

    [HttpDelete("{id:guid}")]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.InfosTermosResponse>>>> Remove([FromRoute] Guid id, CancellationToken cancellationToken)
        => Ok(await _remove.Handle(id, cancellationToken));
}
