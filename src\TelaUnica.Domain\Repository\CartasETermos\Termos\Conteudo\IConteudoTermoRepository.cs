using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;

public interface IConteudoTermoRepository : IGenericRepository<ConteudoTermo>
{
    public Task<List<ConteudoTermo>> GetList(CancellationToken cancellationToken);
    public Task<ConteudoTermo?> GetByTipo(Guid tipoTermoId, int? grupoId, GvcRodobens? crm, CancellationToken cancellationToken);
    public Task<ConteudoTermo> GetByTipoOrDefault(Guid tipoTermoId, int? grupoId, GvcRodobens? crm, CancellationToken cancellationToken);
}
