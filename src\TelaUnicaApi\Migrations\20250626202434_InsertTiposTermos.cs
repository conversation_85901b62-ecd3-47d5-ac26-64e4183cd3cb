﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class InsertTiposTermos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                
                EXECUTE sp_executesql N'
                    DECLARE @termo1 uniqueidentifier = NEWID();
                    DECLARE @termo2 uniqueidentifier = NEWID();
                    DECLARE @termo3 uniqueidentifier = NEWID();
                    DECLARE @termo4 uniqueidentifier = NEWID();
                    DECLARE @termo5 uniqueidentifier = NEWID();
                    DECLARE @termo6 uniqueidentifier = NEWID();
                    DECLARE @termo7 uniqueidentifier = NEWID();
                    DECLARE @termo8 uniqueidentifier = NEWID();
                    DECLARE @termo9 uniqueidentifier = NEWID();

                    -- Termo 1
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo1, @nome1, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo1, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);
                    
                    -- Termo 2
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo2, @nome2, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo2, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);

                    -- Termo 3
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo3, @nome3, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo3, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);

                    -- Termo 4
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo4, @nome4, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo4, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);

                    -- Termo 5
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo5, @nome5, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo5, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);

                    -- Termo 6
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo6, @nome6, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo6, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);

                    -- Termo 7
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo7, @nome7, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo7, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);

                    -- Termo 8
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo8, @nome8, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo8, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);    

                    -- Termo 9
                    INSERT INTO TipoTermo (Id, Nome, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (@termo9, @nome9, GETDATE(), GETDATE(), NULL);

                    INSERT INTO ConteudoTermo (Id, TipoTermoId, Html, CabecalhoImg, RodapeImg, GrupoId, Crm, CreatedAt, UpdatedAt, DeletedAt)
                    VALUES (NEWID(), @termo9, @html, @empty, @empty, NULL, NULL, GETDATE(), GETDATE(), NULL);
                ', N'@nome1 varchar(max), @nome2 varchar(max), @nome3 varchar(max), @nome4 varchar(max), @nome5 varchar(max), @nome6 varchar(max), @nome7 varchar(max), @nome8 varchar(max), @nome9 varchar(max), @html varchar(max), @empty varchar(max)', 
					 'ACORDO ATUALIZAÇÃO A VISTA - BA', 'ACORDO ATUALIZAÇÃO A VISTA - EXECUÇÃO', 'ACORDO ATUALIZAÇÃO PARCELADO - BA', 'ACORDO ATUALIZAÇÃO PARCELADO - EXECUÇÃO', 'ACORDO QUITAÇÃO A VISTA - BA', 'ACORDO QUITAÇÃO A VISTA - EXECUÇÃO', 'ACORDO QUITAÇÃO PARCELADO - BA', 'ACORDO QUITAÇÃO PARCELADO - EXECUÇÃO', 'ACORDO VEICULO APREENDIDO - BA', '<p></p>', ''
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                
                EXECUTE sp_executesql N'
                    DELETE FROM ConteudoTermo 
							WHERE TipoTermoId IN (
							    SELECT Id FROM TipoTermo 
							    WHERE Nome IN (
                                    @nome1,
                                    @nome2,
                                    @nome3,
                                    @nome4,
                                    @nome5,
                                    @nome6,
                                    @nome7,
                                    @nome8,
                                    @nome9
							    )
							);
							
							-- Depois deletar os tipos de termo
							DELETE FROM TipoTermo 
							WHERE Nome IN (
                                    @nome1,
                                    @nome2,
                                    @nome3,
                                    @nome4,
                                    @nome5,
                                    @nome6,
                                    @nome7,
                                    @nome8,
                                    @nome9
							);
                ', N'@nome1 varchar(max), @nome2 varchar(max), @nome3 varchar(max), @nome4 varchar(max), @nome5 varchar(max), @nome6 varchar(max), @nome7 varchar(max), @nome8 varchar(max), @nome9 varchar(max)', 
					 'ACORDO ATUALIZAÇÃO A VISTA - BA', 'ACORDO ATUALIZAÇÃO A VISTA - EXECUÇÃO', 'ACORDO ATUALIZAÇÃO PARCELADO - BA', 'ACORDO ATUALIZAÇÃO PARCELADO - EXECUÇÃO', 'ACORDO QUITAÇÃO A VISTA - BA', 'ACORDO QUITAÇÃO A VISTA - EXECUÇÃO', 'ACORDO QUITAÇÃO PARCELADO - BA', 'ACORDO QUITAÇÃO PARCELADO - EXECUÇÃO', 'ACORDO VEICULO APREENDIDO - BA'
            ");
        }
    }
}
