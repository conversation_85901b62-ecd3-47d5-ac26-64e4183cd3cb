trigger:
  branches:
    include:
      - prod
      - hml
      - hml_v2
      - piloto
      - ws_prod
      - ws_hml
      - ws_piloto
  # Qualquer outra branch será ignorada

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  artifactName: 'drop-$(Build.SourceBranchName)'

# 📌 1️⃣ RESTAURAÇÃO API
stages:
- stage: RestoreApi
  condition: or(eq(variables['Build.SourceBranch'], 'refs/heads/prod'), eq(variables['Build.SourceBranch'], 'refs/heads/hml'), eq(variables['Build.SourceBranch'], 'refs/heads/hml_v2'), eq(variables['Build.SourceBranch'], 'refs/heads/piloto'))
  variables:
    projectPath: '**/TelaUnica.Api.csproj'
  displayName: "Restore NuGet Packages API"
  jobs:
  - job: RestoreJob
    steps:
    - task: DotNetCoreCLI@2
      inputs:
        command: 'restore'
        projects: '$(projectPath)'
        feedsToUse: 'select'
      displayName: 'Restore NuGet packages'

# 📌 1️⃣ RESTAURAÇÃO WS
- stage: RestoreWs
  condition: or(eq(variables['Build.SourceBranch'], 'refs/heads/ws_prod'), eq(variables['Build.SourceBranch'], 'refs/heads/ws_hml'), eq(variables['Build.SourceBranch'], 'refs/heads/ws_piloto'))
  variables:
    projectPath: '**/TelaUnica.Websocket.csproj'
  displayName: "Restore NuGet Packages WS"
  jobs:
  - job: RestoreJob
    steps:
    - task: DotNetCoreCLI@2
      inputs:
        command: 'restore'
        projects: '$(projectPath)'
        feedsToUse: 'select'
      displayName: 'Restore NuGet packages'

# 📌 2️⃣ BUILD DO PROJETO API  
- stage: BuildApi
  displayName: "Build the Project API"
  dependsOn:
    - RestoreApi
  condition: and(eq(dependencies.RestoreApi.result, 'Succeeded'),or(eq(variables['Build.SourceBranch'], 'refs/heads/prod'), eq(variables['Build.SourceBranch'], 'refs/heads/hml'), eq(variables['Build.SourceBranch'], 'refs/heads/hml_v2'), eq(variables['Build.SourceBranch'], 'refs/heads/piloto')))
  variables:
    projectPath: '**/TelaUnica.Api.csproj'
  jobs:
  - job: BuildJob
    steps:
    - task: DotNetCoreCLI@2
      inputs:
        command: 'build'
        projects: '$(projectPath)'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'
      displayName: 'Build the project'

# 📌 2️⃣ BUILD DO PROJETO WS  
- stage: BuildWs
  displayName: "Build the Project WS"
  dependsOn:
    - RestoreWs
  condition: and(eq(dependencies.RestoreWs.result, 'Succeeded'),or(eq(variables['Build.SourceBranch'], 'refs/heads/ws_prod'), eq(variables['Build.SourceBranch'], 'refs/heads/ws_hml'), eq(variables['Build.SourceBranch'], 'refs/heads/ws_piloto')))
  variables:
    projectPath: '**/TelaUnica.Websocket.csproj'
  jobs:
  - job: BuildJob
    steps:
    - task: DotNetCoreCLI@2
      inputs:
        command: 'build'
        projects: '$(projectPath)'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'
      displayName: 'Build the project'
    - script: |
        echo "Listing contents of $(Build.ArtifactStagingDirectory)"
        dir $(Build.ArtifactStagingDirectory)
      displayName: "Check if files exist before publishing"

# 📌 3️⃣ PUBLICAÇÃO DO PROJETO API
- stage: PublishApi
  displayName: "Publish the Project"
  dependsOn: 
    - BuildApi
  condition: eq(dependencies.BuildApi.result, 'Succeeded')
  variables:
    - group: Backend_Environment_Variables  # Conecta ao grupo de variáveis
  jobs:
  - job: PublishJob
    steps:
    - task: PowerShell@2
      displayName: "Criar appsettings.json no diretório do projeto"
      inputs:
        targetType: 'inline'
        script: |
          # Encontra o diretório do .csproj automaticamente
          $projectFile = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Recurse -Filter "TelaUnica.Api.csproj" | Select-Object -First 1
          if ($projectFile -eq $null) {
              Write-Host "Erro: Arquivo .csproj não encontrado!"
              exit 1
          }
          $projectDir = $projectFile.DirectoryName
          Write-Host "Diretório do projeto encontrado: $projectDir"
          $branchName = "$(Build.SourceBranch)".Replace("refs/heads/", "")
          Write-Host "Branch atual: $branchName"

          $Token = '$(TokenProd)'
          $TactiumToken = '$(TactiumTokenProd)'
          $Environment = 'Production'
          $DefaultConnection = '$(DefaultConnectionProd)'
          $DcRodobensConnection = '$(DcRodobensConnectionProd)'
          $DcGvcConnection = '$(DcGvcConnectionProd)'
          $DcOlosConnection = '$(DcOlosConnectionProd)'
          $MktzapStageConnection = '$(MktzapStageConnectionProd)'
          $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
          $NewconConnection = '$(NewconConnectionProd)'
          $CriptKey = '$(CriptKeyProd)'
          $CriptKeyIv = '$(CriptKeyIvProd)'
          $TimeOutLogin = '$(TimeOutLoginProd)'
          $Srv01RodoLogin = '$(Srv01RodoLoginProd)'
          $Srv02RodoLogin = '$(Srv02RodoLoginProd)'
          
          # Definição das variáveis com base na branch
          switch ($branchName) {
              "prod" {
                  $Token = '$(TokenProd)'
                  $TactiumToken = '$(TactiumTokenProd)'
                  $Environment = 'Production'
                  $DefaultConnection = '$(DefaultConnectionProd)'
                  $DcRodobensConnection = '$(DcRodobensConnectionProd)'
                  $DcGvcConnection = '$(DcGvcConnectionProd)'
                  $DcOlosConnection = '$(DcOlosConnectionProd)'
                  $MktzapStageConnection = '$(MktzapStageConnectionProd)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
                  $NewconConnection = '$(NewconConnectionProd)'
              }
              "hml" {
                  $Token = '$(TokenHml)'
                  $TactiumToken = '$(TactiumTokenHml)'
                  $Environment = 'Hml'
                  $DefaultConnection = '$(DefaultConnectionHml)'
                  $DcRodobensConnection = '$(DcRodobensConnectionHml)'
                  $DcGvcConnection = '$(DcGvcConnectionHml)'
                  $DcOlosConnection = '$(DcOlosConnectionHml)'
                  $MktzapStageConnection = '$(MktzapStageConnectionHml)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionHml)'
                  $NewconConnection = '$(NewconConnectionHml)'
                  $CriptKey = '$(CriptKeyHml)'
                  $CriptKeyIv = '$(CriptKeyIvHml)'
                  $TimeOutLogin = '$(TimeOutLoginHml)'
                  $Srv01RodoLogin = '$(Srv01RodoLoginHml)'
                  $Srv02RodoLogin = '$(Srv02RodoLoginHml)'
              }
              "hml_v2" {
                  $Token = '$(TokenHml)'
                  $TactiumToken = '$(TactiumTokenHml)'
                  $Environment = 'Hml'
                  $DefaultConnection = '$(DefaultConnectionHml)'
                  $DcRodobensConnection = '$(DcRodobensConnectionHml)'
                  $DcGvcConnection = '$(DcGvcConnectionHml)'
                  $DcOlosConnection = '$(DcOlosConnectionHml)'
                  $MktzapStageConnection = '$(MktzapStageConnectionHml)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionHml)'
                  $NewconConnection = '$(NewconConnectionHml)'
                  $CriptKey = '$(CriptKeyHml)'
                  $CriptKeyIv = '$(CriptKeyIvHml)'
                  $TimeOutLogin = '$(TimeOutLoginHml)'
                  $Srv01RodoLogin = '$(Srv01RodoLoginHml)'
                  $Srv02RodoLogin = '$(Srv02RodoLoginHml)'
              }
              "piloto" {
                  $Token = '$(TokenProd)'
                  $TactiumToken = '$(TactiumTokenProd)'
                  $Environment = 'Production'
                  $DefaultConnection = '$(DefaultConnectionProd)'
                  $DcRodobensConnection = '$(DcRodobensConnectionProd)'
                  $DcGvcConnection = '$(DcGvcConnectionProd)'
                  $DcOlosConnection = '$(DcOlosConnectionProd)'
                  $MktzapStageConnection = '$(MktzapStageConnectionProd)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
                  $NewconConnection = '$(NewconConnectionProd)'
              }
              Default {
                  Write-Host "Branch não reconhecida, abortando!"
                  exit 1
              }
          }
          # Define o conteúdo do appsettings.json
          $appSettings = @{
              "ConnectionStrings" = @{
                  "DefaultConnection" = $DefaultConnection
                  "DcRodobensConnection" = $DcRodobensConnection
                  "DcGvcConnection" = $DcGvcConnection
                  "DcOlosConnection" = $DcOlosConnection
                  "MktzapStageConnection" = $MktzapStageConnection
                  "ProjurisStageConnection" = $ProjurisStageConnection
                  "NewconConnection" = $NewconConnection
              }
              "AppSettings" = @{
                  "Token" = $Token
                  "TactiumToken" = $TactiumToken
                  "Environment" = $Environment
                  "CriptKey" = $CriptKey
                  "CriptKeyIv"= $CriptKeyIv       
                  "TimeOutLogin" = $TimeOutLogin
                  "Srv01Rodo" = $Srv01RodoLogin
                  "Srv02Rodo" = $Srv02RodoLogin                  
              }
              "Logging" = @{
                  "LogLevel" = @{
                      "Default" = "Information"
                      "Microsoft.AspNetCore" = "Warning"
                  }
              }
              "AllowedHosts" = "*"
          }
          # Converte o conteúdo para JSON
          $jsonContent = $appSettings | ConvertTo-Json -Depth 10
          # Define o caminho dentro do diretório do projeto
          $filePath = "$projectDir/appsettings.json"
          # Salva o arquivo JSON dentro da pasta do projeto
          $jsonContent | Set-Content -Path $filePath -Encoding utf8
          Write-Host "Arquivo appsettings.json criado em: $filePath"
    
    - task: DotNetCoreCLI@2
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: '**/TelaUnica.Api.csproj'
        arguments: '--configuration $(buildConfiguration) --runtime win-x64 --self-contained true --output $(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true
      displayName: 'Publish the project'
    
    - script: |
        echo "Listing contents of $(Build.ArtifactStagingDirectory)"
        dir $(Build.ArtifactStagingDirectory)
      displayName: "Check if files exist before publishing"

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts: drop'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/TelaUnicaApi.zip'
        ArtifactName: 'drop-$(Build.SourceBranchName)'
        publishLocation: 'Container'

# 📌 3️⃣ PUBLICAÇÃO DO PROJETO WS
- stage: PublishWs
  displayName: "Publish the Project"
  dependsOn: 
    - BuildWs
  condition: eq(dependencies.BuildWs.result, 'Succeeded')
  variables:
    - group: Backend_Environment_Variables  # Conecta ao grupo de variáveis
  jobs:
  - job: PublishJob
    steps:
    - task: PowerShell@2
      displayName: "Criar appsettings.json no diretório do projeto"
      inputs:
        targetType: 'inline'
        script: |
          # Encontra o diretório do .csproj automaticamente
          $projectFile = Get-ChildItem -Path "$(Build.SourcesDirectory)" -Recurse -Filter "TelaUnica.Websocket.csproj" | Select-Object -First 1
          if ($projectFile -eq $null) {
              Write-Host "Erro: Arquivo .csproj não encontrado!"
              exit 1
          }
          $projectDir = $projectFile.DirectoryName
          Write-Host "Diretório do projeto encontrado: $projectDir"
          $branchName = "$(Build.SourceBranch)".Replace("refs/heads/", "")
          Write-Host "Branch atual: $branchName"

          $Token = '$(TokenProd)'
          $TactiumToken = '$(TactiumTokenProd)'
          $Environment = 'Production'
          $DefaultConnection = '$(DefaultConnectionProd)'
          $DcRodobensConnection = '$(DcRodobensConnectionProd)'
          $DcGvcConnection = '$(DcGvcConnectionProd)'
          $DcOlosConnection = '$(DcOlosConnectionProd)'
          $MktzapStageConnection = '$(MktzapStageConnectionProd)'
          $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
          $NewconConnection = '$(NewconConnectionProd)'
          $CriptKey = '$(CriptKeyProd)'
          $CriptKeyIv = '$(CriptKeyIvProd)'
          
          # Definição das variáveis com base na branch
          switch ($branchName) {
              "prod" {
                  $Token = '$(TokenProd)'
                  $TactiumToken = '$(TactiumTokenProd)'
                  $Environment = 'Production'
                  $DefaultConnection = '$(DefaultConnectionProd)'
                  $DcRodobensConnection = '$(DcRodobensConnectionProd)'
                  $DcGvcConnection = '$(DcGvcConnectionProd)'
                  $DcOlosConnection = '$(DcOlosConnectionProd)'
                  $MktzapStageConnection = '$(MktzapStageConnectionProd)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
                  $NewconConnection = '$(NewconConnectionProd)'
              }
              "ws_prod" {
                  $Token = '$(TokenProd)'
                  $TactiumToken = '$(TactiumTokenProd)'
                  $Environment = 'Production'
                  $DefaultConnection = '$(DefaultConnectionProd)'
                  $DcRodobensConnection = '$(DcRodobensConnectionProd)'
                  $DcGvcConnection = '$(DcGvcConnectionProd)'
                  $DcOlosConnection = '$(DcOlosConnectionProd)'
                  $MktzapStageConnection = '$(MktzapStageConnectionProd)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
                  $NewconConnection = '$(NewconConnectionProd)'
              }
              "hml" {
                  $Token = '$(TokenHml)'
                  $TactiumToken = '$(TactiumTokenHml)'
                  $Environment = 'Hml'
                  $DefaultConnection = '$(DefaultConnectionHml)'
                  $DcRodobensConnection = '$(DcRodobensConnectionHml)'
                  $DcGvcConnection = '$(DcGvcConnectionHml)'
                  $DcOlosConnection = '$(DcOlosConnectionHml)'
                  $MktzapStageConnection = '$(MktzapStageConnectionHml)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionHml)'
                  $NewconConnection = '$(NewconConnectionHml)'
                  $CriptKey = '$(CriptKeyHml)'
                  $CriptKeyIv = '$(CriptKeyIvHml)'
              }
              "hml_v2" {
                  $Token = '$(TokenHml)'
                  $TactiumToken = '$(TactiumTokenHml)'
                  $Environment = 'Hml'
                  $DefaultConnection = '$(DefaultConnectionHml)'
                  $DcRodobensConnection = '$(DcRodobensConnectionHml)'
                  $DcGvcConnection = '$(DcGvcConnectionHml)'
                  $DcOlosConnection = '$(DcOlosConnectionHml)'
                  $MktzapStageConnection = '$(MktzapStageConnectionHml)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionHml)'
                  $NewconConnection = '$(NewconConnectionHml)'
                  $CriptKey = '$(CriptKeyHml)'
                  $CriptKeyIv = '$(CriptKeyIvHml)'
              }
              "ws_hml" {
                  $Token = '$(TokenHml)'
                  $TactiumToken = '$(TactiumTokenHml)'
                  $Environment = 'Hml'
                  $DefaultConnection = '$(DefaultConnectionHml)'
                  $DcRodobensConnection = '$(DcRodobensConnectionHml)'
                  $DcGvcConnection = '$(DcGvcConnectionHml)'
                  $DcOlosConnection = '$(DcOlosConnectionHml)'
                  $MktzapStageConnection = '$(MktzapStageConnectionHml)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionHml)'
                  $NewconConnection = '$(NewconConnectionHml)'
              }
              "piloto" {
                  $Token = '$(TokenProd)'
                  $TactiumToken = '$(TactiumTokenProd)'
                  $Environment = 'Production'
                  $DefaultConnection = '$(DefaultConnectionProd)'
                  $DcRodobensConnection = '$(DcRodobensConnectionProd)'
                  $DcGvcConnection = '$(DcGvcConnectionProd)'
                  $DcOlosConnection = '$(DcOlosConnectionProd)'
                  $MktzapStageConnection = '$(MktzapStageConnectionProd)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
                  $NewconConnection = '$(NewconConnectionProd)'
              }
              "ws_piloto" {
                  $Token = '$(TokenProd)'
                  $TactiumToken = '$(TactiumTokenProd)'
                  $Environment = 'Production'
                  $DefaultConnection = '$(DefaultConnectionProd)'
                  $DcRodobensConnection = '$(DcRodobensConnectionProd)'
                  $DcGvcConnection = '$(DcGvcConnectionProd)'
                  $DcOlosConnection = '$(DcOlosConnectionProd)'
                  $MktzapStageConnection = '$(MktzapStageConnectionProd)'
                  $ProjurisStageConnection = '$(ProjurisStageConnectionProd)'
                  $NewconConnection = '$(NewconConnectionProd)'
              }
              Default {
                  Write-Host "Branch não reconhecida, abortando!"
                  exit 1
              }
          }
          # Define o conteúdo do appsettings.json
          $appSettings = @{
              "ConnectionStrings" = @{
                  "DefaultConnection" = $DefaultConnection
                  "DcRodobensConnection" = $DcRodobensConnection
                  "DcGvcConnection" = $DcGvcConnection
                  "DcOlosConnection" = $DcOlosConnection
                  "MktzapStageConnection" = $MktzapStageConnection
                  "ProjurisStageConnection" = $ProjurisStageConnection
                  "NewconConnection" = $NewconConnection
              }
              "AppSettings" = @{
                  "Token" = $Token
                  "TactiumToken" = $TactiumToken
                  "Environment" = $Environment
                  "CriptKey" = $CriptKey
                  "CriptKeyIv"= $CriptKeyIv										   
              }
              "Logging" = @{
                  "LogLevel" = @{
                      "Default" = "Information"
                      "Microsoft.AspNetCore" = "Warning"
                  }
              }
              "AllowedHosts" = "*"
          }
          # Converte o conteúdo para JSON
          $jsonContent = $appSettings | ConvertTo-Json -Depth 10
          # Define o caminho dentro do diretório do projeto
          $filePath = "$projectDir/appsettings.json"
          # Salva o arquivo JSON dentro da pasta do projeto
          $jsonContent | Set-Content -Path $filePath -Encoding utf8
          Write-Host "Arquivo appsettings.json criado em: $filePath"
    
    - script: |
        echo "Listing contents of $(Build.ArtifactStagingDirectory)"
        dir $(Build.ArtifactStagingDirectory)

    - task: DotNetCoreCLI@2
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: '**/TelaUnica.Websocket.csproj'
        arguments: '--configuration $(buildConfiguration) --runtime win-x64 --self-contained true --output $(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true
      displayName: 'Publish the project'
    - script: |
        echo "Listing contents of $(Build.ArtifactStagingDirectory)"
        dir $(Build.ArtifactStagingDirectory)
      displayName: "Check if files exist before publishing"

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts: drop'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/TelaUnica.Websocket.zip'
        ArtifactName: 'drop-$(Build.SourceBranchName)'
        publishLocation: 'Container'

# 📌 6 Sonar Analysis
- stage: SonarQubeAnalysis
  displayName: "SonarQube Analysis"
  dependsOn:
    - PublishApi
    - PublishWs
  condition: or(
    in(dependencies.PublishApi.result, 'Succeeded', 'Skipped'),
    in(dependencies.PublishWs.result, 'Succeeded', 'Skipped')
    )
  jobs:
    - job: SonarQube
      displayName: "Run SonarQube Analysis"
      steps:
        - task: SonarQubePrepare@7
          inputs:
            SonarQube: 'GubolincoSonar24.12.0.100206'
            scannerMode: 'dotnet'
            projectKey: 'GVC-Tela-Unica-Backend'
          continueOnError: true
        # 🔥 Build necessário para Sonar funcionar corretamente
        - task: DotNetCoreCLI@2
          displayName: 'Build project for Sonar'
          inputs:
            command: 'build'
            projects: '**/*.sln'
            arguments: '--configuration Release'
        - task: SonarQubeAnalyze@7
          inputs:
            jdkversion: 'JAVA_HOME_17_X64'
          condition: succeededOrFailed()  # Executa mesmo se o Prepare falhar
          continueOnError: true
        - task: SonarQubePublish@7
          inputs:
            pollingTimeoutSec: '300'
          condition: succeededOrFailed()  # Executa mesmo se o Prepare falhar
          continueOnError: true
        
        - script: echo "Ignoring SonarQube failures"
          condition: always()

