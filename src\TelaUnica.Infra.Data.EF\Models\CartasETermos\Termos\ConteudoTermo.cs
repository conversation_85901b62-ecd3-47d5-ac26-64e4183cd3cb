using System.ComponentModel.DataAnnotations.Schema;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

public class ConteudoTermo : Entity
{
    [ForeignKey("TipoTermo")]
    public Guid TipoTermoId { get; set; }
    public virtual TipoTermo? TipoTermo { get; set; }

    [Column(TypeName = "text")]
    public string Html { get; set; } = string.Empty;

    [Column(TypeName = "text")]
    public string CabecalhoImg { get; set; } = string.Empty;

    [Column(TypeName = "text")]
    public string RodapeImg { get; set; } = string.Empty;
    public int? GrupoId { get; set; }
    public GvcRodobens? Crm { get; set; }
}
