using System;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;
using TelaUnica.Domain.Dtos.Response;
using Models = TelaUnica.Infra.Data.EF.Models;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Get;

public interface IFilaAprovacaoCartasETermosGet
{
    public Task<ServiceResponse<List<ItemFilaAprovacaoCartasETermosResponse>>> Handle(CancellationToken cancellationToken);
}
