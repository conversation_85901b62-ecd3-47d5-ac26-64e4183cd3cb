using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Common;

public record ConteudoTermosResponse(Guid Id, Guid TipoTermoId, string Html, string CabecalhoImg, string RodapeImg, int? GrupoId, GvcRodobens? Crm, DateTime CreatedAt)
{
    public static ConteudoTermosResponse FromModel(ConteudoTermo model)
    {
        return new(model.Id, model.TipoTermoId, model.Html, model.CabecalhoImg, model.RodapeImg, model.GrupoId, model.Crm, model.CreatedAt);
    }
    public static ConteudoTermosResponse FromModelSimplified(ConteudoTermo model)
    {
        return new(model.Id, model.TipoTermoId, model.Html, string.Empty, string.Empty, model.GrupoId, model.Crm, model.CreatedAt);
    }
}