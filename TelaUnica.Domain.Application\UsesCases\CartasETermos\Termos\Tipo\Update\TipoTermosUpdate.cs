using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Common;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Update;

public class TipoTermosUpdate(
    ITipoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : ITipoTermosUpdate
{
    public ITipoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<TipoTermosResponse>> Handle(TipoTermoUpdateInput input, CancellationToken cancellationToken)
    {
        ServiceResponse<TipoTermosResponse> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = await _repo.Get(input.Id, cancellationToken);
            model.Nome = input.Nome;
            await _repo.Update(model, cancellationToken);
            response.Data = TipoTermosResponse.FromModel(model);
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
