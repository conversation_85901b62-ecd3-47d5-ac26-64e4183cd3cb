using System.Text.RegularExpressions;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;
namespace TelaUnica.Domain.Utils;
public static class StringHelpers {
    public static string GenerateTemplate(string template, Dictionary<string, string> parameters) {
        if (string.IsNullOrEmpty(template) || parameters == null)
            return template;
        string result = template;
        foreach (var param in parameters)
            result = result.Replace("{"+param.Key+"}", param.Value);
        
        return result;
    }
    public static string GenerateTemplateParcelasTermo(List<PedidoTermoParcelas>? parcelas) {
        string templateParcelas = @"
				<tr style=""height:15pt"">
					<td style=""width:70.65pt; border-right:1pt solid #000000; padding:0pt 3pt; vertical-align:bottom"">
						<p style=""margin-bottom:0pt; text-align:center; line-height:115%; font-size:12pt"">
							{numero}
						</p>
					</td>
					<td style=""width:78.05pt; border-right:1pt solid #000000; padding:0pt 3pt 0pt 3.5pt; vertical-align:bottom"">
						<p style=""margin-bottom:0pt; text-align:center; line-height:115%; font-size:12pt"">
							{vencimento}
						</p>
					</td>
					<td style=""width:81.35pt; padding:0pt 3pt 0pt 3.5pt; vertical-align:bottom"">
						<p style=""margin-bottom:0pt; text-align:center; line-height:115%; font-size:12pt"">
							{valor}
						</p>
					</td>
				</tr>";
        
        string result = "";
        if (parcelas == null || parcelas.Count == 0) 
            return result;

        foreach (var parcela in parcelas.OrderBy(x => x.Numero))
        {
            result += GenerateTemplate(templateParcelas, new Dictionary<string, string>()
            {
                { "numero", parcela.Numero.ToString() },
                { "vencimento", parcela.Vencimento.ToString("dd/MM/yyyy") },
                { "valor", parcela.Valor.ToString("C2") }
            });
        }

        return result;
    }
}








