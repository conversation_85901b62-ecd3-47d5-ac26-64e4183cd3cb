using Microsoft.AspNetCore.Http;
using TelaUnica.Infra.Data.EF.Enuns;
using Models = TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Update;

public class ConteudoTermoUpdateInput
{
    public Guid Id { get; set; }
    public string? Html { get; set; }
    public IFormFile? CabecalhoImg { get; set; }
    public IFormFile? RodapeImg { get; set; }
    public int? GrupoId { get; set; }
    public GvcRodobens? Crm { get; set; }

}