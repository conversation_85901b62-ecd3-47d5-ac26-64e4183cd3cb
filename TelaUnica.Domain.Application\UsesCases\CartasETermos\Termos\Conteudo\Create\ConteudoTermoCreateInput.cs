using Microsoft.AspNetCore.Http;
using TelaUnica.Infra.Data.EF.Enuns;
using Models = TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Create;

public class ConteudoTermoCreateInput
{
    public Guid TipoTermoId { get; set; }
    public string Html { get; set; } = string.Empty;
    public IFormFile? CabecalhoImg { get; set; }
    public IFormFile? RodapeImg { get; set; }
    public int? GrupoId { get; set; }
    public GvcRodobens? Crm { get; set; }

    public Models.ConteudoTermo ToModel() => new()
    {
        TipoTermoId = TipoTermoId,
        Html = Html,
        CabecalhoImg = string.Empty,
        RodapeImg = string.Empty,
        GrupoId = GrupoId,
        Crm = Crm
    };

    public void Validate()
    {
        if(TipoTermoId == Guid.Empty)
            throw new("TipoTermoId não pode ser vazio");
    }
}