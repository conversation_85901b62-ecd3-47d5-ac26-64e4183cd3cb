
using Microsoft.AspNetCore.Mvc;
using TelaUnica.Domain.Dtos.Response;
using UCList = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.List;
using UCCommon = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Common;
using UCCreate = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Create;
using UCRemove = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Remove;
using UCUpdate = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Update;

namespace TelaUnica.Api.Controllers.CartasETermos.Termos;

[ApiController]
[Route("api/CartasETermos/Termos/Tipo")]
public class TipoTermosController(
    UCList.ITipoTermosList list,
    UCCreate.ITipoTermosCreate create,
    UCRemove.ITipoTermosRemove remove,
    UCUpdate.ITipoTermosUpdate update
) : ControllerBase
{
    private readonly UCList.ITipoTermosList _list = list;
    private readonly UCCreate.ITipoTermosCreate _create = create;
    private readonly UCRemove.ITipoTermosRemove _remove = remove;
    private readonly UCUpdate.ITipoTermosUpdate _update = update;

    [HttpGet]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.TipoTermosResponse>>>> GetList(CancellationToken cancellationToken)
        => Ok(await _list.Handle(cancellationToken));

    [HttpPost]
    public async Task<ActionResult<ServiceResponse<bool>>> Create(UCCreate.TipoTermoCreateInput input, CancellationToken cancellationToken)
        => Ok(await _create.Handle(input, cancellationToken));

    [HttpPut]
    public async Task<ActionResult<ServiceResponse<UCCommon.TipoTermosResponse>>> Update(UCUpdate.TipoTermoUpdateInput input, CancellationToken cancellationToken)
        => Ok(await _update.Handle(input, cancellationToken));

    [HttpDelete("{id:guid}")]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.TipoTermosResponse>>>> Remove([FromRoute] Guid id, CancellationToken cancellationToken)
        => Ok(await _remove.Handle(id, cancellationToken));
}
