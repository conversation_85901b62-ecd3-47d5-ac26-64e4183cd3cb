using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Common;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Remove;

public class ConteudoTermosRemove(
    IConteudoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IConteudoTermosRemove
{
    public IConteudoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<List<ConteudoTermosResponse>>> Handle(Guid id, CancellationToken cancellationToken)
    {
        ServiceResponse<List<ConteudoTermosResponse>> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = await _repo.Get(id, cancellationToken);
            await _repo.Delete(model, cancellationToken);
            response.Data = [.. (await _repo.GetList(cancellationToken)).Select(x => ConteudoTermosResponse.FromModel(x))];
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
