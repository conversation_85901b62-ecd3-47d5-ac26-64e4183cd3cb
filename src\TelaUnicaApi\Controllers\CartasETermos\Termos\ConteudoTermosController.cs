
using Microsoft.AspNetCore.Mvc;
using TelaUnica.Domain.Dtos.Response;
using UCList = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.List;
using UCListSimplified = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.ListSimplified;
using UCCommon = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Common;
using UCCreate = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Create;
using UCRemove = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Remove;
using UCUpdate = TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Update;

namespace TelaUnica.Api.Controllers.CartasETermos.Termos;

[ApiController]
[Route("api/CartasETermos/Termos/Conteudo")]
public class ConteudoTermosController(
    UCList.IConteudoTermosList list,
    UCListSimplified.IConteudoTermosListSimplified listSimplified,
    UCCreate.IConteudoTermosCreate create,
    UCRemove.IConteudoTermosRemove remove,
    UCUpdate.IConteudoTermosUpdate update
) : ControllerBase
{
    private readonly UCList.IConteudoTermosList _list = list;
    private readonly UCListSimplified.IConteudoTermosListSimplified _listSimplified = listSimplified;
    private readonly UCCreate.IConteudoTermosCreate _create = create;
    private readonly UCRemove.IConteudoTermosRemove _remove = remove;
    private readonly UCUpdate.IConteudoTermosUpdate _update = update;

    [HttpGet]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.ConteudoTermosResponse>>>> GetList(CancellationToken cancellationToken)
        => Ok(await _list.Handle(cancellationToken));
        
    [HttpGet("Simplified")]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.ConteudoTermosResponse>>>> GetListSimplified(CancellationToken cancellationToken)
        => Ok(await _listSimplified.Handle(cancellationToken));

    [HttpPost]
    public async Task<ActionResult<ServiceResponse<bool>>> Create([FromForm] UCCreate.ConteudoTermoCreateInput input, CancellationToken cancellationToken)
        => Ok(await _create.Handle(input, cancellationToken));

    [HttpPut]
    public async Task<ActionResult<ServiceResponse<UCCommon.ConteudoTermosResponse>>> Update([FromForm] UCUpdate.ConteudoTermoUpdateInput input, CancellationToken cancellationToken)
        => Ok(await _update.Handle(input, cancellationToken));

    [HttpDelete("{id:guid}")]
    public async Task<ActionResult<ServiceResponse<List<UCCommon.ConteudoTermosResponse>>>> Remove([FromRoute] Guid id, CancellationToken cancellationToken)
        => Ok(await _remove.Handle(id, cancellationToken));
}
