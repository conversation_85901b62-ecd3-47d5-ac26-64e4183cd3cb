﻿using iText.Html2pdf;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.CartasETermos;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Generate.ByPedido;

public class FilaAprovacaoCETGenerateByPedido(
    IPedidoCartasETermosRepository repo,
    IDatacobDapperRepository dapperDCRep,
    IConteudoTermoRepository repoConteudo
) : IFilaAprovacaoCETGenerateByPedido
{
    public IPedidoCartasETermosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;
    public IConteudoTermoRepository _repoConteudo = repoConteudo;

    public async Task<ServiceResponse<byte[]>> Handle(Guid pedidoId, CancellationToken cancellationToken)
    {

        ServiceResponse<byte[]> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var pedido = await _repo.GetPedido(pedidoId, cancellationToken) ?? throw new("Pedido não encontrado.");
            if (pedido.Status != "Aprovado") throw new("Pedido não aprovado.");
            if (pedido.PedidoTermoInfos is null) throw new("Pedido não possui informações de termos.");
            if (pedido.PedidoTermoInfos.TipoTermo is null) throw new("Pedido não possui tipo de termos.");

            var conteudo = await _repoConteudo.GetByTipoOrDefault(pedido.PedidoTermoInfos.TipoTermo.Id, pedido.IdGrupo, pedido.Crm, cancellationToken);
            Dictionary<string, string> values = new()
            {
                { "ClientePrincipal", pedido.PedidoTermoInfos.ClientePrincipal },
                { "TipoAcao", pedido.PedidoTermoInfos.TipoAcao },
                { "AdversoPrincipal", pedido.PedidoTermoInfos.AdversoPrincipal },
                { "GrupoCotaContrato", pedido.PedidoTermoInfos.GrupoCotaContrato },
                { "NrParcelasVencidas", pedido.PedidoTermoInfos.NrParcelasVencidas },
                { "ValorParcelasVencidas", pedido.PedidoTermoInfos.ValorParcelasVencidas.ToString("C2") },
                { "MultaJuros", pedido.PedidoTermoInfos.MultaJuros.ToString("C2") },
                { "Custas", pedido.PedidoTermoInfos.Custas.ToString("C2") },
                { "NrParcelasVincendas", pedido.PedidoTermoInfos.NrParcelasVincendas ?? "" },
                { "ValorParcelasVincendas", pedido.PedidoTermoInfos.ValorParcelasVincendas == null ? (pedido.PedidoTermoInfos.ValorParcelasVincendas ?? 0).ToString("C2") : "" },
                { "Honorarios", pedido.PedidoTermoInfos.Honorarios.ToString("C2") },
                { "Total", pedido.PedidoTermoInfos.Total.ToString("C2") },
                { "DataBase", pedido.PedidoTermoInfos.DataBase.ToString("dd/MM/yyyy") },
                { "QtdParcelasAcordadas", pedido.PedidoTermoInfos.QtdParcelasAcordadas.ToString() },
                { "ValorAcordado", pedido.PedidoTermoInfos.ValorAcordado.ToString("C2") },
                { "DescricaoVeiculo", pedido.PedidoTermoInfos.DescricaoVeiculo ?? "" },
                { "NrAtual", pedido.PedidoTermoInfos.NrAtual },
                { "JurisdicaoAtual", pedido.PedidoTermoInfos.JurisdicaoAtual },
                { "HeaderBase64", conteudo.CabecalhoImg },
                { "FooterBase64", conteudo.RodapeImg },
                { "Parcelas", StringHelpers.GenerateTemplateParcelasTermo(pedido.PedidoTermoInfos.PedidoTermoParcelas) }
            };

            var template = StringHelpers.GenerateTemplate(conteudo.Html, values);
            response.Data = PdfHelpers.GeneratePdf(template);
        }, (ex) => response.SetFailure(ex.Message, []));
        return response;
    }
}
