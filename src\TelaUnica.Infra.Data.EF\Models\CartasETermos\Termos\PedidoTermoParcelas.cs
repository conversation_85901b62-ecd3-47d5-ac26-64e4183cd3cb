using System;
using System.ComponentModel.DataAnnotations.Schema;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

public class PedidoTermoParcelas : Entity
{

    [ForeignKey("Infos")]
    public Guid InfosId { get; set; }
    public virtual PedidoTermoInfos? Infos { get; set; }

    public int Numero { get; set; }
    public DateOnly Vencimento { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal Valor { get; set; }

}
