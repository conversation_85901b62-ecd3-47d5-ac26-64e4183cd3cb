using System;
using System.ComponentModel.DataAnnotations.Schema;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

public class TipoTermo : Entity
{
    [Column(TypeName = "varchar(255)")]
    public string Nome { get; set; } = string.Empty;
    public virtual List<ConteudoTermo>? ConteudoTermo { get; set; }
}
