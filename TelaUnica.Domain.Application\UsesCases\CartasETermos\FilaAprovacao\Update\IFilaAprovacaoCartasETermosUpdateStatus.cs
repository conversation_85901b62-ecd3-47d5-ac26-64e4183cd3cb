﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;
using TelaUnica.Domain.Dtos.Response;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Update
{
    public interface IFilaAprovacaoCartasETermosUpdateStatus
    {
        public Task<ServiceResponse<bool>> Handle(Guid Id, string status, CancellationToken cancellationToken);
    }
}
