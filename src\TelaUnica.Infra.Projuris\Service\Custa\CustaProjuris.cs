﻿using TelaUnica.Domain.Interfaces;
using TelaUnica.Domain.Repository.Service;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Projuris.Dtos.Response.Custas;
using TelaUnica.Infra.Projuris.Service.Auth;
using TelaUnica.Infra.Projuris.Service.XmlModels;

namespace TelaUnica.Infra.Projuris.Service.Custa;

public class CustaProjuris
{

    private readonly IConfigRepository _config;
    private readonly ApiHelpers _helper;
    private readonly IServiceRepository _service;
    private readonly AuthProjuris _authProjuris;
    public CustaProjuris(IConfigRepository config, ApiHelpers helper, IServiceRepository service)
    {
        _config = config;
        _helper = helper;
        _service = service;
        _authProjuris = new(config, helper, service);
    }

    public async Task<List<ListCustasResponse>> Execute(string pasta)
    {
        var token = await _authProjuris.Execute();

        var endpoint = await _service.GetByName("ProjurisCusta", Data.EF.Enuns.ApiType.Barramento, CancellationToken.None);

        var api = await _helper.GetApiObjXml<CustaPayload, ElementEnvelope<ElementCustaBody>>($"{token.Url}{endpoint.Api}", new()
        {
           FiltroFinal = pasta,
           FiltroInicial = pasta,
           Token = token.Token
        });
        List<ListCustasResponse> result = new();
        try
        {
            if (api?.Envelope?.Lista != null)
                foreach (var item in api?.Envelope?.Lista)
                    result.Add(new ListCustasResponse()
                    {
                        Pasta = item.Alias,
                        Lancamento = item.IdContaNomeTipoConta,
                        Pago = item.Pago == "F" ? "Não" : "Sim",
                        Valor_Devido = item.ValorDevido,
                        Valor_Pago = item.ValorRealizado != "" ? decimal.Parse(item.ValorRealizado ?? "0.00") : 0.00m,
                        Data_De_Lancamento = item.DataCadastro != null ? DateTime.Parse(item.DataCadastro) : DateTime.Now,
                    });
        }
        catch{

        }

        return result;
    }
}
