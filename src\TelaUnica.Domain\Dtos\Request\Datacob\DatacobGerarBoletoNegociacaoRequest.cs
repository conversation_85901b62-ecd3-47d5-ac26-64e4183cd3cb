using TelaUnica.Domain.Enums;
using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Dtos.Request.Datacob;

public class DatacobGerarBoletoNegociacaoRequest
{
    public int IdAgrupamento { get; set; }
    public DateTime DtNegociacao { get; set; }
    public decimal VlNegociado { get; set; }
    public List<int>? Parcelas { get; set; }
    public string? Email { get; set; }
    public string? Ddd { get; set; }
    public string? Fone { get; set; }
    public int? FormaDesconto { get; set; }
    public string? Mensagem { get; set; }
    public int? UsuarioNegociacao { get; set; }
    public TipoEnvio TipoEnvio { get; set; } = TipoEnvio.Impressao;
    public GvcRodobens? Crm { get; set; }
}