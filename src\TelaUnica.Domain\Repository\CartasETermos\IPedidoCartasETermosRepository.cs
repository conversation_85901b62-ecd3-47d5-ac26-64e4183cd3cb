using System;
using TelaUnica.Infra.Data.EF.Models.CartasETermos;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Domain.Repository.CartasETermos;

public interface IPedidoCartasETermosRepository : IGenericRepository<PedidoCartasETermos>
{
    Task<PedidoCartasETermos?> GetPedido(Guid id, CancellationToken cancellationToken);
    public Task<List<PedidoCartasETermos>> GetList(CancellationToken cancellationToken);
    public Task<bool> UpdateStatus(Guid Id, string status, CancellationToken cancellationToken);
}
