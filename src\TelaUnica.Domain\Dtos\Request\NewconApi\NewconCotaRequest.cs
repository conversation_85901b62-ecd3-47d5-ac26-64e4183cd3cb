﻿namespace TelaUnica.Domain.Dtos.Request.NewconApi;
public class NewconCotaRequest
{
    public string Id { get; set; } = string.Empty;
    public int NewconEmpresaId { get; set; }
    public int NewconUnidadeId { get; set; }
    public string Contrato { get; set; } = string.Empty;
    public string TipoCota { get; set; } = string.Empty;
    public DateTime? DataNascimento { get; set; }
    public int NewconClienteId { get; set; }
    public string CnpjCpf { get; set; } = string.Empty;
    public DateTime? DataAdesao { get; set; }
    public DateTime DataCadastro { get; set; }
    public DateTime DataAlocacao { get; set; }
    public DateTime DataVenda { get; set; }
    public int? NewconAssembleiaIdPrimeira { get; set; }
    public int PrazoPlano { get; set; }
    public string TipoVenda { get; set; } = string.Empty;
    public int NewconPlanoVendaId { get; set; }
    public int? NewconBemObjetoId { get; set; }
    public int NewconFilialVendaId { get; set; }
    public int NewconFilialAdmId { get; set; }
    public int? NewconPontoVendaId { get; set; }
    public int? NewconPontoEntregaId { get; set; }
    public decimal? ValorBem { get; set; }
    public decimal? TaxaAdminstracao { get; set; }
    public decimal? TaxaAdesao { get; set; }
    public decimal? FundoReserva { get; set; }
    public decimal? PercentualCont { get; set; }
    public int? NewconContemplacaoId { get; set; }
    public int? NewconDesclassificacaoId { get; set; }
    public int? NewconAssembleiaIdAtual { get; set; }
    public int? NewconDifGrupoId { get; set; }
    public decimal? ValoresPago { get; set; }
    public decimal? ValoresPagoPercentual { get; set; }
    public decimal? SaldoDevedor { get; set; }
    public decimal? SaldoDevedorPercentual { get; set; }
    public decimal? Atraso { get; set; }
    public decimal? ArasoPercentual { get; set; }
    public decimal? DiferencaParcela { get; set; }
    public decimal? DiferencaParcelaPercentual { get; set; }
    public decimal? ValorParcela { get; set; }
    public decimal? ValorParcelaPercentual { get; set; }
    public int? QuantidadeParcelaPaga { get; set; }
    public int? QuantidadeParcelaFuro { get; set; }
    public int? LanceMinimo { get; set; }
    public int? LanceMaximo { get; set; }
    public DateTime DataSicronizacao { get; set; } = new DateTime();
    public virtual NewconEmpresaCreateRequest NewconEmpresa { get; set; } = new();
    public virtual NewconUnidadeCreateRequest NewconUnidade { get; set; } = new();
    public virtual NewconAssembleiaCreateRequest NewconAssembleia { get; set; } = new();
    public virtual NewconAssembleiaCreateRequest NewconAssembleiaAtual { get; set; } = new();
    public virtual NewconPlanoVendaCreateRequest NewconPlanoVenda { get; set; } = new();
    public virtual NewconBemObjetoCreateRequest? NewconBemObjeto { get; set; }
    public virtual NewconFilialCreateRequest NewconFilialVenda { get; set; } = new();
    public virtual NewconFilialCreateRequest NewconFilialAdm { get; set; } = new();
    public virtual NewconPontoVendaCreateRequest NewconPontoVenda { get; set; } = new();
    public virtual NewconPontoEntregaCreateRequest? NewconPontoEntrega { get; set; }
    public virtual NewconContemplacaoCreateRequest? NewconContemplacao { get; set; }
    public virtual NewconDesclassificacaoCreateRequest? NewconDesclassificacao { get; set; }
    public virtual NewconDifGrupoCreateRequest? NewconDifGrupo { get; set; }
    public virtual NewconClienteCreateRequest NewconCliente { get; set; } = new();
    public virtual NewconComissionadoCreateRequest NewconComissionado { get; set; } = new();
    public virtual NewconEquipeVendaCreateRequest NewconEquipeVenda { get; set; } = new();
    public virtual NewconRecuperacaoCreateRequest NewconRecuperacao { get; set; } = new();
    public virtual NewconSuspensaoCreateRequest NewconSuspensao { get; set; } = new();

    //Cotas
    public virtual List<NewconCotaDadoCotaCreateRequest> NewconCotaDadoCotas { get; set; } = [];
    //Envolvidos
    public virtual List<NewconCotaEnvolvidoCreateRequest> NewconCotaEnvolvidos { get; set; } = [];
    //Titularidade
    public virtual List<NewconCotaTitularidadeCreateRequest> NewconCotaTitularidades { get; set; } = [];
    //Representante
    public virtual List<NewconCotaRepresentanteCreateRequest> NewconCotaRepresentantes { get; set; } = [];
    //Reportavel
    public virtual List<NewconCotaReportavelCreateRequest> NewconCotaReportaveis { get; set; } = [];
    //Negociacao                                                                                 
    public virtual List<NewconCotaNegociacaoCreateRequest> NewconCotaNegociacoes { get; set; } = [];
    public virtual List<NewconCotaValoresPagoCreateRequest> NewconCotaValoresPagos { get; set; } = [];
    public virtual NewconCotaSaldoDevedorRequest NewconCotaSaldoDevedor { get; set; } = new();
    public virtual List<NewconCotaAtrasoCreateRequest> NewconCotaAtrasos { get; set; } = [];
    public virtual NewconCotaDifParcelaRequest NewconCotaSaldoDifParcela { get; set; } = new();
    public virtual NewconCotaValorParcelaRequest NewconCotaValorParcela { get; set; } = new();
    public virtual List<NewconCotaAgendaCreateRequest> NewconCotaAgendas { get; set; } = [];

    public bool Active { get; set; }
}