using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Data;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Repository.CartasETermos.Termos.PedidoInfos;

public class PedidoTermoInfosRepository(DataContext context, IHttpContextAccessor httpContextAccessor) : IPedidoTermoInfosRepository
{
    private readonly DataContext _context = context;
    public readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public async Task Delete(PedidoTermoInfos aggregate, CancellationToken cancellationToken)
    {
        aggregate.Delete();
        _context.PedidoTermoInfos.Update(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<PedidoTermoInfos> Get(Guid id, CancellationToken cancellationToken)
    {
        return await _context.PedidoTermoInfos
            .Include(t => t.TipoTermo)
            .Include(t => t.Pedido)
            .Include(t => t.PedidoTermoParcelas)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken) ?? throw new("PedidoTermoInfos not found");
    }

    public async Task<List<PedidoTermoInfos>> GetList(CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var items = await _context.PedidoTermoInfos
                 .Include(t => t.TipoTermo)
                 .Include(t => t.Pedido)
                 .Include(t => t.PedidoTermoParcelas)
                 .AsNoTracking()
                 .ToListAsync();

            return items;
        }, []);
    }


    public async Task<object> Insert(PedidoTermoInfos aggregate, CancellationToken cancellationToken)
    {
        _context.PedidoTermoInfos.Add(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
        return aggregate;
    }

    public async Task Update(PedidoTermoInfos aggregate, CancellationToken cancellationToken)
    {
        aggregate.Update();
        _context.PedidoTermoInfos.Update(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task InsertParcelas(List<PedidoTermoParcelas> aggregate, CancellationToken cancellationToken)
    {
        _context.PedidoTermoParcelas.AddRange(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteParcelas(PedidoTermoInfos aggregate, CancellationToken cancellationToken)
    {
        var parcelas = await _context.PedidoTermoParcelas.Where(x => x.InfosId == aggregate.Id).ToListAsync();
        foreach (var item in parcelas)
        {
            item.Delete();
            _context.PedidoTermoParcelas.Update(item);
        }
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<PedidoTermoInfos?> GetByPedido(Guid PedidoId, CancellationToken cancellationToken)
    {
        return await _context.PedidoTermoInfos
            .Include(t => t.TipoTermo)
            .Include(t => t.Pedido)
            .Include(t => t.PedidoTermoParcelas)
            .FirstOrDefaultAsync(x => x.PedidoId == PedidoId, cancellationToken);
    }
}
