using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Common;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Remove;

public class TipoTermosRemove(
    ITipoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : ITipoTermosRemove
{
    public ITipoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<List<TipoTermosResponse>>> Handle(Guid id, CancellationToken cancellationToken)
    {
        ServiceResponse<List<TipoTermosResponse>> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = await _repo.Get(id, cancellationToken);
            await _repo.Delete(model, cancellationToken);
            response.Data = [.. (await _repo.GetList(cancellationToken)).Select(x => TipoTermosResponse.FromModel(x))];
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
