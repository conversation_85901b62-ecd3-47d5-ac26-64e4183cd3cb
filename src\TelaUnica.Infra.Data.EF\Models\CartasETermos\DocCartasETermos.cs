using System;
using System.ComponentModel.DataAnnotations.Schema;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Infra.Data.EF.Models.CartasETermos;

public class DocCartasETermos : Entity
{
    [ForeignKey("PedidoCartasETermos")]
    public Guid PedidoCartasETermosId { get; set; }
    public virtual PedidoCartasETermos? PedidoCartasETermos { get; set; }

    [Column(TypeName = "text")]
    public string Documento { get; set; } = string.Empty;
}
