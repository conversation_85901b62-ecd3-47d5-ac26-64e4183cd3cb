using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.PedidoInfos;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Common;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Remove;

public class InfosTermosRemove(
    IPedidoTermoInfosRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IInfosTermosRemove
{
    public IPedidoTermoInfosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<List<InfosTermosResponse>>> Handle(Guid id, CancellationToken cancellationToken)
    {
        ServiceResponse<List<InfosTermosResponse>> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = await _repo.Get(id, cancellationToken);
            await _repo.Delete(model, cancellationToken);
            response.Data = [.. (await _repo.GetList(cancellationToken)).Select(x => InfosTermosResponse.FromModel(x))];
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
