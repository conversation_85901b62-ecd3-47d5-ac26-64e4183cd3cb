using Newtonsoft.Json;
using TelaUnica.Domain.Dtos.Request.Datacob;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.Datacob.Api;
using TelaUnica.Domain.Interfaces;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.Datacob.DatacobInvoices;
using TelaUnica.Domain.Repository.Datacob.DatacobOccurrence;
using TelaUnica.Domain.Repository.TelaUnica.UserCrm;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models;
using Models = TelaUnica.Infra.Data.EF.Models;
using System.Net;
using System.Text.RegularExpressions;
using Azure;
using TelaUnica.Domain.Enums;

namespace TelaUnica.Domain.Repository.Datacob;

public class DatacobApiRepository : IDatacobApiRepository
{
    private readonly IConfigRepository _config;
    private readonly ApiHelpers _helper;
    private string url = "";
    private string type = "";
    private readonly IDatacobInvoicesRepository _di;
    private readonly IDatacobOccurrenceRepository _dc;
    private readonly IDatacobDapperRepository _dr;
    private readonly IUserCrmRepository _userCrm;
    private UserCrm? crmLogin;
    public DatacobApiRepository(IConfigRepository config, ApiHelpers helper, IDatacobInvoicesRepository di, IDatacobOccurrenceRepository dc, IDatacobDapperRepository dr, IUserCrmRepository userCrm)
    {
        _config = config;
        _helper = helper;
        _di = di;
        _dc = dc;
        _dr = dr;
        _userCrm = userCrm;
    }

    private async Task SetConnection(GvcRodobens? crm)
    {

        var logged = await _config.GetLoggedUser();
        crmLogin = logged.UserCrm.Where(x => x.Crm == (crm ?? logged.ActiveConnection)).FirstOrDefault() ?? throw new("Credenciais n�o encontradas.");
        if ((crm ?? logged.ActiveConnection) == GvcRodobens.GVC)
        {
            type = "gvc";
        }
        else if ((crm ?? logged.ActiveConnection) == GvcRodobens.Rodobens)
        {
            type = "rodobens";
        }
        url = await _config.GetValueByKey($"datacob_{type}_url");
    }

    public async Task<string> Login(GvcRodobens? crm)
    {
        await SetConnection(crm);

        string token = crmLogin!.Token ?? "";
        string Login = crmLogin.Login;
        string Password = _userCrm.Decrypt(crmLogin.Password);
        string ApiKey = await _config.GetValueByKey($"datacob_{type}_apikey");
        DateTime tokenExpireDT = crmLogin.TokenExpire ?? DateTime.Now.AddDays(-1);
        string urlLogin = $"{url}/api/account/v1/login";
        if (DateTime.Compare(DateTime.Now, tokenExpireDT) > 0)
        {

            Dictionary<string, string> data = new(){
                {"Login", Login},
                {"Password", Password},
                {"ApiKey", ApiKey}
            };

            ApiResponse<DatacobLoginResponse> responseApi = await _helper.PostApi<DatacobLoginResponse>(urlLogin, data);

            if (responseApi.Success == true && responseApi.Data != null)
            {
                token = responseApi.Data.access_token;
                crmLogin.TokenExpire = DateTime.Parse(responseApi.Data.expires_in);
                crmLogin.Token = token;
                await _userCrm.Update(crmLogin, CancellationToken.None);
            }
        }

        return token;
    }

    public async Task<ServiceResponse<DatacobLoginResponse>> LoginApi()
    {
        ServiceResponse<DatacobLoginResponse> response = new();
        try
        {
            await SetConnection(null);
            if (crmLogin is null)
                throw new("Credenciais n�o encontradas.");

            string urlLogin = $"{url}/api/account/v1/login";
            string Login = crmLogin.Login;
            string Password = _userCrm.Decrypt(crmLogin.Password);
            string ApiKey = await _config.GetValueByKey($"datacob_{type}_apikey");

            Dictionary<string, string> data = new(){
                {"Login", Login},
                {"Password", Password},
                {"ApiKey", ApiKey}
            };

            ApiResponse<DatacobLoginResponse> responseApi = await _helper.PostApi<DatacobLoginResponse>(urlLogin, data);

            if (responseApi.Success == true && responseApi.Data != null)
            {
                crmLogin.Token = responseApi.Data.access_token;
                crmLogin.TokenExpire = DateTime.Parse(responseApi.Data.expires_in);
                await _userCrm.Update(crmLogin, CancellationToken.None);
            }
            response.Data = responseApi.Data;
            response.Success = responseApi.Success;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<List<DatacobDadosCadastraisResponse>>> DadosCadastrais(string cnpj, GvcRodobens? crm)
    {
        ServiceResponse<List<DatacobDadosCadastraisResponse>> response = new();
        try
        {
            var token = await Login(crm);
            string urlLogin = $"{url}/api/dados-cadastrais/v1";

            Dictionary<string, string> data = new(){
                {"cpfCnpj", cnpj}
            };

            ApiResponse<List<DatacobDadosCadastraisResponse>> responseApi = await _helper.GetApi<List<DatacobDadosCadastraisResponse>>(urlLogin, data, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<DatacobResponse>> PostDadosCadastrais(DatacobInsertDadosCadastraisRequest request)
    {
        ServiceResponse<DatacobResponse> response = new();
        try
        {
            var token = await Login(request.Crm);
            string urlLogin = $"{url}/api/dados-cadastrais/v1/financiado";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<DatacobResponse> responseApi = await _helper.PostApi<DatacobResponse>(urlLogin, data, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

            // response.Data = new();
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }
    public async Task<ServiceResponse<DatacobAcordoCalculoResponse>> PostAcordoCalcular(DatacobAcordoCalculoRequest request)
    {
        ServiceResponse<DatacobAcordoCalculoResponse> response = new();
        try
        {
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            ApiResponse<DatacobAcordoCalculoResponse> responseApi = await CalcularAcordoCrm(request);

            if (responseApi.Success)
            {
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.Agreement,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobAcordoCalculoRequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    await CalcularAcordoCrm(model);
                }
            }


            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobAcordoCalculoResponse>> CalcularAcordoCrm(DatacobAcordoCalculoRequest request)
    {
        var token = await Login(request.Crm);
        string urlLogin = $"{url}/api/negociacao/v1/calcular-acordo";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
        var data = JsonConvert.SerializeObject(request, settings);
        ApiResponse<DatacobAcordoCalculoResponse> responseApi = await _helper.PostApi<DatacobAcordoCalculoResponse>(urlLogin, data, token);
        return responseApi;
    }

    public async Task<ServiceResponse<DatacobAcordoConfirmarResponse>> PostAcordoConfirmar(DatacobAcordoConfirmarRequest request)
    {
        ServiceResponse<DatacobAcordoConfirmarResponse> response = new();
        ApiResponse<DatacobAcordoConfirmarResponse> responseApi = new() { Success = true };
        try
        {
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            responseApi = await ConfirmarAcordoCrm(request);

            if (responseApi.Success)
            {
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.Agreement,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobAcordoConfirmarRequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    var responseApiLinked = await ConfirmarAcordoCrm(model);
                    if (responseApiLinked.Success)
                    {
                        var replica = JsonConvert.DeserializeObject<DatacobAcordoConfirmarResponse>(JsonConvert.SerializeObject(responseApiLinked.Data));
                        responseApi.Data!.Replica = replica;
                    }
                }
            }

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobAcordoConfirmarResponse>> ConfirmarAcordoCrm(DatacobAcordoConfirmarRequest request)
    {
        var token = await Login(request.Crm);
        string urlLogin = $"{url}/api/negociacao/v1/confirmar-acordo";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
        var data = JsonConvert.SerializeObject(request, settings);
        ApiResponse<DatacobAcordoConfirmarResponse> responseApi = await _helper.PostApi<DatacobAcordoConfirmarResponse>(urlLogin, data, token);
        return responseApi;
    }

    public async Task<ServiceResponse<DatacobAcordoGerarBoletoResponse>> PostAcordoGerarBoleto(DatacobAcordoGerarBoletoRequest request)
    {
        ServiceResponse<DatacobAcordoGerarBoletoResponse> response = new();
        try
        {
            var token = await Login(request.Crm);
            string urlLogin = $"{url}/api/negociacao/v1/gerar-boleto";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<DatacobAcordoGerarBoletoResponse> responseApi = await _helper.PostApi<DatacobAcordoGerarBoletoResponse>(urlLogin, data, token);

            if (responseApi.Success)
            {
                var user = await _di.Logged(CancellationToken.None);
                var userNeg = await _dr.GetUserByUsername(request.LoginCobradorResponsavel, user.ActiveConnection);
                await _di.Insert(new()
                {
                    IdBoleto = responseApi.Data?.IdBoleto ?? 0,
                    UserId = user.Id,
                    User = user,
                    Message = request.MensagemAdicional,
                    UserNegotiation = userNeg?.Id_Usuario ?? 0
                }, CancellationToken.None);
            }

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }
    public async Task<ServiceResponse<string>> DeleteAcordoCancelar(DatacobAcordoCancelarRequest request)
    {
        ServiceResponse<string> response = new();
        try
        {
            var token = await Login(request.Crm);
            string urlLogin = $"{url}/api/negociacao/v1/cancelar-acordo";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<string> responseApi = await _helper.DeleteApi<string>(urlLogin, data, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }
    public async Task<ServiceResponse<string>> DeleteCancelarBoleto(DatacobCancelarBoletoRequest request)
    {
        ServiceResponse<string> response = new();
        try
        {
            var token = await Login(request.Crm);
            string urlLogin = $"{url}/api/negociacao/v1/cancelar-boleto";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<string> responseApi = await _helper.DeleteApi<string>(urlLogin, data, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }
    public async Task<ServiceResponse<string>> DeleteCancelarBoletoId(DatacobCancelarBoletoIdRequest request)
    {
        ServiceResponse<string> response = new();
        try
        {
            var token = await Login(request.Crm);
            string urlLogin = $"{url}/api/negociacao/v1/cancelar-boleto-por-id";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<string> responseApi = await _helper.DeleteApi<string>(urlLogin, data, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<string>> GetBoleto(int IdBoleto, GvcRodobens? Crm)
    {
        ServiceResponse<string> response = new();
        try
        {
            var token = await Login(Crm);
            string urlLogin = $"{url}/api/negociacao/v1/download-boleto";

            Dictionary<string, string> data = new()
            {
                {"idBoleto", IdBoleto.ToString()},
                {"crm", Crm.ToString()}
            };

            ApiResponse<byte[]> responseApi = await _helper.GetApiPdf(urlLogin, data, token);
            if (responseApi.Data != null && responseApi.Success == true)
            {
                response.Data = Convert.ToBase64String(responseApi.Data);
            }
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<DatacobNegociacaoDtoResponse>> PostCalcularNegociacao(DatacobCalcularNegociacaoRequest request)
    {
        ServiceResponse<DatacobNegociacaoDtoResponse> response = new() { Success = false };
        ApiResponse<DatacobNegociacaoDtoResponse> responseApi = new() { Success = true };
        try
        {
            // GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
            //     ConfigIntegrationType.Agreement,
            //     request.Crm.Value,
            //     request.GroupId ?? 0,
            //     request.LinkedGroupId ?? 0
            // );

            // if (linkedCrm is not null)
            // {
            //     // var model = JsonConvert.DeserializeObject<DatacobCalcularNegociacaoRequest>(JsonConvert.SerializeObject(request));
            //     // model!.Crm = linkedCrm;
            //     // responseApi = await CalcularNegociacaoCrm(model);
            // }

            if (responseApi.Success)
            {
                responseApi = await CalcularNegociacaoCrm(request);
                response.Data = responseApi.Data;
                response.Message = responseApi.Message;
                response.Success = responseApi.Success;
            }

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobNegociacaoDtoResponse>> CalcularNegociacaoCrm(DatacobCalcularNegociacaoRequest request)
    {
        var token = await Login(request.Crm);
        string urlLogin = $"{url}/api/negociacao/v2/calcular-negociacao";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
        var data = JsonConvert.SerializeObject(request, settings);
        return await _helper.PostApi<DatacobNegociacaoDtoResponse>(urlLogin, data, token);
    }

    public async Task<ServiceResponse<string>> PostHistoricoAdicionar(DatacobHistoricoAdicionarTURequest request)
    {
        ServiceResponse<string> response = new();
        try
        {
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            ApiResponse<DatacobAdicionarHistoricoResponse> responseApi = await CadastrarHistoricoCrm(request);

            if (responseApi.Success)
            {
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.Occurrence,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobHistoricoAdicionarTURequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    await CadastrarHistoricoCrm(model);
                }

                var getLastHistory = await _dr.GetUltimoHistoricoInt(request.Id_Contrato, request.Id_Ocorrencia_Sistema);
                if (getLastHistory.Data is not null)
                {
                    var user = await _dc.Logged(CancellationToken.None);
                    Models.DatacobOccurrence model = new()
                    {
                        IdContrato = request.Id_Contrato,
                        IdHistorico = getLastHistory.Data.Id_Historico ?? 0,
                        User = user,
                        UserId = user.Id,
                        CallType = request.CallType
                    };
                    await _dc.InsertRaw(model, CancellationToken.None);
                }
            }

            response.Data = (responseApi.Data?.IdHistorico ?? 0).ToString(); 
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private static List<string> QuebrarTextoComPrefixo(string texto, int limite = 100)
    {
        List<string> partes = new List<string>();
        int indice = 0;
        int parteNumero = 1;

        if (texto.Length <= limite)
        {
            partes.Add(texto);
            return partes;
        }

        while (indice < texto.Length)
        {
            string prefixo = $"[Parte {parteNumero}]\n";
            int tamanhoDisponivel = limite - prefixo.Length;

            if (tamanhoDisponivel <= 0)
                throw new Exception("O prefixo é maior ou igual ao limite definido.");

            int tamanhoTexto = Math.Min(tamanhoDisponivel, texto.Length - indice);
            string parteTexto = texto.Substring(indice, tamanhoTexto);

            partes.Add(prefixo + parteTexto);

            indice += tamanhoTexto;
            parteNumero++;
        }

        return partes;
    }

    private async Task<ApiResponse<DatacobAdicionarHistoricoResponse>> CadastrarHistoricoCrm(DatacobHistoricoAdicionarTURequest request)
    {
        var token = await Login(request.Crm);
        var logged = await _config.GetLoggedUser();
        string urlLogin = $"{url}/api/historico/v1/adicionar-historico-informando-usuario";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
        string observacaoReplaced = Regex.Replace(request.Observacao, "<.*?>", "");
        ApiResponse<DatacobAdicionarHistoricoResponse> responseApi = new() { Success = false };

        var partesObservacao = QuebrarTextoComPrefixo(observacaoReplaced, 1000);
        foreach (var parte in partesObservacao)
        {
            DatacobHistoricoAdicionarRequest payload = new()
            {
                Complemento = request.Complemento,
                Id_Contrato = request.Id_Contrato,
                Id_Ocorrencia_Sistema = request.Id_Ocorrencia_Sistema,
                Login = request.Login,
                Observacao = parte,
                Telefones = request.Telefones,
                TelefoneParaRetorno = request.TelefoneParaRetorno
            };
            responseApi = await _helper.PostApi<DatacobAdicionarHistoricoResponse>(urlLogin, JsonConvert.SerializeObject(payload, settings), token);

            if (!responseApi.Success) return responseApi;
        }

        return responseApi;
    }

    public async Task<ServiceResponse<DatacobGerarReciboResponse>> PostGerarRecibo(DatacobGerarReciboRequest request)
    {

        ServiceResponse<DatacobGerarReciboResponse> response = new();
        try
        {
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            ApiResponse<DatacobGerarReciboResponse> responseApi = await GerarReciboNegociacaoCrm(request);
            
            if (responseApi.Success)
            {
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.Agreement,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobGerarReciboRequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    responseApi = await GerarReciboNegociacaoCrm(model);
                }
            }

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobGerarReciboResponse>> GerarReciboNegociacaoCrm(DatacobGerarReciboRequest request)
    {
        var token = await Login(request.Crm);
        string urlApi = $"{url}/api/pagamento/v1/gerar-recibo-negociacao";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };
        var data = JsonConvert.SerializeObject(request, settings);
        return await _helper.PostApi<DatacobGerarReciboResponse>(urlApi, data, token);
    }

    public async Task<ServiceResponse<DatacobGerarBoletoNegociacaoResponse>> PostGerarBoletoNegociacao(DatacobGerarBoletoNegociacaoRequest request)
    {
        ServiceResponse<DatacobGerarBoletoNegociacaoResponse> response = new();
        try
        {
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            ApiResponse<DatacobGerarBoletoNegociacaoResponse> responseApi = await GerarBoletoNegociacaoCrm(request, (GvcRodobens)request.Crm);
            

            if (responseApi.Success)
            {
                DatacobGerarBoletoNegociacaoResponse? replica = null;
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.BankSlip,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobGerarBoletoNegociacaoRequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    var responseApiLiked = await GerarBoletoNegociacaoCrm(model, (GvcRodobens)model.Crm!);
                    if (responseApiLiked.Success)
                        replica = JsonConvert.DeserializeObject<DatacobGerarBoletoNegociacaoResponse>(JsonConvert.SerializeObject(responseApiLiked.Data));
                    responseApi.Data!.Replica = replica;
                }

                var user = await _di.Logged(CancellationToken.None);
                await _di.Insert(new()
                {
                    IdBoleto = responseApi.Data?.IdBoleto ?? 0,
                    UserId = user.Id,
                    User = user,
                    Message = replica != null ? "Replica:" + JsonConvert.SerializeObject(replica) : request.Mensagem,
                    UserNegotiation = request.UsuarioNegociacao
                }, CancellationToken.None);
            }
            
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobGerarBoletoNegociacaoResponse>> GerarBoletoNegociacaoCrm(DatacobGerarBoletoNegociacaoRequest request, GvcRodobens gvcLog)
    {
        var token = await Login(request.Crm);
        string urlApi = $"{url}/api/negociacao/v2/gerar-boleto-negociacao";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

        DatacobGerarBoletoNegociacaoApiRequest requestApi = new()
        {
            FormaDesconto = request.FormaDesconto,
            DtNegociacao = request.DtNegociacao,
            IdAgrupamento = request.IdAgrupamento,
            VlNegociado = request.VlNegociado,
            Parcelas = request.Parcelas,
            MensagemAdicional = request.Mensagem
        };

        if (request.Crm == GvcRodobens.GVC && request.UsuarioNegociacao != null && request.UsuarioNegociacao != 0)
        {
            var logged = await _config.GetLoggedUser();
            var userNeg = await _dr.GetUserById(request.UsuarioNegociacao ?? 0, logged.ActiveConnection);
            if (userNeg != null)
            {
                requestApi.LoginCobradorResponsavel = userNeg.Login;
            }
        }

        if (gvcLog == GvcRodobens.GVC && gvcLog != request.Crm)
        {
            request.TipoEnvio = Enums.TipoEnvio.Impressao;
        }

        switch (request.TipoEnvio)
        {
            case Enums.TipoEnvio.Email:
                requestApi.Email = request.Email;
                break;
            case Enums.TipoEnvio.SMS:
                requestApi.Ddd = request.Ddd;
                requestApi.Fone = request.Fone;
                break;
        }

        var data = JsonConvert.SerializeObject(requestApi, settings);
        return await _helper.PostApi<DatacobGerarBoletoNegociacaoResponse>(urlApi, data, token);
    }

    public async Task<ServiceResponse<List<DatacobTiposResponse>>> GetTipoEmail()
    {
        ServiceResponse<List<DatacobTiposResponse>> response = new();
        try
        {
            var token = await Login(null);
            string urlLogin = $"{url}/api/dados-cadastrais/v1/tipos-email";

            ApiResponse<List<DatacobTiposResponse>> responseApi = await _helper.GetApi<List<DatacobTiposResponse>>(urlLogin, null, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<List<DatacobTiposResponse>>> GetTipoEndereco()
    {
        ServiceResponse<List<DatacobTiposResponse>> response = new();
        try
        {
            var token = await Login(null);
            string urlLogin = $"{url}/api/dados-cadastrais/v1/tipos-endereco";

            ApiResponse<List<DatacobTiposResponse>> responseApi = await _helper.GetApi<List<DatacobTiposResponse>>(urlLogin, null, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<List<DatacobTiposResponse>>> GetTipoTelefone()
    {
        ServiceResponse<List<DatacobTiposResponse>> response = new();
        try
        {
            var token = await Login(null);
            string urlLogin = $"{url}/api/dados-cadastrais/v1/tipos-telefone";

            ApiResponse<List<DatacobTiposResponse>> responseApi = await _helper.GetApi<List<DatacobTiposResponse>>(urlLogin, null, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<DatacobConsultarDividaResponse>> GetConsultarDivida(string cpfCnpj, int idGrupo, string? numeroContrato, GvcRodobens? crm)
    {
        ServiceResponse<DatacobConsultarDividaResponse> response = new();
        try
        {
            ApiResponse<DatacobConsultarDividaResponse> responseApi = await ConsultarDividaAtivaNegociacaoCrm(cpfCnpj, idGrupo, numeroContrato, crm);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobConsultarDividaResponse>> ConsultarDividaAtivaNegociacaoCrm(string cpfCnpj, int idGrupo, string? numeroContrato, GvcRodobens? crm)
    {
        var token = await Login(crm);
        string urlLogin = $"{url}/api/negociacao/v1/consultar-divida-ativa-negociacao";

        Dictionary<string, string> data = new()
        {
            {"cpfCnpj", cpfCnpj},
            {"idGrupo", idGrupo.ToString()}
        };

        if (numeroContrato != null)
        {
            data.Add("numeroContrato", numeroContrato);
        }

        if (crm != null)
        {
            data.Add("Crm", crm.ToString());
        }

        return await _helper.GetApi<DatacobConsultarDividaResponse>(urlLogin, data, token);
    }

    public async Task<ServiceResponse<DatacobGerarBoletoNegociacaoResponse>> PostGerarBoletoNegociacaoSalva(DatacobGerarBoletoNegociacaoSalvaRequest request, bool enviaUser = true)
    {
        ServiceResponse<DatacobGerarBoletoNegociacaoResponse> response = new();
        try
        {
            DatacobGerarBoletoNegociacaoResponse? replica = null;
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            ApiResponse<DatacobGerarBoletoNegociacaoResponse> responseApi = await GerarBoletoNegociacaoSalvaCrm(request, enviaUser);


            if (responseApi.Success)
            {
                
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.BankSlip,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobGerarBoletoNegociacaoSalvaRequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    model.IdNegociacao = model.IdNegociacaoReplica ?? 0;
                    if (model.IdNegociacao > 0)
                    {
                        var responseApiLinked = await GerarBoletoNegociacaoSalvaCrm(request, enviaUser);
                        if (responseApiLinked.Success)
                            replica = JsonConvert.DeserializeObject<DatacobGerarBoletoNegociacaoResponse>(JsonConvert.SerializeObject(responseApiLinked.Data));
                    }
                }

                var user = await _di.Logged(CancellationToken.None);
                await _di.Insert(new()
                {
                    IdBoleto = responseApi.Data?.IdBoleto ?? 0,
                    UserId = user.Id,
                    User = user,
                    Message = replica != null ? "Replica:" + JsonConvert.SerializeObject(replica) : request.MensagemAdicional,
                    UserNegotiation = request.UsuarioNegociacao
                }, CancellationToken.None);

                responseApi.Data!.Replica = replica;
            }

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobGerarBoletoNegociacaoResponse>> GerarBoletoNegociacaoSalvaCrm(DatacobGerarBoletoNegociacaoSalvaRequest request, bool enviaUser)
    {
        var token = await Login(request.Crm);
        string urlApi = $"{url}/api/negociacao/v2/gerar-boleto-negociacao-salva";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

        DatacobGerarBoletoNegociacaoSalvaApiRequest requestApi = new()
        {
            IdNegociacao = request.IdNegociacao,
            IdAgrupamento = request.IdAgrupamento,
            MensagemAdicional = request.MensagemAdicional
        };

        if (enviaUser)
        {
            if (request.UsuarioNegociacao != null && request.UsuarioNegociacao != 0)
            {
                var logged = await _config.GetLoggedUser();
                var userNeg = await _dr.GetUserById(request.UsuarioNegociacao ?? 0, logged.ActiveConnection);
                if (userNeg != null)
                {
                    requestApi.LoginCobradorResponsavel = userNeg.Login;
                }
            }
        }

        var data = JsonConvert.SerializeObject(requestApi, settings);
        return await _helper.PostApi<DatacobGerarBoletoNegociacaoResponse>(urlApi, data, token);
    }

    public async Task<ServiceResponse<DatacobGerarNegociacaoLivreResponse>> PostGerarNegociacaoLivre(DatacobGerarNegociacaoLivreRequest request)
    {
        ServiceResponse<DatacobGerarNegociacaoLivreResponse> response = new();
        try
        {
            request.Crm ??= (await _config.GetLoggedUser()).ActiveConnection;
            ApiResponse<DatacobGerarNegociacaoLivreResponse> responseApi = await SalvarNegocicacaoCalculoLivre(request);

            if (responseApi.Success)
            {
                GvcRodobens? linkedCrm = request.Crm is null ? null : await _dr.GetLinkedCrm(
                    ConfigIntegrationType.Agreement,
                    request.Crm.Value,
                    request.GroupId ?? 0,
                    request.LinkedGroupId ?? 0
                );

                if (linkedCrm is not null)
                {
                    var model = JsonConvert.DeserializeObject<DatacobGerarNegociacaoLivreRequest>(JsonConvert.SerializeObject(request));
                    model!.Crm = linkedCrm;
                    var responseApiLinked = await SalvarNegocicacaoCalculoLivre(model);
                    if (responseApiLinked.Success)
                    {
                        var replica = JsonConvert.DeserializeObject<DatacobGerarNegociacaoLivreResponse>(JsonConvert.SerializeObject(responseApiLinked.Data));
                        responseApi.Data!.Replica = replica;
                    }
                }
            }

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    private async Task<ApiResponse<DatacobGerarNegociacaoLivreResponse>> SalvarNegocicacaoCalculoLivre(DatacobGerarNegociacaoLivreRequest request)
    {
        var token = await Login(request.Crm);
        string urlApi = $"{url}/api/negociacao/v1/salvar-negociacao-calculo-livre";
        JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

        var data = JsonConvert.SerializeObject(request, settings);
        return await _helper.PostApi<DatacobGerarNegociacaoLivreResponse>(urlApi, data, token);
    }

    public async Task<ServiceResponse<DatacobCustasResponse>> PostCadastrarCustas(DatacobCustasRequest<DatacobCadastrarCustasRequest> request)
    {
        ServiceResponse<DatacobCustasResponse> response = new();
        try
        {

            var token = await Login(request.Crm);
            string urlApi = $"{url}/api/dados-cadastrais/v1/cadastrar-custas";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<DatacobCustasResponse> responseApi = await _helper.PostApi<DatacobCustasResponse>(urlApi, data, token);

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<DatacobCustasResponse>> PostAtualizarCustas(DatacobCustasRequest<DatacobAtualizarCustasRequest> request)
    {
        ServiceResponse<DatacobCustasResponse> response = new();
        try
        {

            var token = await Login(request.Crm);
            string urlApi = $"{url}/api/dados-cadastrais/v1/atualizar-custas";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<DatacobCustasResponse> responseApi = await _helper.PostApi<DatacobCustasResponse>(urlApi, data, token);

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<DatacobCustasResponse>> PostDevolverCustas(DatacobCustasRequest<DatacobDevolverCustasRequest> request)
    {
        ServiceResponse<DatacobCustasResponse> response = new();
        try
        {

            var token = await Login(request.Crm);
            string urlApi = $"{url}/api/dados-cadastrais/v1/devolver-custas";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<DatacobCustasResponse> responseApi = await _helper.PostApi<DatacobCustasResponse>(urlApi, data, token);

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<DatacobCustasResponse>> PostReativarCustas(DatacobCustasRequest<DatacobReativarCustasRequest> request)
    {
        ServiceResponse<DatacobCustasResponse> response = new();
        try
        {

            var token = await Login(request.Crm);
            string urlApi = $"{url}/api/dados-cadastrais/v1/reativar-custas";
            JsonSerializerSettings settings = new() { NullValueHandling = NullValueHandling.Ignore };

            var data = JsonConvert.SerializeObject(request, settings);
            ApiResponse<DatacobCustasResponse> responseApi = await _helper.PostApi<DatacobCustasResponse>(urlApi, data, token);

            response.Data = responseApi.Data;
            response.Message = responseApi.Message;
            response.Success = responseApi.Success;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }

    public async Task<ServiceResponse<List<DatacobGetCustasResponse>>> GetCustas(int idContrato, GvcRodobens? crm)
    {
        ServiceResponse<List<DatacobGetCustasResponse>> response = new();
        try
        {

            var token = await Login(crm);
            string urlLogin = $"{url}/api/negociacao/v1/consultar-divida-ativa-negociacao";

            Dictionary<string, string> data = new(){
                {"idContrato", idContrato.ToString()},
            };

            ApiResponse<List<DatacobGetCustasResponse>> responseApi = await _helper.GetApi<List<DatacobGetCustasResponse>>(urlLogin, data, token);
            response.Data = responseApi.Data;
            response.Message = responseApi.Message;

        }
        catch (Exception e)
        {
            response.Success = false;
            response.Message = e.Message;
        }
        return response;
    }
}