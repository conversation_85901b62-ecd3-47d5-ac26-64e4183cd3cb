using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Create;

public class ConteudoTermosCreate(
    IConteudoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IConteudoTermosCreate
{
    public IConteudoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<bool>> Handle(ConteudoTermoCreateInput input, CancellationToken cancellationToken)
    {
        ServiceResponse<bool> response = new(true);
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            input.Validate();
            var conteudoTermo = await _repo.GetByTipo(input.TipoTermoId, input.GrupoId, input.Crm, cancellationToken);
            if (conteudoTermo != null) throw new("Conteudo de termo já existente");

            var model = input.ToModel();
            if (model.CabecalhoImg != null && model.CabecalhoImg.Length == 0)
            {
                using var memoryStream = new MemoryStream();
                await input.CabecalhoImg!.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();
                string base64String = Convert.ToBase64String(fileBytes);
                model.CabecalhoImg = base64String;
            }

            if (model.RodapeImg != null && model.RodapeImg.Length == 0)
            {
                using var memoryStream = new MemoryStream();
                await input.RodapeImg!.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();
                string base64String = Convert.ToBase64String(fileBytes);
                model.RodapeImg = base64String;
            }

            _ = (await _repo.Insert(model, cancellationToken)) ?? throw new();
        }, (ex) => response.SetFailure($"Falha ao inserir conteudo de termo! {ex.Message}.", false));
        return response;
    }
}
