using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using TelaUnica.Api.Middleware.Services;
using TelaUnica.Domain.Dtos.Request.User;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Interfaces;
using TelaUnica.Infra.Data.EF.Models.Datacob;

namespace TelaUnica.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IMemoryCache _cache;
    private readonly BruteForceService _bruteForceService;
    public IAuthRepository _authRepository { get; }
    public AuthController(IAuthRepository authRepository, IMemoryCache cache, BruteForceService bruteForceService)
    {
        _authRepository = authRepository;
        _cache = cache;
        _bruteForceService = bruteForceService;
    }

    [Authorize]
    [HttpPost("Register")]
    public async Task<ActionResult<ServiceResponse<int>>> Register(UserRegisterDto request)
    {
        var response = await _authRepository.Register(new User { Username = request.Username, AutomaticReconnectionOlos = true }, request.Password, request);
        if (!response.Success)
        {
            return BadRequest(response);
        }
        return Ok(response);
    }

    [HttpPost("Login")]
    public async Task<ActionResult<ServiceResponse<int>>> Login(UserLoginDto request)
    {
        var response = await _authRepository.Login(request.Username, request.Password, request.isPhone);

        string userKey = $"LoginUser_{request.Username}";
        var cacheKey = $"LoginAttempts_{HttpContext.Connection.RemoteIpAddress?.ToString()}";

        if (!response.Success)
        {
            _bruteForceService.RegisterFailedAttempt(userKey);
            _bruteForceService.RegisterFailedAttempt(cacheKey);
            return BadRequest(response);
        }

        _bruteForceService.ResetAttempts(userKey);
        _bruteForceService.ResetAttempts(cacheKey);

        return Ok(response);
    }

    [Authorize]
    [HttpGet("GetPermissionsAndGroups/{id}")]
    public async Task<ActionResult<ServiceResponse<string>>> GetPermissionsAndGroups(int id)
    {
        var response = await _authRepository.GetPermissionsAndGroups(id);

        return Ok(response);
    }

    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshAsync([FromBody] RefreshRequest request, [FromQuery] bool isPhone = true)
    {
        var response = await _authRepository.ValidateRefreshToken(request.RefreshToken, isPhone);
        if (!response.Success)
            return Unauthorized();

        return Ok(response);
    }

    [HttpPost("active")]
    public async Task<IActionResult> ActiveAsync([FromBody] string request)
    {
        var response = await _authRepository.ValidateRefreshToken(request);
        if (!response.Success)
            return Unauthorized();

        return Ok(response);
    }

    [HttpPost("Login/Simplified")]
    public async Task<ActionResult<ServiceResponse<int>>> LoginSimplified(UserLoginDto request)
    {
        var response = await _authRepository.LoginSimplified(request.Username, request.Password);
        if (!response.Success)
        {
            return BadRequest(response);
        }

        return Ok(response);
    }

    [HttpGet("TesteApi")]
    public ActionResult<ServiceResponse<string>> TesteApi()
    {
        ServiceResponse<string> response = new()
        {
            Data = "Disparo Teste Pipeline",
            Message = "Disparo Teste Retornou com Sucesso.",
            Success = true
        };
        return Ok(response);
    }

    [Authorize]
    [HttpPost("ChangePassword")]
    public async Task<ActionResult<ServiceResponse<int>>> ChangePassword(UserChangePasswordRequest request)
    {
        var response = await _authRepository.ChangePassword(request);
        if (!response.Success)
        {
            return BadRequest(response);
        }
        return Ok(response);
    }

    [HttpPost("LogUser")]
    public async Task<ActionResult<ServiceResponse<string>>> LogUser(string type, string responseApi, string userName)
    {
        await _authRepository.LogUser(type, responseApi, userName);

        ServiceResponse<string> response = new()
        {
            Data = "Log User",
            Message = "Disparo LOG Retornou com Sucesso.",
            Success = true
        };
        return Ok(response);
    }

    [Authorize]
    [HttpGet("LogUser")]
    public async Task<ActionResult<ServiceResponse<List<LogUserDTO>>>> LogUser(
        [FromQuery] string? user_name,
        [FromQuery] string? name,
        [FromQuery] DateTime? data_inicial,
        [FromQuery] DateTime? data_final)

    {
        var response = await _authRepository.GetLogsUsers(user_name, name, data_inicial, data_final);
        if (!response.Success)
        {
            return BadRequest(response);
        }

        return Ok(response);

    }

    [HttpPost("ListLogUser/ExportToExcel")]
    public async Task<ActionResult> ListLogUserExcel(List<LogUserDTO> data)
    {
        var excelFile = await _authRepository.ListLogUserExcel(data);

        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "log_lista_user.xlsx");

    }
}
