﻿using TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.CartasETermos;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Create;

public class FilaAprovacaoCartasETermosCreate(
    IPedidoCartasETermosRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IFilaAprovacaoCartasETermosCreate
{
    public IPedidoCartasETermosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<ItemFilaAprovacaoCartasETermosResponse>> Handle(FilaAprovacaoCartasETermosCreateStatusInput input, CancellationToken cancellationToken)
    {
        
        ServiceResponse<ItemFilaAprovacaoCartasETermosResponse> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = input.ToModel();
            model.Status = "Informações pendentes";
            _ = await _repo.Insert(model, cancellationToken) ?? throw new("Falha ao inserir Pedido.");
            response.Data = ItemFilaAprovacaoCartasETermosResponse.FromModel(model);
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
