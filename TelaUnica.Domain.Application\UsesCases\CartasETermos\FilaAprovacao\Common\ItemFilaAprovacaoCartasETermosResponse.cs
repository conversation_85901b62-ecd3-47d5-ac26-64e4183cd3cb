using System;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Models.CartasETermos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;

public class ItemFilaAprovacaoCartasETermosResponse
{
    public Guid Id { get; set; }
    public int IdOperador { get; set; }
    public string Operador { get; set; } = string.Empty;
    public int IdFinanciado { get; set; }
    public string Financiado { get; set; } = string.Empty;

    public string? Status { get; set; }
    public int Tipo { get; set; }
    public string TipoDescricao { get; set; } = string.Empty;
    public int IdContrato { get; set; }

    public DateTime? CreatedAt { get; set; }

    public static ItemFilaAprovacaoCartasETermosResponse FromModel(PedidoCartasETermos model)
    {
        return new()
        {
            Id = model.Id,
            IdOperador = model.IdOperador,
            Operador = string.Empty,
            IdFinanciado = model.IdFinanciado,
            Financiado = string.Empty,
            Status = model.Status,
            Tipo = (int)model.Tipo,
            TipoDescricao = model.Tipo.GetDisplayName(),
            IdContrato = model.IdContrato,
            CreatedAt = model.CreatedAt
        };
    }

}
