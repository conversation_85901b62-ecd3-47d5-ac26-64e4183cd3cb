﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class CriarTabelaTipoCartasETermo_CriarTabelaPedidosCartasETermos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TipoCartaETermo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Nome = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TipoCartaETermo", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PedidoCartasETermos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IdOperador = table.Column<int>(type: "int", nullable: false),
                    IdFinanciado = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TipoId = table.Column<int>(type: "int", nullable: true),
                    IdContrato = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PedidoCartasETermos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PedidoCartasETermos_TipoCartaETermo_TipoId",
                        column: x => x.TipoId,
                        principalTable: "TipoCartaETermo",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_PedidoCartasETermos_TipoId",
                table: "PedidoCartasETermos",
                column: "TipoId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PedidoCartasETermos");

            migrationBuilder.DropTable(
                name: "TipoCartaETermo");
        }
    }
}
