﻿using iText.Html2pdf;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.CartasETermos;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Generate.Preview;

public class FilaAprovacaoCETGeneratePreview(
    IPedidoCartasETermosRepository repo,
    IDatacobDapperRepository dapperDCRep,
    IConteudoTermoRepository repoConteudo
) : IFilaAprovacaoCETGeneratePreview
{
    public IPedidoCartasETermosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;
    public IConteudoTermoRepository _repoConteudo = repoConteudo;

    public async Task<ServiceResponse<byte[]>> Handle(FilaAprovacaoCETGeneratePreviewInput pedido, CancellationToken cancellationToken)
    {

        ServiceResponse<byte[]> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            
            var conteudo = await _repoConteudo.GetByTipoOrDefault(pedido.TipoTermoId, pedido.IdGrupo, pedido.Crm, cancellationToken); 
            Dictionary<string, string> values = new()
            {
                { "ClientePrincipal", pedido.ClientePrincipal },
                { "TipoAcao", pedido.TipoAcao },
                { "AdversoPrincipal", pedido.AdversoPrincipal },
                { "GrupoCotaContrato", pedido.GrupoCotaContrato },
                { "NrParcelasVencidas", pedido.NrParcelasVencidas },
                { "ValorParcelasVencidas", pedido.ValorParcelasVencidas},
                { "MultaJuros", pedido.MultaJuros},
                { "Custas", pedido.Custas},
                { "NrParcelasVincendas", pedido.NrParcelasVincendas },
                { "ValorParcelasVincendas", pedido.ValorParcelasVincendas},
                { "Honorarios", pedido.Honorarios},
                { "Total", pedido.Total},
                { "DataBase", pedido.DataBase },
                { "QtdParcelasAcordadas", pedido.QtdParcelasAcordadas.ToString() },
                { "ValorAcordado", pedido.ValorAcordado},
                { "DescricaoVeiculo", pedido.DescricaoVeiculo },
                { "NrAtual", pedido.NrAtual },
                { "JurisdicaoAtual", pedido.JurisdicaoAtual },
                { "headerBase64", pedido.HeaderBase64 },
                { "footerBase64", pedido.FooterBase64 }
            };

            var template = StringHelpers.GenerateTemplate(conteudo.Html, values);
            response.Data = PdfHelpers.GeneratePdf(template);
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
