using CsvHelper;
using Dapper;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using OfficeOpenXml;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Runtime.ConstrainedExecution;
using System.Security.Claims;
using System.Text.RegularExpressions;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.Datacob;
using TelaUnica.Domain.Dtos.Response.Datacob.NegociacaoCalculoDetalhe;
using TelaUnica.Domain.Enums;
using TelaUnica.Domain.Interfaces;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Repository.Datacob.DatacobInvoices;
using TelaUnica.Domain.Repository.Datacob.DatacobOccurrence;
using TelaUnica.Domain.Repository.Datacob.DatacobPhoneCall;
using TelaUnica.Domain.Repository.TelaUnica.UserControlTicket;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Data;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models;
using TelaUnica.Infra.Data.EF.Models.Datacob;

namespace TelaUnica.Domain.Repository.Datacob;

public class DatacobDapperRepository : BaseDapperRepository, IDatacobDapperRepository
{
    public readonly DataContext _context;
    public readonly IHttpContextAccessor _httpContextAccessor;
    public readonly User _logged;
    private readonly DatacobHelpers _helper;
    private readonly SqlDatacobHelpers _sql;
    private readonly IConfigRepository _config;
    private readonly IDatacobPhoneCallRepository _dpc;
    private readonly IDatacobInvoicesRepository _di;
    private readonly IDatacobOccurrenceRepository _dor;
    private readonly IUserControlTicketRepository _uct;
    private readonly IConfiguration _confi;

    public DatacobDapperRepository(
        DatacobHelpers helper
        , SqlDatacobHelpers sql
        , IConfigRepository config
        , IHttpContextAccessor httpContextAccessor
        , DataContext context
        , IConfiguration configuration
        , IDatacobPhoneCallRepository dpc
        , IDatacobInvoicesRepository di
        , IDatacobOccurrenceRepository dor
        , IUserControlTicketRepository uct
        , IConfiguration confi
    )
        : base(configuration)
    {
        _helper = helper;
        _sql = sql;
        _config = config;
        _httpContextAccessor = httpContextAccessor;
        _context = context;
        _configuration = configuration;
        _logged = GetUserModel().Result;
        _dpc = dpc;
        _di = di;
        _dor = dor;
        _uct = uct;
        _confi = confi;
    }

    private async Task<User> GetUserModel()
    {
        if (_httpContextAccessor.HttpContext is null)
        {
            return await _context.Users.FirstOrDefaultAsync(u => u.Id == 2) ?? new() { Id = 0 };
        }
        string? user = _httpContextAccessor.HttpContext!.User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (_httpContextAccessor.HttpContext is null || user is null) return new() { Id = 0, ActiveConnection = 0 };
        return await _context.Users.FirstOrDefaultAsync(u => u.Id == int.Parse(user)) ?? new() { Id = 0 };
    }

    public async Task<UserDatacob?> GetUserById(int id, GvcRodobens? ActiveConnection)
    {
        string sql = $"{_sql.SqlUsuario()} and u.Id_Usuario = {id}";
        try
        {
            IDbConnection conn = CreateDatacobConnection(ActiveConnection ?? _logged.ActiveConnection);
            UserDatacob? user = await conn.QueryFirstAsync<UserDatacob>(sql);
            return user;
        }
        catch
        {
            return null;
        }
    }

    public async Task<ServiceResponse<List<UserDatacob>>> GetUsers(GvcRodobens? ActiveConnection)
    {
        ServiceResponse<List<UserDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(ActiveConnection ?? _logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<UserDatacob>(_sql.SqlUsuario())).ToList();

            if (response.Data is null || response.Data.Count == 0) throw new("Falha ao trazer os usuários.");

            response.Data?.ForEach(usuario =>
            {
                usuario.Login = usuario.Login.Encrypt(_confi);
                usuario.Email = usuario.Email.Encrypt(_confi);
                usuario.Grupo = usuario.Grupo.Encrypt(_confi);
            });
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<UserDatacob?> GetUserByUsername(string username, GvcRodobens crm = GvcRodobens.GVC)
    {
        try
        {
            string sql = $"{_sql.SqlUsuario()} and u.Login = '{username}'";
            IDbConnection conn = CreateDatacobConnection(_logged.Id == 0 ? crm : _logged.ActiveConnection);
            UserDatacob? user = await conn.QueryFirstAsync<UserDatacob>(sql);

            return user;
        }
        catch
        {
            return null;
        }
    }

    public async Task<UserDatacob?> GetUserByLogin(string username, GvcRodobens crm = GvcRodobens.GVC)
    {
        IDbConnection conn = CreateDatacobConnection(_logged.Id == 0 ? crm : _logged.ActiveConnection);
        UserDatacob? user = await conn.QueryFirstAsync<UserDatacob>(_sql.SqlUsuarioLogin(), new { username });

        return user;
    }

    public async Task<ServiceResponse<List<ClientDatacob>>> GetClients(GvcRodobens? ActiveConnection, int? GroupId)
    {

        ServiceResponse<List<ClientDatacob>> response = new();
        try
        {
            var sql = _sql.SqlCliente();
            if (GroupId is not null) sql += $" AND c.Id_Grupo = {GroupId}";

            IDbConnection conn = CreateDatacobConnection(ActiveConnection ?? _logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<ClientDatacob>(sql)).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Falha ao trazer os clientes.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }
    public async Task<ServiceResponse<List<StatusDatacob>>> GetStatus(GvcRodobens? ActiveConnection, int? Nr_Agrupamento)
    {
        ServiceResponse<List<StatusDatacob>> response = new();
        try
        {
            var sql = _sql.SqlStatusContrato();
            if (Nr_Agrupamento is not null) sql += $"AND sc.Nr_Agrupamento = {Nr_Agrupamento}";

            IDbConnection conn = CreateDatacobConnection(ActiveConnection ?? _logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<StatusDatacob>(sql)).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Falha ao trazer os status do contrato.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosFinanciados(GvcRodobens ActiveConnection, string? documento, string? nome, string? contrato, string? email, string? telefone, int? clientId, int? groupId, int? aberto, int? agrupamentoId, int? idStatusContrato, int? cyber, string? fase, int? contratoId)
    {
        aberto ??= 1;
        ServiceResponse<List<DadosCadastraisDatacob>> response = new();
        try
        {

            if (documento is null && nome is null && contrato is null && email is null && telefone is null && groupId is null && agrupamentoId is null && contratoId is null)
            {
                response.Message = "Preencha pelo menos um dos seguintes campos: documento, nome, contrato, email, telefone, grupo.";
                response.Success = false;
                return response;
            }
            List<FinanciadoIdDatacob>? finan = null;
            IDbConnection conn = CreateDatacobConnection(ActiveConnection);

            if (documento is not null || nome is not null || email is not null || telefone is not null)
            {

                if (documento is not null) finan = (await conn.QueryAsync<FinanciadoIdDatacob>(_sql.SqlBuscaFinanciadoDoc(documento.Trim()))).ToList();
                else if (nome is not null) finan = (await conn.QueryAsync<FinanciadoIdDatacob>(_sql.SqlBuscaFinanciadoNome(nome.Replace(" ", "")))).ToList();
                else if (email is not null) finan = (await conn.QueryAsync<FinanciadoIdDatacob>(_sql.SqlBuscaFinanciadoEmail(email.Trim()))).ToList();
                else if (telefone is not null)
                {
                    telefone = Regex.Replace(telefone, "[^0-9]", "").Trim();
                    finan = (await conn.QueryAsync<FinanciadoIdDatacob>(_sql.SqlBuscaFinanciadoTelefone(telefone))).ToList();
                }
                if (finan is null || finan.Count == 0)
                {
                    response.Message = "Dados não encontrados.";
                    response.Success = false;
                    return response;
                }
            }

            string sql = _sql.SqlBuscaDadosFinanciados(aberto, ActiveConnection == GvcRodobens.Rodobens);
            if (finan is not null) sql += $" AND f.Id_financiado IN ({string.Join(',', finan.Select(x => x.Id_Financiado))})";
            if (contrato is not null)
            {
                if (groupId is not null) sql += $" AND c.Numero_Contrato like '%{contrato}%'";
                else sql += $" AND c.Numero_Contrato = '{contrato}'";
            }
            if (clientId is not null) sql += $" AND f.Id_Cliente LIKE {clientId}";
            if (groupId is not null) sql += $" AND cli.Id_Grupo LIKE {groupId}";
            if (agrupamentoId is not null) sql += $" AND c.Id_Contrato LIKE {agrupamentoId} and c.Id_Agrupamento = {agrupamentoId}";
            if (idStatusContrato != null) sql += $" AND d.Id_Status_Contrato = {idStatusContrato}";
            if (!string.IsNullOrEmpty(fase)) sql += $" AND d.Cod_Fase = {fase}";
            if (contratoId is not null) sql += $" AND c.Id_Contrato = {contratoId}";

            if (cyber != null && cyber == 0)
                sql += $" AND LEN(c.Numero_Contrato) < 25";

            if (cyber != null && cyber == 1)
                sql += $" AND LEN(c.Numero_Contrato) >= 25";

            sql += _sql.SqlBuscaDadosFinanciadosGroupBy();
            List<DadosCadastraisDatacob> result = (await conn.QueryAsync<DadosCadastraisDatacob>(sql)).ToList();

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            result = await ExceptionHelpers.SafeExecuteAsync(async () =>
            {
                IEnumerable<int?> groupsIds = result.Select(x => (int?)x.Id_Grupo).Distinct();
                var integrations = await _context.IntegrationConfigurations.Where(x => groupsIds.Contains(x.LinkedGroupId) && x.Active).ToListAsync() ?? [];
                IDbConnection connLinked = CreateDatacobConnection(ActiveConnection == GvcRodobens.Rodobens ? GvcRodobens.GVC : GvcRodobens.Rodobens);
                List<GroupDatacob> groups = [.. (await connLinked.QueryAsync<GroupDatacob>(_sql.SqlGrupo())) ?? []];
                return [.. result.Select((res) =>
                {
                    var integration = integrations.FirstOrDefault(x => x.LinkedGroupId == res.Id_Grupo);
                    var linked = groups.FirstOrDefault(x => x.Id_Grupo == integration?.GroupId);
                    res.IdLinkedGroup = linked?.Id_Grupo;
                    res.LinkedGroup = linked?.Descricao;
                    res.coddatacob = ActiveConnection == GvcRodobens.Rodobens ? "Rodobens" : "GVC";
                    return res;
                })];
            }, result);

            List<DadosCadastraisDatacob> responseData = [];

            List<int?> ids = result.Select(x => x.Id_Contrato).Distinct().ToList();
            List<CheckDnrDatacob> result2 = (await conn.QueryAsync<CheckDnrDatacob>(_sql.SqlCheckDNR(string.Join(",", ids)))).ToList();

            List<int?> idsDnr = [];
            if (ActiveConnection == GvcRodobens.Rodobens)
                idsDnr = result2.Select(x => x.Id_Contrato).Distinct().ToList();

            foreach (var line in result.Where(x => !idsDnr.Contains(x.Id_Contrato)))
                if (responseData.FirstOrDefault(x => x.Id_Contrato == line.Id_Agrupamento || x.Id_Contrato == line.Id_Contrato && x.Id_Contrato != line.Id_Agrupamento) == null)
                    responseData.Add(line);



            response.Data = responseData;

        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosFinanciadoContrato(GvcRodobens ActiveConnection, int agrupamentoId, int? aberto)
    {

        ServiceResponse<List<DadosCadastraisDatacob>> response = new();
        try
        {

            string sql = _sql.SqlBuscaDadosFinanciados(aberto, ActiveConnection == GvcRodobens.Rodobens);
            sql += $" AND c.Id_Contrato LIKE {agrupamentoId} and c.Id_Agrupamento = {agrupamentoId}";

            sql += _sql.SqlBuscaDadosFinanciadosGroupBy();
            IDbConnection conn = CreateDatacobConnection(ActiveConnection);
            List<DadosCadastraisDatacob> result = (await conn.QueryAsync<DadosCadastraisDatacob>(sql)).ToList();

            /*add conexao list result*/
            foreach (var item in result)
                item.coddatacob = ActiveConnection == Infra.Data.EF.Enuns.GvcRodobens.Rodobens ? "Rodobens" : "GVC";


            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            List<int?> ids = result.Select(x => x.Id_Contrato).Distinct().ToList();
            List<CheckDnrDatacob> result2 = (await conn.QueryAsync<CheckDnrDatacob>(_sql.SqlCheckDNR(string.Join(",", ids)))).ToList();

            List<int?> idsDnr = [];
            if (ActiveConnection == GvcRodobens.Rodobens)
                idsDnr = result2.Select(x => x.Id_Contrato).Distinct().ToList();

            List<DadosCadastraisDatacob> responseData = [];
            foreach (var line in result.Where(x => !idsDnr.Contains(x.Id_Contrato)))
                if (responseData.FirstOrDefault(x => x.Id_Contrato == line.Id_Agrupamento || x.Id_Contrato == line.Id_Contrato && x.Id_Contrato != line.Id_Agrupamento) == null)
                    responseData.Add(line);

            response.Data = responseData;

        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<List<GroupDatacob>>> GetGroups(GvcRodobens? ActiveConnection)
    {
        ServiceResponse<List<GroupDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(ActiveConnection ?? _logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<GroupDatacob>(_sql.SqlGrupo())).ToList();

            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<DatacobDadosFinanciadoAdjacente>> GetFinanInfos(int IdFinanciado, string? numeroContrato = "")
    {
        ServiceResponse<DatacobDadosFinanciadoAdjacente> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<EmailDatacob> emails = (await conn.QueryAsync<EmailDatacob>(_sql.SqlEmails(IdFinanciado, numeroContrato ?? ""))).ToList();

            List<TelefoneDatacob> telefones = (await conn.QueryAsync<TelefoneDatacob>(_sql.SqlTelefones(IdFinanciado, numeroContrato ?? ""))).ToList();

            List<EnderecoDatacob> enderecos = (await conn.QueryAsync<EnderecoDatacob>(_sql.SqlEnderecos(IdFinanciado))).ToList();

            if (telefones.Count > 0)
            {

                var fones = (await _dpc.GetByIdFinanciado(IdFinanciado, CancellationToken.None)) ?? [];
                if (fones.Count != 0)
                {
                    foreach (var item in telefones)
                    {
                        var fone = fones.Where(x => x.IdTelefone == item.Id_Telefone).LastOrDefault();
                        item.Dt_Ultima_Ligacao = fone?.CreatedAt ?? item.Dt_Ultima_Ligacao;
                    }
                }
            }

            response.Data = new()
            {
                Emails = emails,
                Telefones = telefones,
                Enderecos = enderecos
            };
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ContratosAtivosDatacob>>> GetContratosAtivos(int IdFinanciado, int IdAgrupamento, int? Ativo, string? statusInstallment = "")
    {
        ServiceResponse<List<ContratosAtivosDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<ParcelaDatacob> result = (await conn.QueryAsync<ParcelaDatacob>(_sql.SqlContratosAtivos(IdFinanciado, IdAgrupamento, Ativo, statusInstallment, _logged.ActiveConnection == GvcRodobens.Rodobens))).ToList();

            List<ContratosAtivosDatacob> responseData = [];
            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
            result.OrderBy(x => x.Id_Contrato).OrderBy(x => x.Nr_Parcela);

            ParcelaDatacob fParcela = result.First();

            //List<ParcelaDatacob> resultsOther = (await conn.QueryAsync<ParcelaDatacob>(_sql.SqlContratosAtivosOther(fParcela.Numero_Contrato ?? "", IdFinanciado, IdAgrupamento, Ativo, statusInstallment, _logged.ActiveConnection == GvcRodobens.Rodobens))).ToList();
            //if (resultsOther != null && resultsOther.Count > 0) result.AddRange(resultsOther);

            var calculo = GetParametroCalculo(fParcela.GrupoId ?? 0, fParcela.Fase).Result.Data;

            bool custaPreenchida = false;
            //int contratoId = 0;
            List<ListaCustasDatacob> custas = [];
            List<int> contratosId = [];

            List<int?> ids = result.Select(x => x.Id_Contrato).Distinct().ToList();
            List<CheckDnrDatacob> result2 = (await conn.QueryAsync<CheckDnrDatacob>(_sql.SqlCheckDNR(string.Join(",", ids)))).ToList();

            List<int?> idsDnr = [];
            if (_logged.ActiveConnection == GvcRodobens.Rodobens)
                idsDnr = result2.Select(x => x.Id_Contrato).Distinct().ToList();

            foreach (var line in result.Where(x => !idsDnr.Contains(x.Id_Contrato)))
            {
                if (!contratosId.Contains(line.Id_Contrato ?? 0))
                {
                    contratosId.Add(line.Id_Contrato ?? 0);
                    custas = GetCustasContrato(line.Id_Contrato ?? 0).Result;
                    custaPreenchida = false;
                }


                if (calculo is not null && calculo.Count > 0)
                {
                    /*if (line.Numero_Contrato == "001173 0624 00" && line.Nr_Parcela == 19)
                        Thread.Sleep(100);*/


                    var atraso = result.OrderByDescending(x => x.Atraso).FirstOrDefault(x => x.Numero_Contrato == line.Numero_Contrato && x.Status == "A")?.Atraso ?? 0;

                    foreach (var calc in calculo)
                    {
                        if (calc.Dias_De_Calc <= atraso && calc.Dias_Ate_Calc >= atraso)
                        {
                            var multa = Math.Round(((line.Vl_Original ?? 0) * (calc.Perc_Multa ?? 0) / 100), 2);
                            var juros = Math.Round(((line.Vl_Original ?? 0) / 30 * (calc.Perc_Juros ?? 00) / 100 * (line.Atraso ?? 0)), 2);
                            var honor = Math.Round((((line.Vl_Original ?? 0) + multa + juros) * (calc.Perc_Honor ?? 0) / 100), 2);
                            if (line.Vl_Saldo < 1)
                            {
                                line.Vl_Atualizado = line.Vl_Saldo;
                            }
                            else
                            {
                                line.Vl_Atualizado = line.Vl_Saldo + multa + honor + juros;
                            }
                            line.Vl_Desc_Max = line.Vl_Atualizado - multa * calc.Perc_Desc_Multa / 100 - juros * calc.Perc_Desc_Juros / 100;
                        }
                    }
                }

                if (line.Status == "D")
                {
                    line.Vl_Atualizado = line.Vl_Original;
                    line.Vl_Desc_Max = line.Vl_Original;
                }
                else if (line.Status == "P")
                {
                    line.Vl_Saldo = 0;
                    line.Vl_Desc_Max = 0;
                    line.Vl_Atualizado = 0;
                }
                else if (line.Status == "A" && line.Atraso == 0)
                {
                    line.Vl_Desc_Max = line.Vl_Original;
                    line.Vl_Atualizado = line.Vl_Original;
                }

                line.Vl_Atualizado = Math.Truncate(100 * line.Vl_Atualizado ?? 0) / 100;
                line.Vl_Saldo = Math.Truncate(100 * line.Vl_Saldo ?? 0) / 100;
                line.Vl_Desc_Max = Math.Truncate(100 * line.Vl_Desc_Max ?? 0) / 100;
                line.Vl_Original = Math.Truncate(100 * line.Vl_Original ?? 0) / 100;

                if (custas is not null && custas.Count > 0)
                {
                    var custa = custas.Where(c => c.Dt_Dev is null);
                    decimal vlCusta = 0.00m;
                    foreach (var cus in custa)
                    {
                        vlCusta += cus.Vl_saldo ?? 0;
                    }
                    if (!custaPreenchida && line.Status == "A" && line.Nome_Tipo_Parcela != "DIF_PARCELAS")
                    {
                        line.Vl_Atualizado += vlCusta;
                        line.Vl_Desc_Max += vlCusta;
                        line.Vl_Custa = vlCusta;
                        custaPreenchida = true;
                    }
                }

                var contrato = responseData.FirstOrDefault(x => x.Id_Contrato == line.Id_Contrato);
                if (contrato != null)
                {
                    if (contrato.Parcelas.FirstOrDefault(x => x.Id_Parcela == line.Id_Parcela) == null)
                        contrato.Parcelas.Add(line);
                }
                else
                {
                    List<IndicadoresCampanhaDatacob>? ind = (await conn.QueryAsync<IndicadoresCampanhaDatacob>(_sql.SqlIndicadoresCampanha(line.Id_Contrato ?? 0))).ToList();

                    responseData.Add(new()
                    {
                        Id_Contrato = line.Id_Contrato,
                        Numero_Contrato = line.Numero_Contrato,
                        Contrato_Aberto = line.Contrato_Aberto,
                        Grupo = line.Grupo,
                        GrupoId = line.GrupoId,
                        Fase = line.Fase,
                        Cpfcnpj = line.Cpfcnpj,
                        Parcelas = new() { line },
                        Tx_Contrato = line.Tx_Contrato,
                        Tx_Mora = line.Tx_Mora,
                        Tx_Multa = line.Tx_Multa,
                        Campanha = ind,
                        Cliente = line.Cliente
                    });
                }
            }


            response.Data = responseData;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<List<ListaCustasDatacob>> GetCustasContrato(int IdContrato)
    {
        List<ListaCustasDatacob> response = [];
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response = (await conn.QueryAsync<ListaCustasDatacob>(_sql.SqlCustasContrato(IdContrato))).ToList();
        }
        catch
        {
        }

        return response;
    }

    public async Task<ServiceResponse<List<DetalhesContratosDatacob>>> GetDetalhesContratos(int IdFinanciado, int IdGrupo)
    {
        ServiceResponse<List<DetalhesContratosDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<DetalhesContratosDatacob>(_sql.GetDetalhesContratos(IdFinanciado, IdGrupo, _logged.ActiveConnection == GvcRodobens.Rodobens))).ToList();

            foreach (var item in response.Data)
            {
                item.Padrao = await conn.QueryFirstAsync<ContratoPadraoDatacob>(_sql.SqlContratoPadrao(item.Id_Contrato ?? 0));
            }


            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<DadosBensDatacob>>> GetDadosBens(int IdContrato)
    {
        ServiceResponse<List<DadosBensDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<DadosBensDatacob>(_sql.SqlDadosBens(IdContrato))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<DadosAvalistasDatacob>>> GetDadosAvalistas(int IdAgrupamento)
    {
        ServiceResponse<List<DadosAvalistasDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<DadosAvalistasDatacob> result = (await conn.QueryAsync<DadosAvalistasDatacob>(_sql.SqlDadosAvalistas(IdAgrupamento))).ToList();

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            foreach (var dados in result) dados.Tipo = _helper.TipoFinanciado(dados.Tipo_Financiado);

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<DatacobGrupoContratoAbertoResponse>>> GetGrupoContratoAberto(string Cpfcnpj, int IdAgrupamento)
    {
        ServiceResponse<List<DatacobGrupoContratoAbertoResponse>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<GrupoContratoAbertoDatacob> result = (await conn.QueryAsync<GrupoContratoAbertoDatacob>(_sql.SqlGrupoContratoAberto(Cpfcnpj, IdAgrupamento))).ToList();

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            List<DatacobGrupoContratoAbertoResponse> responseData = [];
            foreach (var line in result)
            {
                var verif = responseData.FirstOrDefault(x => x.IdAgrupamento == line.Id_Agrupamento);
                if (verif == null)
                {
                    responseData.Add(new()
                    {
                        IdAgrupamento = line.Id_Agrupamento,
                        IdContrato = line.Id_Contrato,
                        Grupo = line.Descricao,
                        IdFinanciado = line.Id_Financiado,
                        NrContrato = line.Numero_Contrato,
                        Qtd = result.Where(x => x.Id_Agrupamento == line.Id_Agrupamento).Count(),
                        IdGrupo = line.IdGrupo
                    });
                }
            }

            response.Data = responseData;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<NegociacoesDatacob>>> GetNegociacoes(
        int IdContrato,
        int? IdParcela,
        bool? DetalheParcela = true,
        string? numeroContrato = "",
        int? groupId = null,
        int? linkedGroupId = null
    )
    {
        ServiceResponse<List<NegociacoesDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<NegociacoesDatacob> result = (await conn.QueryAsync<NegociacoesDatacob>(_sql.SqlNegociacoes(IdContrato, IdParcela, DetalheParcela))).ToList();

            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.BankSlip, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
            {
                response.Data ??= [];
                response.Data.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<NegociacoesDatacob>(_sql.SqlNegociacoesContrato(numeroContrato ?? "", IdContrato, IdParcela, DetalheParcela));
                    }, [])
                );
            }

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            var users = GetUsers(_logged.ActiveConnection).Result.Data;

            foreach (var item in result)
            {
                if (item.Id_Boleto is not null)
                {
                    var invoice = await _di.GetByIdBoleto(item.Id_Boleto ?? 0, CancellationToken.None);
                    if (invoice is not null)
                    {
                        item.UserBoleto = invoice?.User.Name;
                        item.UserNegociacao = users?.FirstOrDefault(x => x.Id_Usuario == (invoice?.UserNegotiation ?? 0))?.Nome;
                        item.Mensagem = invoice?.Message;
                    }
                    else
                    {
                        item.UserBoleto = users?.FirstOrDefault(x => x.Id_Usuario == (item?.Id_Cobrador_Gerador ?? 0))?.Nome;
                        item.UserNegociacao = item.NomeUsuario;
                    }
                }
            }

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<NegociacaoParcelasDatacob>>> GetNegociacaoParcelas(int IdNegociacao, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<NegociacaoParcelasDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<NegociacaoParcelasDatacob>(_sql.SqlNegociacaoParcelas(IdNegociacao))).ToList();

            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.BankSlip, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
            {
                response.Data ??= [];
                response.Data.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<NegociacaoParcelasDatacob>(_sql.SqlNegociacaoParcelasContrato(numeroContrato));
                    }, [])
                );
            }

            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<NegociacoesDetalhesDatacob>>> GetNegociacaoDetalhes(int IdNegociacao)
    {
        ServiceResponse<List<NegociacoesDetalhesDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<NegociacoesDetalhesDatacob>(_sql.SqlNegociacoesDetalhes(IdNegociacao))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<NegDetalheDatacob>> GetNegociacaoCalculo(int IdNegociacao, GvcRodobens? crm)
    {
        ServiceResponse<NegDetalheDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm ?? _logged.ActiveConnection);
            List<NegociacoesCalculoDatacob> result = (await conn.QueryAsync<NegociacoesCalculoDatacob>(_sql.SqlNegociacoesCalculo(IdNegociacao))).ToList();

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            int param = result.First().Id_Parametro_Calculo_Pc ?? 0;
            int faixa = result.First().Id_Faixa_Calculo ?? 0;

            NegCalculoParametro? parametros = await conn.QueryFirstAsync<NegCalculoParametro>(_sql.SqlNegociacoesCalculoParametro(param, faixa));

            if (parametros is null)
            {
                response.Message = "Parâmetros não encontrados.";
                response.Success = false;
                return response;
            }

            NegociacoesCalculoDatacob data = new();
            NegDetalheDatacob resp = new();
            foreach (var parcela in result)
            {
                resp.Id_Negociacao = parcela.Id_Negociacao;

                resp.Real.Original += parcela.Vl_Principal_Original ?? 0;
                resp.Real.DescontoPerc = parametros.Desc_Original ?? 0;
                resp.Real.Desconto = CalcDesconto(resp.Real.DescontoPerc, resp.Real.Original);
                resp.Real.Negociacao += parcela.Vl_Principal ?? 0;
                resp.Real.NegociacaoPerc = CalcPercNeg(resp.Real.Negociacao, resp.Real.Original);

                resp.Correcao.Original += parcela.Vl_Correcao_Monetaria_Original ?? 0;
                resp.Correcao.Negociacao += parcela.Vl_Correcao_Monetaria ?? 0;
                resp.Correcao.NegociacaoPerc = CalcPercNeg(resp.Correcao.Negociacao, resp.Correcao.Original);

                resp.Atualizado.Original += parcela.Vl_Atualizado_Original ?? 0;
                resp.Atualizado.DescontoPerc = parametros.Desc_Original ?? 0;
                resp.Atualizado.Desconto = CalcDesconto(resp.Atualizado.DescontoPerc, resp.Atualizado.Original);
                resp.Atualizado.Negociacao += parcela.Vl_Atualizado ?? 0;
                resp.Atualizado.NegociacaoPerc = CalcPercNeg(resp.Atualizado.Negociacao, resp.Atualizado.Original);

                resp.Juros.Original += parcela.Vl_Juros_Original ?? 0;
                resp.Juros.DescontoPerc = parametros.Perc_Desc_Juros ?? 0;
                resp.Juros.Desconto = CalcDesconto(resp.Juros.DescontoPerc, resp.Juros.Original);
                resp.Juros.Negociacao += parcela.Vl_Juros ?? 0;
                resp.Juros.NegociacaoPerc = CalcPercNeg(resp.Juros.Negociacao, resp.Juros.Original);

                resp.Multa.Original += parcela.Vl_Multa_Original ?? 0;
                resp.Multa.DescontoPerc = parametros.Perc_Desc_Multa ?? 0;
                resp.Multa.Desconto = CalcDesconto(resp.Multa.DescontoPerc, resp.Multa.Original);
                resp.Multa.Negociacao += parcela.Vl_Multa ?? 0;
                resp.Multa.NegociacaoPerc = CalcPercNeg(resp.Multa.Negociacao, resp.Multa.Original);

                resp.Comissao.Original += parcela.Vl_Comissao_Permanencia_Original ?? 0;
                resp.Comissao.Negociacao += parcela.Vl_Comissao_Permanencia ?? 0;
                resp.Comissao.NegociacaoPerc = CalcPercNeg(resp.Comissao.Negociacao, resp.Comissao.Original);


                resp.SubTotal.Original = resp.Real.Original + resp.Correcao.Original + resp.Juros.Original + resp.Multa.Original + resp.Comissao.Original;
                resp.SubTotal.Desconto = resp.Real.Desconto + resp.Correcao.Desconto + resp.Juros.Desconto + resp.Multa.Desconto + resp.Comissao.Desconto;
                resp.SubTotal.Negociacao = resp.Real.Negociacao + resp.Correcao.Negociacao + resp.Juros.Negociacao + resp.Multa.Negociacao + resp.Comissao.Negociacao;

                resp.Honorario.Original += parcela.Vl_Honorario_Original ?? 0;
                resp.Honorario.Negociacao += parcela.Vl_Honorario ?? 0;
                resp.Honorario.NegociacaoPerc = parcela.Percentual_Honorarios_Negociacao ?? 0;
                resp.Honorario.Desconto = (parametros.Perc_Honor ?? 0) / 100 * resp.SubTotal.Desconto;

                resp.Despesa.Original += parcela.Vl_Custas_Original ?? 0;
                resp.Despesa.Negociacao += parcela.Vl_Custas ?? 0;
                resp.Despesa.NegociacaoPerc = CalcPercNeg(resp.Despesa.Negociacao, resp.Despesa.Original);

                resp.Notificacao.Original += parcela.Vl_Notificacao_Original ?? 0;
                resp.Notificacao.Negociacao += parcela.Vl_Notificacao ?? 0;
                resp.Notificacao.NegociacaoPerc = CalcPercNeg(resp.Notificacao.Negociacao, resp.Notificacao.Original);

                resp.Tarifa.Original += parcela.Vl_Tarifa_Original ?? 0;
                resp.Tarifa.Negociacao += parcela.Vl_Tarifa ?? 0;
                resp.Tarifa.NegociacaoPerc = CalcPercNeg(resp.Tarifa.Negociacao, resp.Tarifa.Original);

                resp.Iof.Original += parcela.Vl_Iof_Original ?? 0;
                resp.Iof.Negociacao += parcela.Vl_Iof ?? 0;
                resp.Iof.NegociacaoPerc = CalcPercNeg(resp.Iof.Negociacao, resp.Iof.Original);

                resp.Total.Original += parcela.Vl_Total_Original ?? 0;
                resp.Total.Desconto = resp.SubTotal.Desconto + resp.Honorario.Desconto + resp.Despesa.Desconto + resp.Notificacao.Desconto + resp.Tarifa.Desconto + resp.Iof.Desconto;
                resp.Total.DescontoPerc = CalcPercNeg(resp.Total.Desconto, resp.Total.Original);
                resp.Total.Negociacao += parcela.Vl_Total ?? 0;
                resp.Total.NegociacaoPerc = CalcPercNeg(resp.Total.Negociacao, resp.Total.Original);

                resp.DescontoAutorizado += parcela.Vl_Desconto_Excedido ?? 0;
            }
            //data.Percentual_Total_Negociacao = Math.Round((data.Total_Original_Desconto - data.Total_Negociacao) * 100 / data.Total_Original_Desconto, 2);
            resp.Primeira_Parcela = result.First().Primeira_Parcela ?? 0;
            resp.Ultima_Parcela = result.Last().Primeira_Parcela ?? 0;

            response.Data = resp;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    private decimal CalcDesconto(decimal v1, decimal v2)
    {
        if (v1 <= 0) return 0;
        decimal val = ((100 - v1) / 100) * v2;
        return Math.Round(val * 100) / 100;
    }

    private decimal CalcPercNeg(decimal v1, decimal v2)
    {
        if (v1 == 0 || v2 == 0) return 0;
        return -((v1 * 100 / v2) - 100);
    }

    public async Task<ServiceResponse<List<NegociacaoAcordosDatacob>>> GetNegociacaoAcordos(int IdAgrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<NegociacaoAcordosDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<NegociacaoAcordosDatacob>(_sql.SqlNegociacaoAcordos(IdAgrupamento, _logged.ActiveConnection))).ToList();

            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Agreement, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
            {
                response.Data ??= [];
                response.Data.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<NegociacaoAcordosDatacob>(_sql.SqlNegociacaoAcordosContrato(numeroContrato));
                    }, [])
                );
                //response.Data.AddRange((await linkedConn.QueryAsync<NegociacaoAcordosDatacob>(_sql.SqlNegociacaoAcordosContrato(numeroContrato))).ToList());
            }

            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<NegociacaoAcordosParcelasDatacob>>> GetNegociacaoAcordosParcelas(int IdAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<NegociacaoAcordosParcelasDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<NegociacaoAcordosParcelasDatacob>(_sql.SqlParcelasAcordo(IdAcordo, _logged.ActiveConnection))).ToList();

            response.Data ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Agreement, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                response.Data.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<NegociacaoAcordosParcelasDatacob>(_sql.SqlParcelasAcordoContrato(numeroContrato, IdAcordo));
                    }, [])
                );
            //response.Data.AddRange([.. await linkedConn.QueryAsync<NegociacaoAcordosParcelasDatacob>(_sql.SqlParcelasAcordoContrato(numeroContrato, IdAcordo))]);

            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParcelasAcordoBoletoDatacob>>> GetParcelasAcordoBoleto(int IdParcelaAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<ParcelasAcordoBoletoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<ParcelasAcordoBoletoDatacob> result = (await conn.QueryAsync<ParcelasAcordoBoletoDatacob>(_sql.SqlParcelasAcordoBoleto(IdParcelaAcordo))).ToList();

            result ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Agreement, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                result.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<ParcelasAcordoBoletoDatacob>(_sql.SqlParcelasAcordoBoletoContrato(numeroContrato, IdParcelaAcordo));
                    }, [])
                );
            //result.AddRange([.. await linkedConn.QueryAsync<ParcelasAcordoBoletoDatacob>(_sql.SqlParcelasAcordoBoletoContrato(numeroContrato, IdParcelaAcordo))]);

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
            var logged = _logged;
            var users = GetUsers(logged.ActiveConnection).Result.Data;

            foreach (var item in result)
            {
                if (item.Id_Boleto is not null)
                {
                    var invoice = await _di.GetByIdBoleto(item.Id_Boleto ?? 0, CancellationToken.None);
                    item.UserBoleto = invoice?.User.Name;
                    item.UserNegociacao = users?.FirstOrDefault(x => x.Id_Usuario == (invoice?.UserNegotiation ?? 0))?.Nome;
                    item.Mensagem = invoice?.Message;
                }
            }

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParcelasAcordoReciboDatacob>>> GetParcelasAcordoRecibo(int IdParcelaAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<ParcelasAcordoReciboDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<ParcelasAcordoReciboDatacob> result = (await conn.QueryAsync<ParcelasAcordoReciboDatacob>(_sql.SqlParcelasAcordoRecibo(IdParcelaAcordo))).ToList();

            result ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Agreement, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                result.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<ParcelasAcordoReciboDatacob>(_sql.SqlParcelasAcordoReciboContrato(numeroContrato, IdParcelaAcordo));
                    }, [])
                );
            //result.AddRange([.. await linkedConn.QueryAsync<ParcelasAcordoReciboDatacob>(_sql.SqlParcelasAcordoReciboContrato(numeroContrato, IdParcelaAcordo))]);

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            var users = GetUsers(_logged.ActiveConnection).Result.Data;

            foreach (var item in result)
            {
                if (item.Id_Boleto is not null)
                {
                    var invoice = await _di.GetByIdBoleto(item.Id_Boleto ?? 0, CancellationToken.None);
                    item.UserBoleto = invoice?.User.Name;
                    item.UserNegociacao = users?.FirstOrDefault(x => x.Id_Usuario == (invoice?.UserNegotiation ?? 0))?.Nome;
                    item.Mensagem = invoice?.Message;
                }
            }

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParcelasAcordoNegociacaoDatacob>>> GetParcelasAcordoNegociacao(int IdParcelaAcordo, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<ParcelasAcordoNegociacaoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<ParcelasAcordoNegociacaoDatacob>(_sql.SqlParcelasAcordoNegociacao(IdParcelaAcordo))).ToList();

            response.Data ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Agreement, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                response.Data.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<ParcelasAcordoNegociacaoDatacob>(_sql.SqlParcelasAcordoNegociacaoContrato(numeroContrato, IdParcelaAcordo));
                    }, [])
                );
            //response.Data.AddRange([.. await linkedConn.QueryAsync<ParcelasAcordoNegociacaoDatacob>(_sql.SqlParcelasAcordoNegociacaoContrato(numeroContrato, IdParcelaAcordo))]);

            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>>> GetParcelasNegociacaoBoleto(int IdNegociacao, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {

        ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<ParcelasNegociacaoBoletoDatacob> result = (await conn.QueryAsync<ParcelasNegociacaoBoletoDatacob>(_sql.SqlParcelasNegociacaoBoleto(IdNegociacao))).ToList();

            result ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.BankSlip, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                result.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<ParcelasNegociacaoBoletoDatacob>(_sql.SqlParcelasNegociacaoBoletoContrato(numeroContrato));
                    }, [])
                );
            //result.AddRange([.. await linkedConn.QueryAsync<ParcelasNegociacaoBoletoDatacob>(_sql.SqlParcelasNegociacaoBoletoContrato(numeroContrato))]);

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            var users = GetUsers(_logged.ActiveConnection).Result.Data;
            foreach (var item in result)
            {
                item.UserIdBoleto = item?.Id_Cobrador_Gerador;
                item!.Cobrador_Gerador = users?.FirstOrDefault(x => x.Id_Usuario == (item.Id_Cobrador_Gerador ?? 0))?.Login.Trim();
                if (item.Id_Boleto is not null)
                {
                    var invoice = await _di.GetByIdBoleto(item.Id_Boleto ?? 0, CancellationToken.None);
                    if (invoice != null)
                    {
                        item.UserBoleto = invoice?.User.Name;
                        item.UserNameBoleto = invoice?.User.Username;
                        item.UserIdBoleto = invoice?.User.Id;
                        item.UserNegociacao = users?.FirstOrDefault(x => x.Id_Usuario == (invoice?.UserNegotiation ?? 0))?.Nome;
                        item.Mensagem = invoice?.Message;
                        item.Cobrador_Gerador = item.UserNameBoleto;
                    }
                }
            }

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParcelasNegociacaoReciboDatacob>>> GetParcelasNegociacaoRecibo(int IdNegociacao)
    {

        ServiceResponse<List<ParcelasNegociacaoReciboDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<ParcelasNegociacaoReciboDatacob>(_sql.SqlParcelasNegociacaoRecibo(IdNegociacao))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ListaCustasDatacob>>> GetCustas(int IdContrato)
    {
        ServiceResponse<List<ListaCustasDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<ListaCustasDatacob>(_sql.SqlCustas(IdContrato))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<HistoricoAtendimentoDatacob>>> GetHistoricoAtendimentoDatacob(int Id_Agrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<HistoricoAtendimentoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<HistoricoAtendimentoDatacob> result = (await conn.QueryAsync<HistoricoAtendimentoDatacob>(_sql.SqlHistoricoAtendimento(Id_Agrupamento))).ToList();

            result ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Occurrence, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                result.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<HistoricoAtendimentoDatacob>(_sql.SqlHistoricoAtendimentoContrato(numeroContrato));
                    }, [])
                );
            //result.AddRange([.. await linkedConn.QueryAsync<HistoricoAtendimentoDatacob>(_sql.SqlHistoricoAtendimentoContrato(numeroContrato))]);

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
            int?[] ContratosId = result.Select(x => x.Id_Contrato).Distinct().ToArray();

            var listHist = await _dor.GetByIdContrato(ContratosId, CancellationToken.None) ?? [];

            foreach (var item in result)
            {
                var hist = listHist.Where(x => x.IdHistorico == item.Id_Historico).FirstOrDefault();

                item.CallType = hist?.CallType;
            }

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<NegociacaoParcelasDetalhesDatacob>> GetNegociacaoParcelaDetalhes(int IdParcela, int IdNegociacao)
    {
        ServiceResponse<NegociacaoParcelasDetalhesDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            NegociacaoParcelasDetalhesDatacob? result = await conn.QueryFirstAsync<NegociacaoParcelasDetalhesDatacob>(_sql.SqlNegociacaoParcelaDetalhes(IdParcela, IdNegociacao));

            if (result is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            result.Percentual_Total_Negociacao = Math.Round((result.Total_Original_Desconto - result.Total_Negociacao) * 100 / result.Total_Original_Desconto, 2);

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<OcorrenciaDatacob>>> GetOcorrencias(int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<OcorrenciaDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<OcorrenciaDatacob>(_sql.SqlOcorrencias(_logged.Username))).ToList();

            response.Data ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Occurrence, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                response.Data.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<OcorrenciaDatacob>(_sql.SqlOcorrencias(_logged.Username));
                    }, [])
                );
            //response.Data.AddRange([.. await linkedConn.QueryAsync<OcorrenciaDatacob>(_sql.SqlOcorrencias(_logged.Username))]);

            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<OcorrenciaDatacob>>> GetOcorrenciasCrm(GvcRodobens crm)
    {
        ServiceResponse<List<OcorrenciaDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            response.Data = (await conn.QueryAsync<OcorrenciaDatacob>(_sql.SqlOcorrencias(_logged.Username))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<HistoricoResumoDatacob>>> GetHistoricoResumoDatacob(int Id_Agrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<HistoricoResumoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<HistoricoResumoDatacob> result = (await conn.QueryAsync<HistoricoResumoDatacob>(_sql.SqlHistoricoResumo(Id_Agrupamento))).ToList();

            result ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.Occurrence, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                result.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<HistoricoResumoDatacob>(_sql.SqlHistoricoResumoContrato(0, numeroContrato));
                    }, [])
                );
            //result.AddRange([.. await linkedConn.QueryAsync<HistoricoResumoDatacob>(_sql.SqlHistoricoResumoContrato(0, numeroContrato))]);

            if (result is null || result.Count == 0) throw new("Dados não encontrados.");

            int?[] ContratosId = result.Select(x => x.Id_Contrato).Distinct().ToArray();
            var listHist = await _dor.GetByIdContrato(ContratosId, CancellationToken.None) ?? [];
            foreach (var item in result)
            {
                var hist = listHist.Where(x => x.IdHistorico == item.Id_Historico).FirstOrDefault();
                item.CallType = hist?.CallType;
            }

            response.Data = result;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParametroCalculo>>> GetParametroCalculo(int Grupo, string? Fase)
    {

        ServiceResponse<List<ParametroCalculo>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<ParametroCalculo>(_sql.SqlParametroCalculo(Grupo))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<TipoParcelaDatacob>>> GetTipoParcela()
    {
        ServiceResponse<List<TipoParcelaDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<TipoParcelaDatacob>(_sql.SqlTipoParcela())).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ContratosDatacob>>> GetContratos(int IdAgrupamento)
    {
        ServiceResponse<List<ContratosDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<ParcelaDatacob> result = (await conn.QueryAsync<ParcelaDatacob>(_sql.SqlContratos(IdAgrupamento))).ToList();

            List<ContratosDatacob> responseData = [];
            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            response.Data = responseData;
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<FaseDatacob>>> GetFases()
    {
        ServiceResponse<List<FaseDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<FaseDatacob>(_sql.SqlFases())).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<CalculoAcordoDatacob>> GetAcordoCalculoData(int IdAcordo, GvcRodobens? crm)
    {
        ServiceResponse<CalculoAcordoDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm ?? _logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<CalculoAcordoDatacob>(_sql.SqlCalculoDataAcordo(IdAcordo))).FirstOrDefault();
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<CidadeDatacob>>> GetCidadeUf()
    {
        ServiceResponse<List<CidadeDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<CidadeDatacob>(_sql.SqlCidadeUf())).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<HistoricoIdDatacob>> GetUltimoHistorico(int ContratoId, string OcorrenciaCod, GvcRodobens? crm = null)
    {
        ServiceResponse<HistoricoIdDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<HistoricoIdDatacob>(_sql.SqlHistoricoId(ContratoId, OcorrenciaCod));
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<HistoricoIdDatacob>> GetUltimoHistoricoInt(int ContratoId, int OcorrenciaCod, GvcRodobens? crm = null)
    {
        ServiceResponse<HistoricoIdDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<HistoricoIdDatacob>(_sql.SqlHistoricoIdInt(ContratoId, OcorrenciaCod));
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<TelefoneIdDatacob>> GetTelefoneId(string Telefone, int IdAgrupamento)
    {
        ServiceResponse<TelefoneIdDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<TelefoneIdDatacob>(_sql.SqlTelefoneByAgrupamento(Telefone, IdAgrupamento));
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<IndicadoresCampanhaDatacob>>> GetIndicadoresCampanha(int IdContrato)
    {
        ServiceResponse<List<IndicadoresCampanhaDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<IndicadoresCampanhaDatacob>(_sql.SqlIndicadoresCampanha(IdContrato))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<NegociacaoAbertaRpaDatacob>> GetNegOpenRpa(int IdAgrupamento, int IdUsuario, decimal VlNeg, GvcRodobens crm)
    {
        ServiceResponse<NegociacaoAbertaRpaDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<NegociacaoAbertaRpaDatacob>(_sql.SqlOpenNegRPA(IdAgrupamento, IdUsuario, VlNeg));
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<NegociacaoAbertaRpaDatacob>> GetNegOpenCrm(int IdAgrupamento, int IdUsuario, decimal VlNeg, DateTime DtNegociacao)
    {
        ServiceResponse<NegociacaoAbertaRpaDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<NegociacaoAbertaRpaDatacob>(_sql.SqlOpenNegCRM(), new
            {
                IdGrouping = IdAgrupamento,
                IdUsuario = IdUsuario,
                VlNeg = VlNeg,
                DtNegociacao = DtNegociacao.ToString("yyyy-MM-dd")
            });
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<NegociacaoStatusRpaDatacob>> GetNegById(int IdNegociacao, GvcRodobens crm)
    {
        ServiceResponse<NegociacaoStatusRpaDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<NegociacaoStatusRpaDatacob>(_sql.SqNegById(IdNegociacao));
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<IndicadoresOcorrenciaDatacob>>> GetIndicadoresOcorrencia(int IdAgrupamento)
    {
        ServiceResponse<List<IndicadoresOcorrenciaDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<IndicadoresOcorrenciaDatacob>(_sql.SqlIndicadoresOcorrencia(IdAgrupamento))).ToList();
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<NrNegociacaoDatacob>> GetNrNegociacao(int IdAgrupamento)
    {
        ServiceResponse<NrNegociacaoDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = await conn.QueryFirstAsync<NrNegociacaoDatacob>(_sql.SqlNrNegociacao(IdAgrupamento));
            if (response.Data is null) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<GvcRodobens?>> FindCrm(int IdContrato, string documento)
    {
        ServiceResponse<GvcRodobens?> res = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            var userGvc = await conn.QueryFirstAsync<FindCrmDatacob>(_sql.SqlFindCrm(IdContrato, documento));
            if (userGvc != null) return new ServiceResponse<GvcRodobens?>() { Data = GvcRodobens.GVC };

        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }

        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.Rodobens);
            var userRod = await conn.QueryFirstAsync<FindCrmDatacob>(_sql.SqlFindCrm(IdContrato, documento));
            if (userRod != null) return new ServiceResponse<GvcRodobens?>() { Data = GvcRodobens.Rodobens };
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }
        return res;
    }

    public async Task<ServiceResponse<List<OutrosContratosDatacob>>> GetOutrosContratos(int IdGrupo, string Documento, GvcRodobens CrmAtual)
    {
        ServiceResponse<List<OutrosContratosDatacob>> res = new()
        {
            Data = []
        };
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            var contratos = await conn.QueryAsync<OutrosContratosDatacob>(_sql.SqlOutrosContratos(CrmAtual == GvcRodobens.GVC, false), new { Documento = Documento, IdGrupoIgnorar = IdGrupo });
            if (contratos != null)
                res.Data.AddRange(contratos.ToList().Select(x => new OutrosContratosDatacob() { NrContrato = x.NrContrato, IdFinanciado = x.IdFinanciado, IdAgrupamento = x.IdAgrupamento, IdGrupo = x.IdGrupo, Grupo = x.Grupo, Ativo = x.Ativo, Crm = GvcRodobens.GVC, Cliente = x.Cliente }));

        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }

        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.Rodobens);
            var contratos = await conn.QueryAsync<OutrosContratosDatacob>(_sql.SqlOutrosContratos(CrmAtual == GvcRodobens.Rodobens, true), new { Documento, IdGrupoIgnorar = IdGrupo });
            if (contratos != null)
                res.Data.AddRange(contratos.ToList().Select(x => new OutrosContratosDatacob() { NrContrato = x.NrContrato, IdFinanciado = x.IdFinanciado, IdAgrupamento = x.IdAgrupamento, IdGrupo = x.IdGrupo, Grupo = x.Grupo, Ativo = x.Ativo, Crm = GvcRodobens.Rodobens, Cliente = x.Cliente }));
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
        }
        return res;
    }

    public async Task<ServiceResponse<List<DatacobBoletosContratoResponse>>> GetBoletosContrato(int IdAgrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {
        ServiceResponse<List<DatacobBoletosContratoResponse>> response = new() { Data = [] };
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            var ret = (await conn.QueryAsync<BoletosContratoDatacob>(_sql.SqlBoletosContrato(), new { Id = IdAgrupamento })).ToList();

            ret ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.BankSlip, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                ret.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<BoletosContratoDatacob>(_sql.SqlBoletosContratoBoleto(), new { numero_Contrato = numeroContrato, idAgrupamento = 0 });
                    }, [])
                );
            //ret.AddRange([.. await linkedConn.QueryAsync<BoletosContratoDatacob>(_sql.SqlBoletosContratoBoleto(), new { numero_Contrato = numeroContrato, idAgrupamento = 0 })]);

            if (ret is null || ret.Count == 0) return response;
            foreach (var item in ret)
            {
                if (!response.Data.Where(x => x.IdBoleto == item.IdBoleto).Any())
                    response.Data.Add(new()
                    {
                        IdNegociacao = item.IdNegociacao,
                        IdBoleto = item.IdBoleto,
                        Status = item.Status,
                        NrParcelas = ret.Where(x => x.IdBoleto == item.IdBoleto).Select(x => x.NrParcela).ToList(),
                        DtVenc = item.DtVenc
                    });
            }
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<DatacobCheckStatusDnrResponse>> GetCheckStatusContrato(int IdAgrupamento)
    {
        ServiceResponse<DatacobCheckStatusDnrResponse> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            var dnr = await conn.QueryAsync<CheckDnrDatacob>(_sql.SqlCheckDNR(IdAgrupamento.ToString()));
            var status = await conn.QueryFirstAsync<CheckStatusContratoDatacob>(_sql.SqlCheckStatusContrato(), new { idAgrupamento = IdAgrupamento });
            response.Data = new()
            {
                IsDnr = dnr.Any(),
                Status = status?.Status ?? string.Empty
            };

        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<List<DetalhesParcelasDatacob>> GetDetalhesParcelas(List<int> Parcelas)
    {
        List<DetalhesParcelasDatacob> response = [];
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response = (await conn.QueryAsync<DetalhesParcelasDatacob>(_sql.SqlDetalhesParcelas(), new { Parcelas = Parcelas })).ToList();
        }
        catch
        {
        }

        return response;
    }
    public async Task<ServiceResponse<List<MotivoDevolucaoDatacob>>> GetMotivoDevolucao()
    {
        ServiceResponse<List<MotivoDevolucaoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            response.Data = (await conn.QueryAsync<MotivoDevolucaoDatacob>(_sql.SqlMotivoDevolucao())).ToList();
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<FilterSafraCampaignDatacob>>> FilterSafraCampaign(int daysDelay, int amountInstall, SafraContractType type)
    {
        ServiceResponse<List<FilterSafraCampaignDatacob>> response = new()
        {
            Data = []
        };
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            List<int> configLeves = JsonConvert.DeserializeObject<List<int>>(await _config.GetValueByKey("safra_light_client_ids")) ?? [];
            List<int> configPesados = JsonConvert.DeserializeObject<List<int>>(await _config.GetValueByKey("safra_heavy_client_ids")) ?? [];
            List<int> clientIds = type switch
            {
                SafraContractType.Leves => configLeves,
                SafraContractType.Pesados => configPesados,
                SafraContractType.Ambos => [.. configLeves, .. configPesados],
                _ => []
            };

            response.Data = (await conn.QueryAsync<FilterSafraCampaignDatacob>(_sql.SqlFilterSafraCampaign(), new { amount = amountInstall, clientIds, delay = daysDelay })).ToList();
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<byte[]> FilterSafraCampaignAndExportToExcel(int daysDelay, int amountInstall, SafraContractType type)
    {

        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            List<int> configLeves = JsonConvert.DeserializeObject<List<int>>(await _config.GetValueByKey("safra_light_client_ids")) ?? [];
            List<int> configPesados = JsonConvert.DeserializeObject<List<int>>(await _config.GetValueByKey("safra_heavy_client_ids")) ?? [];
            List<int> clientIds = type switch
            {
                SafraContractType.Leves => configLeves,
                SafraContractType.Pesados => configPesados,
                SafraContractType.Ambos => [.. configLeves, .. configPesados],
                _ => []
            };

            var data = (await conn.QueryAsync<FilterSafraCampaignDatacob>(_sql.SqlFilterSafraCampaign(), new { amount = amountInstall, clientIds, delay = daysDelay })).ToList();

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Data");

            worksheet.Cells[1, 1].Value = "Nome do Devedor";
            worksheet.Cells[1, 2].Value = "CPF/ CNPJ";
            worksheet.Cells[1, 3].Value = "Contrato";
            worksheet.Cells[1, 4].Value = "QTD de parcelas";
            worksheet.Cells[1, 5].Value = "Valor total das Parcelas";


            // Carregando os dados a partir da linha 2, logo abaixo dos cabeçalhos
            int row = 2;
            foreach (var item in data)
            {
                worksheet.Cells[row, 1].Value = item.Name;
                worksheet.Cells[row, 2].Value = item.Document;
                worksheet.Cells[row, 3].Value = item.Contract;
                worksheet.Cells[row, 4].Value = item.InstallmentAmount;
                worksheet.Cells[row, 5].Value = item.InstallmentTotalValue;
                row++;
            }

            //worksheet.Cells.LoadFromCollection(data, true);  // Carrega os dados
            return package.GetAsByteArray();

        }
        catch (Exception ex)
        {
            return new byte[0];

        }

    }

    public async Task<ServiceResponse<DadosCadastraisDatacob>> BuscaDadosFinanciadoContratoSafra(int contractId)
    {

        ServiceResponse<DadosCadastraisDatacob> response = new();
        try
        {

            string sql = _sql.SqlBuscaDadosFinanciadosSafra();
            sql += _sql.SqlBuscaDadosFinanciadosGroupBy();
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            response.Data = await conn.QueryFirstAsync<DadosCadastraisDatacob>(sql, new { contractId });

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            response.Data.coddatacob = GvcRodobens.GVC.ToString();
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }
    public async Task<ServiceResponse<PrimeiraParcelaSafraDatacob>> PrimeiraParcelaSafra(int contractId)
    {

        ServiceResponse<PrimeiraParcelaSafraDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            response.Data = await conn.QueryFirstAsync<PrimeiraParcelaSafraDatacob>(_sql.SqlPrimeiraParcelaSafra(), new { contractId });

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<List<FilterSafraCampaignDatacob>>> FilterSafraCampaignExcel(IFormFile file)
    {
        ServiceResponse<List<FilterSafraCampaignDatacob>> response = new()
        {
            Data = []
        };
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets[0];
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            int? colContrato = null;
            for (int col = 1; col <= columns; col++)
                if (worksheet.Cells[1, col].Text.ToLower() == "contrato")
                    colContrato = col;

            if (colContrato is null)
                throw new Exception("Coluna de contrato não encontrada.");

            List<string> contratos = [];

            for (int row = 2; row <= rows; row++)
                contratos.Add(worksheet.Cells[row, colContrato ?? 0].Text);

            response.Data = (await conn.QueryAsync<FilterSafraCampaignDatacob>(_sql.SqlFilterSafraCampaignExcel(), new { contratos })).ToList();
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<byte[]> FilterSafraCampaignExcelAndExportToExcel(IFormFile file)
    {
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets[0];
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            int? colContrato = null;
            for (int col = 1; col <= columns; col++)
                if (worksheet.Cells[1, col].Text.ToLower() == "contrato")
                    colContrato = col;

            if (colContrato is null)
                throw new Exception("Coluna de contrato não encontrada.");

            List<string> contratos = [];

            for (int row = 2; row <= rows; row++)
                contratos.Add(worksheet.Cells[row, colContrato ?? 0].Text);

            var data = (await conn.QueryAsync<FilterSafraCampaignDatacob>(_sql.SqlFilterSafraCampaignExcel(), new { contratos })).ToList();


            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var package2 = new ExcelPackage();
            var worksheet2 = package2.Workbook.Worksheets.Add("Data");

            worksheet2.Cells[1, 1].Value = "Nome do Devedor";
            worksheet2.Cells[1, 2].Value = "CPF/ CNPJ";
            worksheet2.Cells[1, 3].Value = "Contrato";
            worksheet2.Cells[1, 4].Value = "QTD de parcelas";
            worksheet2.Cells[1, 5].Value = "Valor total das Parcelas";


            // Carregando os dados a partir da linha 2, logo abaixo dos cabeçalhos
            int line = 2;
            foreach (var item in data)
            {
                worksheet2.Cells[line, 1].Value = item.Name;
                worksheet2.Cells[line, 2].Value = item.Document;
                worksheet2.Cells[line, 3].Value = item.Contract;
                worksheet2.Cells[line, 4].Value = item.InstallmentAmount;
                worksheet2.Cells[line, 5].Value = item.InstallmentTotalValue;
                line++;
            }

            //worksheet.Cells.LoadFromCollection(data, true);  // Carrega os dados
            return package2.GetAsByteArray();

        }
        catch (Exception ex)
        {
            return new byte[0];

        }
    }
    public async Task<ServiceResponse<EmailDatacob>> EmailFinanciadoValido(int financedId)
    {

        ServiceResponse<EmailDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            response.Data = await conn.QueryFirstAsync<EmailDatacob>(_sql.SqlFinancedValidEmail(), new { idFinanced = financedId });

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }
    public async Task<ServiceResponse<List<EmailDatacob>>> EmailFinanciadoValido(List<int> financedIds)
    {

        ServiceResponse<List<EmailDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            response.Data = (await conn.QueryAsync<EmailDatacob>(_sql.SqlFinancedValidEmail(false), new { idFinanced = financedIds })).ToList();

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<List<FinanciadoSimplesDatacob>>> FinanciadoSimples(List<int> financedIds)
    {

        ServiceResponse<List<FinanciadoSimplesDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            response.Data = (await conn.QueryAsync<FinanciadoSimplesDatacob>(_sql.SqlFinancedSimple(), new { idFinanceds = financedIds })).ToList();

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<FinanciadoSimplesDatacob>> FinanciadoSimplesById(GvcRodobens crm, int financiadoId)
    {

        ServiceResponse<FinanciadoSimplesDatacob> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            response.Data = (await conn.QueryAsync<FinanciadoSimplesDatacob>(_sql.SqlFinancedSimpleById(), new { Id_Financiado = financiadoId })).FirstOrDefault();

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>>> GetListarBoleto(int IdAgrupamento, string numeroContrato, int? groupId = null, int? linkedGroupId = null)
    {

        ServiceResponse<List<ParcelasNegociacaoBoletoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            List<ParcelasNegociacaoBoletoDatacob> result = (await conn.QueryAsync<ParcelasNegociacaoBoletoDatacob>(_sql.SqlListarBoleto(), new { IdAgrupamento })).ToList();

            result ??= [];
            IDbConnection? linkedConn = await GetLinkedConnection(ConfigIntegrationType.BankSlip, _logged.ActiveConnection, groupId ?? 0, linkedGroupId ?? 0);
            if (linkedConn is not null)
                result.AddRange(
                    await ExceptionHelpers.SafeExecuteAsync(async () => {
                        return await linkedConn.QueryAsync<ParcelasNegociacaoBoletoDatacob>(_sql.SqlListarBoletoContrato(), new { numeroContrato, idAgrupamento = 0 });
                    }, [])
                );
            //result.AddRange([.. await linkedConn.QueryAsync<ParcelasNegociacaoBoletoDatacob>(_sql.SqlListarBoletoContrato(), new { numeroContrato, idAgrupamento = 0 })]);

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            var users = GetUsers(_logged.ActiveConnection).Result.Data;
            foreach (var item in result)
            {
                item.UserIdBoleto = item?.Id_Cobrador_Gerador;
                var userCobrador = users?.FirstOrDefault(x => x.Id_Usuario == (item?.Id_Cobrador_Gerador ?? 0));
                item.Cobrador_Gerador = userCobrador?.Nome.Trim();
                if (item.Id_Boleto is not null)
                {
                    var invoice = await _di.GetByIdBoleto(item.Id_Boleto ?? 0, CancellationToken.None);
                    if (invoice != null)
                    {
                        item.UserBoleto = invoice?.User.Name;
                        item.UserNameBoleto = invoice?.User.Username;
                        item.UserIdBoleto = invoice?.User.Id;
                        item.UserNegociacao = users?.FirstOrDefault(x => x.Id_Usuario == (invoice?.UserNegotiation ?? 0))?.Nome;
                        item.Mensagem = invoice?.Message;
                        item.Cobrador_Gerador = item.UserNameBoleto;
                    }
                    else
                    {
                        if (userCobrador is not null)
                        {
                            item.UserBoleto = userCobrador.Nome;
                            item.UserNameBoleto = userCobrador.Login;
                            item.UserIdBoleto = userCobrador.Id_Usuario;
                            item.UserNegociacao = userCobrador.Nome;
                        }
                    }
                }
            }

            response.Data = result.OrderByDescending(a => a.Dt_Venc).ToList();
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<List<ParcelaIdDatacob>> GetIdsParcelas(int IdContrato, List<int> nrParcelas)
    {
        try
        {
            IDbConnection conn = CreateDatacobConnection(_logged.ActiveConnection);
            return (await conn.QueryAsync<ParcelaIdDatacob>(_sql.SqlParcelasIds(), new { IdContrato, nrParcelas })).ToList();
        }
        catch
        {
        }

        return [];
    }

    public async Task<ServiceResponse<List<BoletosUsuariosDatacob>>> BuscaBoletosUsuarioLogado(GvcRodobens crm, DateTime inicio, DateTime fim)
    {
        ServiceResponse<List<BoletosUsuariosDatacob>> response = new()
        {
            Data = []
        };
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            var user = await GetUserByUsername(_logged.Username, crm);
            if (user is null)
            {
                response.Message = "Usuário não encontrado.";
                response.Success = false;
                return response;
            }

            List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> result = [.. (await conn.QueryAsync<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente>(_sql.SqlBoletosUsuarioLogado(), new { dtInicio = inicio, dtFim = fim.AddDays(1), idUsuario = user.Id_Usuario }))];

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            var group = result.GroupBy(x => x.NomeCliente).ToList();
            foreach (var item in group)
            {
                var cliente = new BoletosUsuariosDatacob
                {
                    NomeCliente = item.Key,
                    Boletos = [.. item]
                };

                response.Data.Add(cliente);
            }

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public async Task<ServiceResponse<List<BoletosUsuariosDatacob>>> BuscaBoletosUsuarioLogado(GvcRodobens crm, int idUsuario, DateTime inicio, DateTime fim)
    {
        ServiceResponse<List<BoletosUsuariosDatacob>> response = new()
        {
            Data = []
        };
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);

            List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> result = [.. (await conn.QueryAsync<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente>(_sql.SqlBoletosUsuarioLogado(), new { dtInicio = inicio, dtFim = fim.AddDays(1), idUsuario = idUsuario }))];

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            var group = result.GroupBy(x => x.NomeCliente).ToList();
            foreach (var item in group)
            {
                var cliente = new BoletosUsuariosDatacob
                {
                    NomeCliente = item.Key,
                    Boletos = [.. item]
                };

                response.Data.Add(cliente);
            }

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }
        }
        catch (Exception ex)
        {
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        }

        return response;
    }

    public byte[] VisaoBoletosExportToPdf(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data)
    {

        try
        {
            using MemoryStream memoryStream = new();
            // Cria um escritor para o MemoryStream
            using (PdfWriter writer = new(memoryStream))
            {
                // Cria o documento PDF
                using PdfDocument pdf = new(writer);
                // Cria um documento
                using Document document = new(pdf);

                Paragraph title = new("Visão Analítica de Boletos");
                title.SetFontSize(16);
                document.Add(title);

                document.SetFontSize(8);
                // PdfFont boldFont = PdfFontFactory.CreateFont("HELVETICA_BOLD");

                Table table = new(11, true);
                // Adiciona cabeçalhos
                table.AddCell("Nr. do Boleto");
                table.AddCell("CPF/CNPJ");
                table.AddCell("Nome");
                table.AddCell("Nr. Contrato");
                table.AddCell("Valor");
                table.AddCell("Dt. Vencimento");
                table.AddCell("Honorário");
                table.AddCell("Status");
                table.AddCell("Cliente");
                table.AddCell("Grupo");
                table.AddCell("Operador");

                foreach (var item in data)
                {
                    table.AddCell(item.NrBoleto);
                    table.AddCell(item.CpfCnpj);
                    table.AddCell(item.NomeFinanciado);
                    table.AddCell(item.NrContrato);
                    table.AddCell(item.ValorBoleto.ToString());
                    table.AddCell(item.DataVencimento!.Value.ToString("dd/MM/yyyy"));
                    table.AddCell(item.TotalHonorarios.ToString());
                    table.AddCell(BoletosUsuariosDatacob.StatusBoleto(item.StatusBoleto));
                    table.AddCell(item.NomeCliente);
                    table.AddCell(item.NomeGrupo);
                    table.AddCell(item.NomeOperador);
                }
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");
                table.AddCell("");

                // Adiciona a tabela ao documento
                document.Add(table);
            }

            // Retorna o PDF como um array de bytes
            return memoryStream.ToArray();

        }
        catch
        {
            return [];

        }
    }

    public byte[] VisaoBoletosExportToCsv(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data)
    {

        try
        {
            using var memoryStream = new MemoryStream();
            using var writer = new StreamWriter(memoryStream);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            csv.WriteRecords(data.Select(BoletosUsuariosDatacob.ToCsv));
            writer.Flush();
            return memoryStream.ToArray();
        }
        catch
        {
            return [];
        }
    }

    public async Task<byte[]> VisaoBoletosExportToExcel(List<BoletosUsuariosDatacob.BoletosUsuariosDatacobCliente> data)
    {
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var stream = new MemoryStream();

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets.Add("Pagina 1");
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            worksheet.Cells[1, 1].Value = "Nr. do Boleto";
            worksheet.Cells[1, 2].Value = "CPF/CNPJ";
            worksheet.Cells[1, 3].Value = "Nome";
            worksheet.Cells[1, 4].Value = "Nr. Contrato";
            worksheet.Cells[1, 5].Value = "Valor";
            worksheet.Cells[1, 6].Value = "Dt. Vencimento";
            worksheet.Cells[1, 7].Value = "Honorário";
            worksheet.Cells[1, 8].Value = "Status";
            worksheet.Cells[1, 9].Value = "Cliente";
            worksheet.Cells[1, 10].Value = "Grupo";
            worksheet.Cells[1, 11].Value = "Operador";


            // Carregando os dados a partir da linha 2, logo abaixo dos cabeçalhos
            int line = 2;
            foreach (var item in data)
            {
                worksheet.Cells[line, 1].Value = item.NrBoleto;
                worksheet.Cells[line, 2].Value = item.CpfCnpj;
                worksheet.Cells[line, 3].Value = item.NomeFinanciado;
                worksheet.Cells[line, 4].Value = item.NrContrato;
                worksheet.Cells[line, 5].Value = item.ValorBoleto.ToString();
                worksheet.Cells[line, 6].Value = item.DataVencimento!.Value.ToString("dd/MM/yyyy");
                worksheet.Cells[line, 7].Value = item.TotalHonorarios.ToString();
                worksheet.Cells[line, 8].Value = BoletosUsuariosDatacob.StatusBoleto(item.StatusBoleto);
                worksheet.Cells[line, 9].Value = item.NomeCliente;
                worksheet.Cells[line, 10].Value = item.NomeGrupo;
                worksheet.Cells[line, 11].Value = item.NomeOperador;
                line++;
            }

            //worksheet.Cells.LoadFromCollection(data, true);  // Carrega os dados
            return package.GetAsByteArray();

        }
        catch
        {
            return [];
        }
    }

    public async Task<ServiceResponse<List<ControleUsuarioDatacob>>> GetControleUsuario(DateTime dtInicio, DateTime dtFim, GvcRodobens crm, int? idGrupo = null, string? status = null, DateTime? dtVencimento = null, string? nr_Boleto = null)
    {

        ServiceResponse<List<ControleUsuarioDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            string sql = ControleUsuariosGerarSql(false, idGrupo, status, dtVencimento, nr_Boleto);
            List<ControleUsuarioDatacob> result = [.. await conn.QueryAsync<ControleUsuarioDatacob>(sql, new { dtInicio, dtFim = dtFim.AddDays(1), idGrupo, status, dtVencimento, nr_Boleto })];

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            response.Data = await ControleUsuariosOrganizarBoletos(result);
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<ControleUsuarioDatacob>>> GetControleUsuario(int idUsuario, DateTime dtInicio, DateTime dtFim, GvcRodobens crm, int? idGrupo = null, string? status = null, DateTime? dtVencimento = null, string? nr_Boleto = null)
    {

        ServiceResponse<List<ControleUsuarioDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            string sql = ControleUsuariosGerarSql(true, idGrupo, status, dtVencimento, nr_Boleto);
            List<ControleUsuarioDatacob> result = [.. await conn.QueryAsync<ControleUsuarioDatacob>(sql, new { idUsuario, dtInicio, dtFim = dtFim.AddDays(1), idGrupo, status, dtVencimento })];

            if (result is null || result.Count == 0)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
                return response;
            }

            response.Data = await ControleUsuariosOrganizarBoletos(result);
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    private string ControleUsuariosGerarSql(bool idUsuario = false, int? idGrupo = null, string? status = null, DateTime? dtVencimento = null, string? nr_Boleto = null)
    {
        string sql = _sql.SqlControleUsuario();
        sql += @"where b.Dt_Emiss >= @dtInicio and b.Dt_Emiss <= @dtFim";
        if (idUsuario)
            sql += @" and u.Id_Usuario = @idUsuario";
        if (idGrupo is not null)
            sql += @" and g.Id_Grupo = @idGrupo";
        if (status is not null)
            sql += @" and b.Status = @status";
        if (dtVencimento is not null)
            sql += @" and b.Dt_Venc = @dtVencimento";
        if (nr_Boleto is not null)
            sql += @" and b.Nr_Boleto = @nr_Boleto";
        sql += _sql.SqlControleUsuarioGroupBy();
        return sql;
    }

    private async Task<List<ControleUsuarioDatacob>> ControleUsuariosOrganizarBoletos(List<ControleUsuarioDatacob> result)
    {
        List<ControleUsuarioDatacob> agrupados = [];
        foreach (var item in result)
        {
            var agrupado = agrupados.Where(x => x.IdBoleto == item.IdBoleto).FirstOrDefault();
            if (agrupado == null)
            {
                item.NumeroParcelaEnvolvidas.Add(item.NumeroParcelaEnvolvida ?? 0);
                agrupados.Add(item);
            }
            else
            {
                agrupado.NumeroParcelaEnvolvidas.Add(item.NumeroParcelaEnvolvida ?? 0);
                agrupado.Honorario += item.Honorario ?? 0;
            }
        }

        IEnumerable<int> ticketIds = [.. agrupados.Select(x => x.IdBoleto ?? 0).Distinct()];
        var tickets = await _uct.ListByTickets(ticketIds, CancellationToken.None);
        foreach (var item in agrupados)
        {
            var ticket = tickets?.Where(x => x.BoletoId == item.IdBoleto && x.UserCrmId == item.IdUsuario && x.BoletoNumber == item.NumeroBoleto).FirstOrDefault();
            item.Observacao = ticket?.Observation;
            item.BP = ticket?.BP;
            item.NumeroParcelaEnvolvidas = [.. item.NumeroParcelaEnvolvidas.OrderBy(x => x)];
        }
        return agrupados;
    }

    public async Task<byte[]> ControleUsuarioExcel(List<ControleUsuarioDatacob> data)
    {
        try
        {
            IDbConnection conn = CreateDatacobConnection(GvcRodobens.GVC);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var stream = new MemoryStream();

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets.Add("Pagina 1");
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;


            foreach (var item in data)
            {
                var model = await _uct.GetByParams(
                    item.IdUsuario ?? 0,
                    item.IdBoleto ?? 0,
                    item.NumeroBoleto ?? "",
                    CancellationToken.None);
                if (model is not null)
                {
                    item.BP = model.BP ?? item.BP;
                    item.Observacao = model.Observation ?? item.Observacao;
                    item.CrmUsuarioBoletoOwner = model.CrmUserBoletoOwner;
                    item.IdUsuarioBoletoOwner = model.IdUserBoletoOwner;
                    if (item.IdUsuarioBoletoOwner is not null || item.IdUsuarioBoletoOwner == 0)
                    {
                        string crmBoletoOwner = model.CrmUserBoletoOwner;

                        if (Enum.TryParse<GvcRodobens>(crmBoletoOwner, out var crmBoletoOwnerEnum))
                        {
                            item.NomeUsuarioBoletoOwner = (await GetUserById((int)item.IdUsuarioBoletoOwner, crmBoletoOwnerEnum))?.Nome ?? "";

                        }

                    }
                    item.IdNegociacao = model.IdNegotiation;
                    item.UsuarioAtualizacao = model.UserUpdated;
                }


            }

            worksheet.Cells[1, 1].Value = "Valor Total Pago";
            worksheet.Cells[1, 2].Value = "Valor Total Honorário";
            decimal totalPago = 0;
            decimal totalHonorario = 0;
            foreach (var item in data.Where(x => x.StatusBoleto?.ToUpper() != "CANCELADO").ToList())
            {
                totalPago += item.ValorCaixa ?? 0;
                totalHonorario += item.Honorario ?? 0;
            }
            worksheet.Cells[2, 1].Value = totalPago.ToString("C2");
            worksheet.Cells[2, 2].Value = totalHonorario.ToString("C2");

            worksheet.Cells[4, 1].Value = "Nome";
            worksheet.Cells[4, 2].Value = "Nr. Contrato";
            worksheet.Cells[4, 3].Value = "EMP";
            worksheet.Cells[4, 4].Value = "Nr. Boleto";
            worksheet.Cells[4, 5].Value = "Nr. Parcela";
            worksheet.Cells[4, 6].Value = "Tipo Parcela";
            worksheet.Cells[4, 7].Value = "Valor do Caixa";
            worksheet.Cells[4, 8].Value = "Honorário";
            worksheet.Cells[4, 9].Value = "Vencimento";
            worksheet.Cells[4, 10].Value = "Emissão";
            worksheet.Cells[4, 11].Value = "Status";
            worksheet.Cells[4, 12].Value = "BP";
            worksheet.Cells[4, 13].Value = "Observação";
            worksheet.Cells[4, 14].Value = "Carteira";
            worksheet.Cells[4, 15].Value = "Empreendimento";
            worksheet.Cells[4, 16].Value = "Usuário";
            worksheet.Cells[4, 17].Value = "Id Negociacao";

            var logged = await _config.GetLoggedUser();
            var show_dono_boleto_config = "";
            var showDonoBoletoInfo = false;
            if (logged is not null)
            {
                show_dono_boleto_config = await _config.GetValueByKey("users_show_input_dono_boleto_controle_usuarios");

                string[] show_dono_boleto_config_list = JsonConvert.DeserializeObject<string[]>(show_dono_boleto_config);
                if (show_dono_boleto_config_list is not null && show_dono_boleto_config_list.Contains(logged.Username))
                {
                    showDonoBoletoInfo = true;
                }
            }



            if (showDonoBoletoInfo)
            {
                worksheet.Cells[4, 18].Value = "Id Dono Boleto";
                worksheet.Cells[4, 19].Value = "Usuário Dono Boleto";
                worksheet.Cells[4, 20].Value = "Crm Dono Boleto";
                worksheet.Cells[4, 21].Value = "Usuário Atualização";
            }



            // Carregando os dados a partir da linha 2, logo abaixo dos cabeçalhos
            int line = 5;
            foreach (var item in data)
            {
                worksheet.Cells[line, 1].Value = item.NomeCliente;
                worksheet.Cells[line, 2].Value = item.NumeroContrato;
                worksheet.Cells[line, 3].Value = item.EMP;
                worksheet.Cells[line, 4].Value = item.NumeroBoleto;
                worksheet.Cells[line, 5].Value = string.Join(", ", item.NumeroParcelaEnvolvidas);
                worksheet.Cells[line, 6].Value = item.DescricaoTipoParcela;
                worksheet.Cells[line, 7].Value = item?.ValorCaixa?.ToString("C2");
                worksheet.Cells[line, 8].Value = item?.Honorario?.ToString("C2");
                worksheet.Cells[line, 9].Value = item!.Vencimento!.Value.ToString("dd/MM/yyyy");
                worksheet.Cells[line, 10].Value = item.DataEmissao!.Value.ToString("dd/MM/yyyy");
                worksheet.Cells[line, 11].Value = item.StatusBoleto;
                worksheet.Cells[line, 12].Value = item.BP;
                worksheet.Cells[line, 13].Value = item.Observacao;
                worksheet.Cells[line, 14].Value = item.DescricaoCarteira;
                worksheet.Cells[line, 15].Value = item.CarteiraEmpreendimento;
                worksheet.Cells[line, 16].Value = item.NomeUsuario;
                worksheet.Cells[line, 17].Value = item.IdNegociacao;

                if (showDonoBoletoInfo)
                {
                    worksheet.Cells[line, 18].Value = item.IdUsuarioBoletoOwner;
                    worksheet.Cells[line, 19].Value = item.NomeUsuarioBoletoOwner;
                    worksheet.Cells[line, 20].Value = item.CrmUsuarioBoletoOwner;
                    worksheet.Cells[line, 21].Value = item.UsuarioAtualizacao;
                }

                line++;
            }

            //worksheet.Cells.LoadFromCollection(data, true);  // Carrega os dados
            return package.GetAsByteArray();

        }
        catch
        {
            return [];
        }
    }

    public async Task<ServiceResponse<List<ContratoPadraoDatacob>>> GetListContratoPadrao(GvcRodobens crm, int IdContrato)
    {
        ServiceResponse<List<ContratoPadraoDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            response.Data = (await conn.QueryAsync<ContratoPadraoDatacob>(_sql.SqlListContratoPadrao(IdContrato))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosMailing(GvcRodobens crm, string groupId, string? fase, int? dayDueStart, int? dayDueEnd, float? debtValueStart, float? debtValueEnd)
    {
        ServiceResponse<List<DadosCadastraisDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            response.Data = (await conn.QueryAsync<DadosCadastraisDatacob>(_sql.SqlListDadosMailing(crm, groupId, fase, dayDueStart, dayDueEnd, debtValueStart, debtValueEnd))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }
    public async Task<ServiceResponse<List<DadosCadastraisDatacob>>> BuscaDadosMailingFromFile(IFormFile file, GvcRodobens crm, string groupId, string? fase, int? dayDueStart, int? dayDueEnd, float? debtValueStart, float? debtValueEnd)
    {
        ServiceResponse<List<DadosCadastraisDatacob>> response = new();
        try
        {
            IDbConnection conn = CreateDatacobConnection(crm);

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets[0];
            var rows = worksheet.Dimension.Rows;
            var columns = worksheet.Dimension.Columns;

            int? colCpfCnpj = null;
            for (int col = 1; col <= columns; col++)
                if (worksheet.Cells[1, col].Text.ToLower() == "cpf_cnpj")
                    colCpfCnpj = col;

            if (colCpfCnpj is null)
                throw new Exception("Coluna de cpf_cnpj não encontrada.");

            List<string> list_cpf_cnpj = [];

            for (int row = 2; row <= rows; row++)
                list_cpf_cnpj.Add(worksheet.Cells[row, colCpfCnpj ?? 0].Text);


            var list_financiado_id = new List<int>();

            foreach (var item in list_cpf_cnpj)
            {
                List<FinanciadoIdDatacob>? finan = null;

                if (item is not null)
                {
                    finan = (await conn.QueryAsync<FinanciadoIdDatacob>(
                        _sql.SqlBuscaFinanciadoDoc(item.Trim()))
                    ).ToList();

                    var lista = finan.Select(p => (int)p.Id_Financiado).ToList();

                    list_financiado_id.AddRange(lista); // 👈 adiciona os IDs à lista principal


                }
            }
            response.Data = (await conn.QueryAsync<DadosCadastraisDatacob>(_sql.SqlListDadosMailing(crm, groupId, fase, dayDueStart, dayDueEnd, debtValueStart, debtValueEnd, list_financiado_id))).ToList();
            if (response.Data is null || response.Data.Count == 0) throw new("Dados não encontrados.");


        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }

    public async Task<IDbConnection?> GetLinkedConnection(
        ConfigIntegrationType type,
        GvcRodobens currentCrm,
        int currentGroupId,
        int integratedGroupId,
        ConfigIntegrationActionType actionType = ConfigIntegrationActionType.Consult
    )
    {
        if (!IsValidGroupIds(currentGroupId, integratedGroupId))
            return null;

        try
        {
            var (currentAccess, integratedAccess) = await GetAccessInfo(currentCrm);
            
            bool hasValidConfig = await ExistsIntegrationConfiguration(
                type, 
                currentAccess, 
                currentGroupId,
                integratedGroupId, 
                integratedAccess?.Id ?? 0,
                actionType
            ) ?? false;

            if (hasValidConfig)
                return CreateDatacobConnection(integratedAccess?.DatacobNumber ?? currentCrm);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine(ex.Message);
        }

        return null;
    }

    public async Task<GvcRodobens?> GetLinkedCrm(
        ConfigIntegrationType type,
        GvcRodobens currentCrm,
        int currentGroupId,
        int integratedGroupId,
        ConfigIntegrationActionType actionType = ConfigIntegrationActionType.Insert
    )
    {
        if (!IsValidGroupIds(currentGroupId, integratedGroupId))
            return null;

        try
        {
            var (currentAccess, integratedAccess) = await GetAccessInfo(currentCrm);
            
            bool hasValidConfig = await ExistsIntegrationConfiguration(
                type,
                currentAccess,
                currentGroupId,
                integratedGroupId,
                integratedAccess?.Id ?? 0,
                actionType
            ) ?? false;

            if (hasValidConfig)
                return integratedAccess?.DatacobNumber;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine(ex.Message);
        }

        return null;
    }

    /* Dashboard */
    public async Task<ServiceResponse<List<DataDashboadContract>>> GetDashboadContracts(GvcRodobens? crm, int? groupId, DateTime? startDate, DateTime? endDate)
    {
        List<GvcRodobens> gvcRodobens = [GvcRodobens.Rodobens, GvcRodobens.GVC];
        ServiceResponse<List<DataDashboadContract>> response = new() { Data = [] };
        try
        {
            gvcRodobens = gvcRodobens.Where(x => x == crm || crm is null).ToList();
            foreach (var item in gvcRodobens)
            {
                IDbConnection conn = CreateDatacobConnection(item);
                response.Data.AddRange((await conn.QueryAsync<DataDashboadContract>(_sql.SqlDashboardListContrato(item, groupId, startDate, endDate))).ToList());
            }
        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Message = ex.Message;
        }

        return response;
    }


    private static bool IsValidGroupIds(int currentGroupId, int integratedGroupId)
        => currentGroupId != 0 && integratedGroupId != 0;

    private async Task<(int currentAccess, DatacobAccess? integratedAccess)> GetAccessInfo(GvcRodobens currentCrm)
    {
        var access = await _context.DatacobAccess.ToListAsync();
        int currentAccess = access.FirstOrDefault(x => x.DatacobNumber == currentCrm)?.Id ?? 0;
        var integratedAccess = access.FirstOrDefault(x => x.DatacobNumber != currentCrm);
        
        return (currentAccess, integratedAccess);
    }

    private async Task<bool?> ExistsIntegrationConfiguration(
        ConfigIntegrationType type,
        int currentCrm,
        int currentGroupId,
        int integratedGroupId,
        int integratedCrm,
        ConfigIntegrationActionType? actionType = null
    )
    {
        try
        {
            var baseQuery = _context.IntegrationConfigurations.Where(a =>
                a.CrmId == integratedCrm &&
                a.Started.Date <= DateTime.Now.Date &&
                a.GroupId == integratedGroupId &&
                a.LinkedGroupId == currentGroupId &&
                a.LinkedCrmId == currentCrm &&
                a.Active);

            if (actionType.HasValue)
            {
                baseQuery = baseQuery.Where(a =>
                    (actionType == ConfigIntegrationActionType.Consult && a.AllowConsult) ||
                    (actionType == ConfigIntegrationActionType.Insert && a.AllowInsert));
            }

            var query = type switch
            {
                ConfigIntegrationType.Occurrence => baseQuery.Where(a => a.Occurrence),
                ConfigIntegrationType.Agreement => baseQuery.Where(a => a.Agreement),
                ConfigIntegrationType.BankSlip => baseQuery.Where(a => a.BankSlip),
                _ => baseQuery.Where(_ => false)
            };

            return await query.AnyAsync();
        }
        catch (Exception)
        {
            return false;
        }
    }
    
    public async Task<ServiceResponse<InfoContratoSimplesDatacob>> SimpleInfoByContratctId(GvcRodobens crm, int contratoId)
    {
        ServiceResponse<InfoContratoSimplesDatacob> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            IDbConnection conn = CreateDatacobConnection(crm);
            response.Data = (await conn.QueryAsync<InfoContratoSimplesDatacob>(_sql.SqlSimpleInfoByContractId(), new { IdContrato = contratoId })).FirstOrDefault();

            if (response.Data is null)
            {
                response.Message = "Dados não encontrados.";
                response.Success = false;
            }
        }, (ex) =>
        { 
            response.Data = null;
            response.Success = false;
            response.Message = ex.Message + ex.StackTrace;
        });

        return response;
    }

}
