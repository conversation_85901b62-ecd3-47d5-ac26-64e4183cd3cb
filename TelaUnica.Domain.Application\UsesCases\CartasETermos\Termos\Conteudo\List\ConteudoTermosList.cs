using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Common;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.List;

public class ConteudoTermosList(
    IConteudoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IConteudoTermosList
{
    public IConteudoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<List<ConteudoTermosResponse>>> Handle(CancellationToken cancellationToken)
    {
        ServiceResponse<List<ConteudoTermosResponse>> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            response.Data = [.. (await _repo.GetList(cancellationToken)).Select(x => ConteudoTermosResponse.FromModel(x))];
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
