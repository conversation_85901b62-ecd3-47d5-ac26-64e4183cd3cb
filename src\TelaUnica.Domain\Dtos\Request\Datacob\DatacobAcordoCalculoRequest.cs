using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Dtos.Request.Datacob;

public class DatacobAcordoCalculoRequest
{
    public required int IdContrato { get; set; }
    public double ValorEntrada { get; set; }
    public DateTime? DataNegociacao { get; set; }
    public List<int>? Parcelas { get; set; } = [];
    public double? ValorParcela { get; set; }
    public int? ModalidadeNegociacao { get; set; }
    public GvcRodobens? Crm { get; set; }

}