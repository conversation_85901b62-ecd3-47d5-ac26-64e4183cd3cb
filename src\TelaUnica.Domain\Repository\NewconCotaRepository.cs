﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using TelaUnica.Domain.Dtos.Request.NewconApi;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Dtos.Response.NewconApi;
using TelaUnica.Domain.Interfaces;
using TelaUnica.Infra.Data.EF.Data;
using TelaUnica.Infra.Data.EF.Models;

namespace TelaUnica.Domain.Repository;

public class NewconCotaRepository : INewconCotaRepository
{
    private readonly IMapper _mapper;
    public readonly IHttpContextAccessor _httpContextAccessor;
    private readonly DataContext _context;
    public NewconCotaRepository(IMapper mapper, IHttpContextAccessor httpContextAccessor, DataContext context)
    {
        _httpContextAccessor = httpContextAccessor;
        _context = context;
        _mapper = mapper;
    }

    public async Task<ServiceResponse<NewconCotaResponse>> Create(NewconCotaRequest request)
    {
        var response = new ServiceResponse<NewconCotaResponse>();
        try
        {
            var newconCota = await _context.NewconCota
                .FirstOrDefaultAsync(r => request.Id == r.Id);

            if (newconCota == null)
            {
                var model = _mapper.Map<NewconCota>(request);

                model.UpdatedAt = DateTime.Now;
                model.CreatedAt = DateTime.Now;
                model.DataSicronizacao = DateTime.Now;
                model.Active = true;

                /* Dados Cliente */
                model.NewconClienteId = await getNewconClienteIdAsync(request.NewconCliente) ?? request.NewconClienteId;
                /* Dados empresa */
                model.NewconEmpresaId = await GetNewconEmpresaAsync(request) ?? 1;
                /* Dados Unidade */
                model.NewconUnidadeId = await GetNewconUnidadeAsync(request) ?? 1;
                /* Dados Assembleia */
                model.NewconAssembleiaIdAtual = await getNewconAssembleiaIdAsync(request.NewconAssembleia) ?? model.NewconAssembleiaIdPrimeira;
                /* Dados Assembleia Primeira */
                model.NewconAssembleiaIdPrimeira = await getNewconAssembleiaIdAsync(request.NewconAssembleia) ?? model.NewconAssembleiaIdPrimeira;
                /* Dados Plano Venda */
                model.NewconPlanoVendaId = await GetNewconPlanoVendaAsync(request) ?? 1;
                /* Dados Filial Adm */
                model.NewconFilialAdmId = await getNewconNewconFilialAsync(request.NewconFilialAdm) ?? model.NewconFilialAdmId;
                /* Dados Ponto Venda */
                model.NewconPontoVendaId = await GetNewconPontoVendaAsync(request) ?? 1;
                                
                /* Dados Filial Venda */
                if (request.NewconFilialVenda != null)
                    model.NewconFilialVendaId = await getNewconNewconFilialAsync(request.NewconFilialVenda) ?? model.NewconFilialVendaId;

                /* Dados Bem Objeto */
                if (request.NewconBemObjeto != null)
                {
                    model.NewconBemObjetoId = await getNewconBemObjetoIdAsync(request.NewconBemObjeto);
                }

                /* Dados Ponto Entrega */
                if (request.NewconPontoEntrega != null)
                {
                    model.NewconPontoEntregaId = await GetNewconPontoEntregaAsync(request) ?? 1;
                }

                /* Dados Contemplacao */
                if (request.NewconContemplacao != null)
                {
                    model.NewconContemplacaoId = await getNewconContemplacaoIdAsync(request.NewconContemplacao);
                }

                /* Dados Desclassificacao*/
                if (request.NewconDesclassificacao != null)
                {
                    model.NewconDesclassificacaoId = await GetNewconDesclassificacaoAsync(request);
                }

                /* Dados Assembleia Atual */
                if (request.NewconAssembleiaAtual != null)
                {
                    model.NewconAssembleiaIdAtual = await getNewconAssembleiaIdAsync(request.NewconAssembleiaAtual);
                }
                /* Dados Dif Grupo */
                if (request.NewconDifGrupo != null)
                {
                    model.NewconDifGrupoId = await GetNewconDifGrupoAsync(request);
                }

                _context.NewconCota.Add(model);
                await _context.SaveChangesAsync();

                var dto = _mapper.Map<NewconCotaResponse>(model);

                response.Message = "Gerado com sucesso.";
                response.Data = dto;
            }
            else
            {
                response = await UpdateCustom(request);
            }
        }
        catch (Exception ex)
        {
            response.Message = ex.Message;
            response.Success = false;
        }

        return response;
    }

    public async Task<ServiceResponse<NewconCotaResponse>> Delete(string id)
    {
        var response = new ServiceResponse<NewconCotaResponse>();
        try
        {
            var model = await _context.NewconCota.FirstOrDefaultAsync(r => id == r.Id) ?? throw new("Registro não encontrado.");

            model.UpdatedAt = DateTime.Now;
            model.DeletedAt = DateTime.Now;
            model.Active = false;

            _context.NewconCota.Update(model);
            await _context.SaveChangesAsync();

            response.Message = "Registro excluído com sucesso.";
            response.Data = _mapper.Map<NewconCotaResponse>(model);
        }
        catch (Exception ex)
        {
            response.Message = ex.Message;
            response.Success = false;
        }

        return response;
    }

    public async Task<ServiceResponse<List<NewconCotaResponse>>> Get(string? id)
    {
        var response = new ServiceResponse<List<NewconCotaResponse>>();
        try
        {
            var model = _context.NewconCota
                .Include(a => a.NewconEmpresa)
                .Include(a => a.NewconUnidade)
                .Include(a => a.NewconAssembleia)
                .Include(a => a.NewconAssembleiaAtual)
                .Include(a => a.NewconPlanoVenda)
                .Include(a => a.NewconBemObjeto).ThenInclude(b => b.NewconBemObjetoPagamentos)
                .Include(a => a.NewconFilialVenda)
                .Include(a => a.NewconFilialAdm)
                .Include(a => a.NewconPontoVenda)
                .Include(a => a.NewconPontoEntrega)
                .Include(a => a.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconBemObjeto)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconDesclassificacaoFases)
                .Include(a => a.NewconDifGrupo)
                .Include(a => a.NewconCliente)
                .AsQueryable();

            if (!string.IsNullOrEmpty(id)) model = model.Where(a => a.Id == id);

            var dto = await model.Select(c => _mapper.Map<NewconCotaResponse>(c)).ToListAsync();

            response.Data = dto;
        }
        catch (Exception ex)
        {
            response.Message = ex.Message;
            response.Success = false;
        }

        return response;
    }

    public async Task<NewconCotaResponse?> GetById(string id)
    {
        NewconCotaResponse? response = null;
        try
        {
            response = _mapper.Map<NewconCotaResponse>(await _context.NewconCota
                .Include(a => a.NewconEmpresa)
                .Include(a => a.NewconUnidade)
                .Include(a => a.NewconAssembleia)
                .Include(a => a.NewconAssembleiaAtual)
                .Include(a => a.NewconPlanoVenda)
                .Include(a => a.NewconBemObjeto).ThenInclude(b=> b.NewconBemObjetoPagamentos)
                .Include(a => a.NewconFilialVenda)
                .Include(a => a.NewconFilialAdm)
                .Include(a => a.NewconPontoVenda)
                .Include(a => a.NewconPontoEntrega)
                .Include(a => a.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconBemObjeto)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconDesclassificacaoFases)
                .Include(a => a.NewconDifGrupo)
                .Include(a => a.NewconCliente)
                .FirstOrDefaultAsync(x => x.Id == id));
        }
        catch
        {
            response = null;
        }

        return response;
    }

    public async Task<NewconCotaResponse?> GetNewconBemObjetoId(int newconBemObjetoId)
    {
        NewconCotaResponse? response = null;
        try
        {
            response = _mapper.Map<NewconCotaResponse>(await _context.NewconCota
                .Include(a => a.NewconEmpresa)
                .Include(a => a.NewconUnidade)
                .Include(a => a.NewconAssembleia)
                .Include(a => a.NewconAssembleiaAtual)
                .Include(a => a.NewconPlanoVenda)
                .Include(a => a.NewconBemObjeto).ThenInclude(b => b.NewconBemObjetoPagamentos)
                .Include(a => a.NewconFilialVenda)
                .Include(a => a.NewconFilialAdm)
                .Include(a => a.NewconPontoVenda)
                .Include(a => a.NewconPontoEntrega)
                .Include(a => a.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconBemObjeto)
                .Include(a => a.NewconDesclassificacao).ThenInclude(b => b.NewconDesclassificacaoFases)
                .Include(a => a.NewconDifGrupo)
                .Include(a => a.NewconCliente)
                .FirstOrDefaultAsync(x => x.NewconBemObjetoId == newconBemObjetoId));
        }
        catch
        {
            response = null;
        }

        return response;
    }

    public async Task<ServiceResponse<NewconCotaResponse>> Update(NewconCotaRequest request)
    {
        var response = new ServiceResponse<NewconCotaResponse>();
        try
        {
            var model = await _context.NewconCota.FirstOrDefaultAsync(r => request.Id == r.Id);

            if (model is null)
                throw new("Registro não encontrado.");

            model.NewconEmpresaId = request.NewconEmpresaId == 0 ? model.NewconEmpresaId : request.NewconEmpresaId;
            model.NewconUnidadeId = request.NewconUnidadeId == 0 ? model.NewconUnidadeId : request.NewconUnidadeId;
            model.Contrato = request.Contrato ?? model.Contrato;
            model.TipoCota = request.TipoCota ?? model.TipoCota;
            model.DataNascimento = request.DataNascimento ?? model.DataNascimento;
            model.CnpjCpf = request.CnpjCpf ?? model.CnpjCpf;
            model.DataAdesao = request.DataAdesao ?? model.DataAdesao;
            model.DataCadastro = request.DataCadastro != DateTime.MinValue ? request.DataCadastro : model.DataCadastro;
            model.DataVenda = request.DataVenda != DateTime.MinValue ? request.DataVenda : model.DataVenda;
            model.NewconAssembleiaIdPrimeira = request.NewconAssembleiaIdPrimeira ?? model.NewconAssembleiaIdPrimeira;
            model.PrazoPlano = request.PrazoPlano == 0 ? model.PrazoPlano : request.PrazoPlano;
            model.TipoVenda = request.TipoVenda ?? model.TipoVenda;
            model.NewconPlanoVendaId = request.NewconPlanoVendaId == 0 ? model.NewconPlanoVendaId : request.NewconPlanoVendaId;
            model.NewconBemObjetoId = request.NewconBemObjetoId ?? model.NewconBemObjetoId;
            model.NewconFilialVendaId = request.NewconFilialVendaId == 0 ? model.NewconFilialVendaId : request.NewconFilialVendaId;
            model.NewconFilialAdmId = request.NewconFilialAdmId == 0 ? model.NewconFilialAdmId : request.NewconFilialAdmId;
            model.NewconPontoVendaId = request.NewconPontoVendaId ?? model.NewconPontoVendaId;
            model.NewconPontoEntregaId = request.NewconPontoEntregaId ?? model.NewconPontoEntregaId;
            model.ValorBem = request.ValorBem ?? model.ValorBem;
            model.TaxaAdminstracao = request.TaxaAdminstracao ?? model.TaxaAdminstracao;
            model.TaxaAdesao = request.TaxaAdesao ?? model.TaxaAdesao;
            model.FundoReserva = request.FundoReserva ?? model.FundoReserva;
            model.PercentualCont = request.PercentualCont ?? model.PercentualCont;
            model.NewconContemplacaoId = request.NewconContemplacaoId ?? model.NewconContemplacaoId;
            model.NewconDesclassificacaoId = request.NewconDesclassificacaoId ?? model.NewconDesclassificacaoId;
            model.NewconAssembleiaIdAtual = request.NewconAssembleiaIdAtual ?? model.NewconAssembleiaIdAtual;
            model.NewconDifGrupoId = request.NewconDifGrupoId ?? model.NewconDifGrupoId;
            model.ValoresPago = request.ValoresPago ?? model.ValoresPago;
            model.ValoresPagoPercentual = request.ValoresPagoPercentual ?? model.ValoresPagoPercentual;
            model.SaldoDevedor = request.SaldoDevedor ?? model.SaldoDevedor;
            model.SaldoDevedorPercentual = request.SaldoDevedorPercentual ?? model.SaldoDevedorPercentual;
            model.Atraso = request.Atraso ?? model.Atraso;
            model.ArasoPercentual = request.ArasoPercentual ?? model.ArasoPercentual;
            model.DiferencaParcela = request.DiferencaParcela ?? model.DiferencaParcela;
            model.DiferencaParcelaPercentual = request.DiferencaParcelaPercentual ?? model.DiferencaParcelaPercentual;
            model.ValorParcela = request.ValorParcela ?? model.ValorParcela;
            model.ValorParcelaPercentual = request.ValorParcelaPercentual ?? model.ValorParcelaPercentual;
            model.QuantidadeParcelaPaga = request.QuantidadeParcelaPaga ?? model.QuantidadeParcelaPaga;
            model.QuantidadeParcelaFuro = request.QuantidadeParcelaFuro ?? model.QuantidadeParcelaFuro;
            model.LanceMinimo = request.LanceMinimo ?? model.LanceMinimo;
            model.LanceMaximo = request.LanceMaximo ?? model.LanceMaximo;
            model.DataSicronizacao = request.DataSicronizacao != DateTime.MinValue ? request.DataSicronizacao : model.DataSicronizacao;
            model.UpdatedAt = DateTime.Now;

            _context.NewconCota.Update(model);
            await _context.SaveChangesAsync();

            var dto = _mapper.Map<NewconCotaResponse>(model);

            response.Message = "Registro atualizado com sucesso.";
            response.Data = dto;
        }
        catch (Exception ex)
        {
            response.Message = ex.Message;
            response.Success = false;
        }

        return response;
    }

    private async Task<ServiceResponse<NewconCotaResponse>> UpdateCustom(NewconCotaRequest request)
    {
        var response = new ServiceResponse<NewconCotaResponse>();
        try
        {
            var model = await _context.NewconCota
                .Include(a => a.NewconEmpresa)
                .Include(a => a.NewconUnidade)
                .Include(a => a.NewconAssembleia)
                .Include(a => a.NewconAssembleiaAtual)
                .Include(a => a.NewconPlanoVenda)
                .Include(a => a.NewconBemObjeto).ThenInclude(aa => aa.NewconBemObjetoRendimento).ThenInclude(aa => aa.NewconBemObjetoRendimentoMensal)
                .Include(a => a.NewconBemObjeto).ThenInclude(aa => aa.NewconBemObjetoFgts).ThenInclude(aa => aa.NewconBemObjetoFgtsMensal)
                .Include(a => a.NewconBemObjeto).ThenInclude(aa => aa.NewconBemObjetoPagamentos)
                .Include(a => a.NewconFilialVenda)
                .Include(a => a.NewconFilialAdm)
                .Include(a => a.NewconPontoVenda)
                .Include(a => a.NewconPontoEntrega)
                .Include(a => a.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconDesclassificacaoFases)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconContemplacao)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconCliente).ThenInclude(aaa => aaa.NewconClienteBancos)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconCliente).ThenInclude(aaa => aaa.NewconPessoa.NewconPessoaEnderecos)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconCliente).ThenInclude(aaa => aaa.NewconPessoa.NewconPessoaContatos)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconBemObjeto).ThenInclude(aa => aa.NewconBemObjetoPagamentos)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconBemObjeto).ThenInclude(aa => aa.NewconBemObjetoRendimento).ThenInclude(aa => aa.NewconBemObjetoRendimentoMensal)
                .Include(a => a.NewconDesclassificacao).ThenInclude(aa => aa.NewconBemObjeto).ThenInclude(aa => aa.NewconBemObjetoFgts).ThenInclude(aa => aa.NewconBemObjetoFgtsMensal)
                .Include(a => a.NewconDifGrupo)
                .Include(a => a.NewconCliente).ThenInclude(aaa => aaa.NewconClienteBancos)
                .Include(a => a.NewconCliente).ThenInclude(aaa => aaa.NewconPessoa.NewconPessoaEnderecos)
                .Include(a => a.NewconCliente).ThenInclude(aaa => aaa.NewconPessoa.NewconPessoaContatos)
                .Include(a => a.NewconCotaDadoCotas).ThenInclude(aa => aa.NewconCotaDadoCotaBanco)
                .Include(a => a.NewconCotaEnvolvidos).ThenInclude(aa => aa.NewconCotaEnvolvidoCota)
                .Include(a => a.NewconCotaEnvolvidos).ThenInclude(aa => aa.NewconCotaEnvolvidoVigencia)
                .Include(a => a.NewconCotaEnvolvidos).ThenInclude(aa => aa.NewconPessoa.NewconPessoaEnderecos)
                .Include(a => a.NewconCotaEnvolvidos).ThenInclude(aa => aa.NewconPessoa.NewconPessoaContatos)
                .Include(a => a.NewconCotaTitularidades).ThenInclude(aa => aa.NewconCotaTitularidadeVigencia)
                .Include(a => a.NewconCotaRepresentantes).ThenInclude(aa => aa.NewconPessoa.NewconPessoaEnderecos)
                .Include(a => a.NewconCotaRepresentantes).ThenInclude(aa => aa.NewconPessoa.NewconPessoaContatos)
                .Include(a => a.NewconCotaReportaveis).ThenInclude(aa => aa.NewconPessoa.NewconPessoaEnderecos)
                .Include(a => a.NewconCotaReportaveis).ThenInclude(aa => aa.NewconPessoa.NewconPessoaContatos)
                .Include(a => a.NewconCotaNegociacoes).ThenInclude(aa => aa.NewconAssembleia)
                .Include(a => a.NewconCotaNegociacoes).ThenInclude(aa => aa.Parcelas)
                .Include(a => a.NewconCotaValoresPagos)
                .Include(a => a.NewconCotaSaldoDevedor).ThenInclude(aa => aa.NewconCotaSaldoDevedorOutrosValores)
                .Include(a => a.NewconCotaAtrasos)
                .Include(a => a.NewconCotaSaldoDifParcela)
                .Include(a => a.NewconCotaValorParcela)
                .Include(a => a.NewconCotaAgendas)
                .FirstOrDefaultAsync(r => request.Id == r.Id);

            if (model is null)
                throw new("Registro não encontrado.");

            /* Dados Cliente */
            model.NewconClienteId = await getNewconClienteIdAsync(request.NewconCliente) ?? request.NewconClienteId;
            /* Dados empresa */
            model.NewconEmpresaId = await GetNewconEmpresaAsync(request) ?? 1;
            /* Dados Unidade */
            model.NewconUnidadeId = await GetNewconUnidadeAsync(request) ?? 1;
            /* Dados Assembleia */
            model.NewconAssembleiaIdPrimeira = await getNewconAssembleiaIdAsync(request.NewconAssembleia) ?? model.NewconAssembleiaIdPrimeira;
            /* Dados Plano Venda */
            model.NewconPlanoVendaId = await GetNewconPlanoVendaAsync(request) ?? 1;
            /* Dados Filial Venda */
            if (request.NewconFilialVenda != null)
                model.NewconFilialVendaId = await getNewconNewconFilialAsync(request.NewconFilialVenda) ?? model.NewconFilialVendaId;
            /* Dados Filial Adm */
            model.NewconFilialAdmId = await getNewconNewconFilialAsync(request.NewconFilialAdm) ?? model.NewconFilialAdmId;
            /* Dados Ponto Venda */
            model.NewconPontoVendaId = await GetNewconPontoVendaAsync(request) ?? 1;

            /* Dados Bem Objeto */
            if (request.NewconBemObjeto != null)
            {
                model.NewconBemObjetoId = await getNewconBemObjetoIdAsync(request.NewconBemObjeto);
            }

            /* Dados Ponto Entrega */
            if (request.NewconPontoEntrega != null)
            {
                model.NewconPontoEntregaId = await GetNewconPontoEntregaAsync(request) ?? 1;
            }

            /* Dados Contemplacao */
            if (request.NewconContemplacao != null)
            {
                model.NewconContemplacaoId = await getNewconContemplacaoIdAsync(request.NewconContemplacao);
            }

            /* Dados Desclassificacao*/
            if (request.NewconDesclassificacao != null)
            {
                model.NewconDesclassificacaoId = await GetNewconDesclassificacaoAsync(request);
            }

            /* Dados Assembleia Atual */
            if (request.NewconAssembleiaAtual != null)
            {
                model.NewconAssembleiaIdAtual = await getNewconAssembleiaIdAsync(request.NewconAssembleiaAtual);
            }
            /* Dados Dif Grupo */
            if (request.NewconDifGrupo != null)
            {
                model.NewconDifGrupoId = await GetNewconDifGrupoAsync(request);
            }

            /* Dados Cota */
            if (request.NewconCotaDadoCotas.Count > 0)
            {
                var newconCotaDadoCota = _context.NewconCotaDadoCota
                    .Include(a => a.NewconCotaDadoCotaBanco)
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaDadoCota.Count == 0)
                {
                    foreach (var dadoCota in request.NewconCotaDadoCotas)
                    {
                        var newconCotaDadoCotaTp = _mapper.Map<NewconCotaDadoCota>(dadoCota);
                        newconCotaDadoCotaTp.NewconCotaId = request.Id;
                        newconCotaDadoCotaTp.Active = true;
                        _context.NewconCotaDadoCota.Add(newconCotaDadoCotaTp);
                    }
                }
                else if (newconCotaDadoCota.Count > 0)
                {
                    newconCotaDadoCota.ForEach(item => item.Active = false);

                    foreach (var dadoCota in request.NewconCotaDadoCotas)
                    {
                        dadoCota.NewconCotaId = request.Id;
                        var findDadoCota = newconCotaDadoCota
                            .FirstOrDefault(a => a.NewconCotaId == dadoCota.NewconCotaId && a.Carteira == dadoCota.Carteira && a.Convenio == dadoCota.Convenio);

                        if (findDadoCota is null)
                        {
                            findDadoCota = _mapper.Map<NewconCotaDadoCota>(dadoCota);
                            findDadoCota.Active = true;
                            findDadoCota.NewconCotaId = request.Id;
                            _context.NewconCotaDadoCota.Add(findDadoCota);
                        }
                        else
                        {
                            findDadoCota.Active = true;
                            if (findDadoCota?.NewconCotaDadoCotaBanco?.Count > 0)
                            {
                                findDadoCota.NewconCotaDadoCotaBanco.ForEach(a => a.Active = false);

                                foreach (var dadoCotaBanco in dadoCota.NewconCotaDadoCotaBanco)
                                {
                                    var dadaBanco = findDadoCota!.NewconCotaDadoCotaBanco
                                        .FirstOrDefault(a => a.NewconCotaDadoCotaId == dadoCotaBanco.NewconCotaDadoCotaId && a.Banco == dadoCotaBanco.Banco && a.Agencia == dadoCotaBanco.Agencia && a.Conta == dadoCotaBanco.Conta);
                                    if (dadaBanco is null)
                                    {
                                        dadaBanco = _mapper.Map<NewconCotaDadoCotaBanco>(dadoCotaBanco);
                                        dadaBanco.NewconCotaDadoCotaId = findDadoCota.Id;
                                        dadaBanco.Active = true;
                                        _context.NewconCotaDadoCotaBanco.Add(dadaBanco);
                                    }
                                    else
                                    {
                                        dadaBanco.Active = true;
                                        dadaBanco.ChavePix = dadoCotaBanco.ChavePix;
                                        _context.NewconCotaDadoCotaBanco.Update(dadaBanco);
                                    }
                                }
                            }

                            if (findDadoCota!.NewconCotaDadoCotaBanco.Count == 0)
                            {
                                foreach (var dadoCotaBanco in dadoCota.NewconCotaDadoCotaBanco)
                                {
                                    var dadaBanco = _mapper.Map<NewconCotaDadoCotaBanco>(dadoCotaBanco);
                                    dadaBanco.NewconCotaDadoCotaId = findDadoCota.Id;
                                    dadaBanco.Active = true;
                                    _context.NewconCotaDadoCotaBanco.Add(dadaBanco);
                                }
                            }
                        }
                    }

                    newconCotaDadoCota
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaDadoCota.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            if (request.NewconCotaEnvolvidos.Count > 0)
            {
                var newconCotaEnvolvido = _context.NewconCotaEnvolvido
                    .Include(a => a.NewconPessoa)
                    .Where(a => a.NewconCotaId == request.Id && a.Active).ToList();

                if (newconCotaEnvolvido.Count == 0)
                {
                    foreach (var dadoEnv in request.NewconCotaEnvolvidos)
                    {
                        var newconCotaEnvolTp = _mapper.Map<NewconCotaEnvolvido>(dadoEnv);
                        newconCotaEnvolTp.Active = true;
                        newconCotaEnvolTp.NewconCotaId = request.Id;
                        _context.NewconCotaEnvolvido.Add(newconCotaEnvolTp);
                    }
                }

                if (newconCotaEnvolvido.Count > 0)
                {
                    newconCotaEnvolvido.ForEach(item => item.Active = false);
                    foreach (var envolvido in request.NewconCotaEnvolvidos)
                    {
                        envolvido.NewconCotaId = request.Id;
                        var findEnvol = newconCotaEnvolvido
                            .FirstOrDefault(a => a.NewconCotaId == envolvido.NewconCotaId && a.NewconPessoa.CnpjCpf == (envolvido?.NewconPessoa?.CnpjCpf ?? ""));

                        if (findEnvol is null)
                        {
                            findEnvol = _mapper.Map<NewconCotaEnvolvido>(envolvido);
                            findEnvol.Active = true;
                            findEnvol.NewconCotaId = request.Id;
                            _context.NewconCotaEnvolvido.Add(findEnvol);
                        }
                        else
                        {
                            findEnvol.Active = true;
                            findEnvol.TipoEnvolvimento = envolvido.TipoEnvolvimento ?? findEnvol.TipoEnvolvimento;
                            if (envolvido.NewconPessoa != null)
                                findEnvol.NewconPessoaId = await getNewconPessoaIdAsync(envolvido.NewconPessoa) ?? findEnvol.NewconPessoaId;

                            //Cota
                            if (findEnvol.NewconCotaEnvolvidoCota.Count > 0)
                            {
                                findEnvol.NewconCotaEnvolvidoCota.ForEach(a => a.Active = false);

                                foreach (var envCota in envolvido.NewconCotaEnvolvidoCota)
                                {
                                    var dadaCota = findEnvol!.NewconCotaEnvolvidoCota
                                        .FirstOrDefault(a => a.NewconCotaEnvolvidoId == envCota.NewconCotaEnvolvidoId && a.Cota == envCota.Cota && a.Grupo == envCota.Grupo);
                                    if (dadaCota is null)
                                    {
                                        dadaCota = _mapper.Map<NewconCotaEnvolvidoCota>(envCota);
                                        dadaCota.Active = true;
                                        dadaCota.NewconCotaEnvolvidoId = findEnvol.Id;
                                        _context.NewconCotaEnvolvidoCota.Add(dadaCota);
                                    }
                                    else
                                    {
                                        dadaCota.Active = true;
                                        dadaCota.Versao = envCota.Versao;
                                        _context.NewconCotaEnvolvidoCota.Update(dadaCota);
                                    }
                                }
                            }

                            if (findEnvol!.NewconCotaEnvolvidoCota.Count == 0)
                            {
                                foreach (var envCota in envolvido.NewconCotaEnvolvidoCota)
                                {
                                    var dadaCota = _mapper.Map<NewconCotaEnvolvidoCota>(envCota);
                                    envCota.NewconCotaEnvolvidoId = findEnvol.Id;
                                    envCota.Active = true;
                                    _context.NewconCotaEnvolvidoCota.Add(dadaCota);
                                }
                            }

                            //Vigencia
                            if (findEnvol.NewconCotaEnvolvidoVigencia.Count > 0)
                            {
                                findEnvol.NewconCotaEnvolvidoVigencia.ForEach(a => a.Active = false);

                                foreach (var envVigencia in envolvido.NewconCotaEnvolvidoVigencia)
                                {
                                    var dadaVigencia = findEnvol!.NewconCotaEnvolvidoVigencia
                                        .FirstOrDefault(a => a.NewconCotaEnvolvidoId == envVigencia.NewconCotaEnvolvidoId && a.InicioVigencia == envVigencia.InicioVigencia);
                                    if (dadaVigencia is null)
                                    {
                                        dadaVigencia = _mapper.Map<NewconCotaEnvolvidoVigencia>(envVigencia);
                                        dadaVigencia.Active = true;
                                        dadaVigencia.NewconCotaEnvolvidoId = findEnvol.Id;
                                        _context.NewconCotaEnvolvidoVigencia.Add(dadaVigencia);
                                    }
                                    else
                                    {
                                        dadaVigencia.Active = true;
                                        dadaVigencia.FinalVigencia = envVigencia.FinalVigencia;
                                        _context.NewconCotaEnvolvidoVigencia.Update(dadaVigencia);
                                    }
                                }
                            }

                            if (findEnvol!.NewconCotaEnvolvidoVigencia.Count == 0)
                            {
                                foreach (var envVigencia in envolvido.NewconCotaEnvolvidoVigencia)
                                {
                                    var dadaVigencia = _mapper.Map<NewconCotaEnvolvidoVigencia>(envVigencia);
                                    envVigencia.Active = true;
                                    envVigencia.NewconCotaEnvolvidoId = findEnvol.Id;
                                    _context.NewconCotaEnvolvidoVigencia.Add(dadaVigencia);
                                }
                            }
                        }
                    }

                    newconCotaEnvolvido
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaEnvolvido.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Titularidade */
            if (request.NewconCotaTitularidades.Count > 0)
            {
                var newconCotaTitularidade = _context.NewconCotaTitularidade
                    .Include(a => a.NewconCotaTitularidadeVigencia)
                    .Include(a => a.NewconPessoa)
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaTitularidade.Count == 0)
                {
                    foreach (var dadoTitula in request.NewconCotaTitularidades)
                    {
                        var newconCotaTitularidadeTp = _mapper.Map<NewconCotaTitularidade>(dadoTitula);
                        newconCotaTitularidadeTp.Active = true;
                        newconCotaTitularidadeTp.NewconCotaId = request.Id;
                        _context.NewconCotaTitularidade.Add(newconCotaTitularidadeTp);
                    }
                }
                else if (newconCotaTitularidade.Count > 0)
                {
                    newconCotaTitularidade.ForEach(item => item.Active = false);

                    foreach (var dadoTitula in request.NewconCotaTitularidades)
                    {
                        dadoTitula.NewconCotaId = request.Id;
                        var findDado = newconCotaTitularidade
                            .FirstOrDefault(a => a.NewconCotaId == dadoTitula.NewconCotaId && a.NewconPessoa.CnpjCpf == (dadoTitula?.NewconPessoa?.CnpjCpf ?? ""));

                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconCotaTitularidade>(dadoTitula);
                            findDado.Active = true;
                            if (dadoTitula.NewconPessoa != null)
                                findDado.NewconPessoaId = await getNewconPessoaIdAsync(dadoTitula.NewconPessoa) ?? findDado.NewconPessoaId;

                            _context.NewconCotaTitularidade.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                            if (findDado?.NewconCotaTitularidadeVigencia?.Count > 0)
                            {
                                findDado.NewconCotaTitularidadeVigencia.ForEach(a => a.Active = false);

                                foreach (var dadoCotaBanco in dadoTitula.NewconCotaTitularidadeVigencia)
                                {
                                    var dadaBanco = findDado!.NewconCotaTitularidadeVigencia
                                        .FirstOrDefault(a => a.InicioVigencia == dadoCotaBanco.InicioVigencia);
                                    if (dadaBanco is null)
                                    {
                                        dadaBanco = _mapper.Map<NewconCotaTitularidadeVigencia>(dadoCotaBanco);
                                        dadaBanco.Active = true;
                                        dadaBanco.NewconCotaTitularidadeId = findDado.Id;
                                        _context.NewconCotaTitularidadeVigencia.Add(dadaBanco);
                                    }
                                    else
                                    {
                                        dadaBanco.Active = true;
                                        dadaBanco.FinalVigencia = dadoCotaBanco.FinalVigencia;
                                        _context.NewconCotaTitularidadeVigencia.Update(dadaBanco);
                                    }
                                }
                            }

                            if (findDado!.NewconCotaTitularidadeVigencia.Count == 0)
                            {
                                foreach (var dadoCotaBanco in dadoTitula.NewconCotaTitularidadeVigencia)
                                {
                                    var dadaBanco = _mapper.Map<NewconCotaTitularidadeVigencia>(dadoCotaBanco);
                                    dadaBanco.NewconCotaTitularidadeId = findDado.Id;
                                    dadaBanco.Active = true;
                                    _context.NewconCotaTitularidadeVigencia.Add(dadaBanco);
                                }
                            }
                        }
                    }

                    newconCotaTitularidade
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaTitularidade.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Representante */
            if (request.NewconCotaRepresentantes.Count > 0)
            {
                var newconCotaRepresentante = _context.NewconCotaRepresentante
                    .Include(a => a.NewconPessoa)
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaRepresentante.Count == 0)
                {
                    foreach (var dado in request.NewconCotaRepresentantes)
                    {
                        var newconCotaRepresentanteTp = _mapper.Map<NewconCotaRepresentante>(dado);
                        newconCotaRepresentanteTp.Active = true;
                        newconCotaRepresentanteTp.NewconCotaId = request.Id;
                        _context.NewconCotaRepresentante.Add(newconCotaRepresentanteTp);
                    }
                }
                else if (newconCotaRepresentante.Count > 0)
                {
                    newconCotaRepresentante.ForEach(item => item.Active = false);

                    foreach (var dado in request.NewconCotaRepresentantes)
                    {
                        dado.NewconCotaId = request.Id;
                        var findDado = newconCotaRepresentante
                            .FirstOrDefault(a => a.NewconCotaId == dado.NewconCotaId && a.NewconPessoa.CnpjCpf == (dado?.NewconPessoa?.CnpjCpf ?? ""));

                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconCotaRepresentante>(dado);
                            findDado.Active = true;
                            if (dado.NewconPessoa != null)
                                findDado.NewconPessoaId = await getNewconPessoaIdAsync(dado.NewconPessoa) ?? findDado.NewconPessoaId;

                            _context.NewconCotaRepresentante.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                        }
                    }
                    newconCotaRepresentante
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaRepresentante.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Reportaveis */
            if (request.NewconCotaReportaveis.Count > 0)
            {
                var newconCotaReportavel = _context.NewconCotaReportavel
                    .Include(a => a.NewconPessoa)
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaReportavel.Count == 0)
                {
                    foreach (var dado in request.NewconCotaReportaveis)
                    {
                        var newconCotaReportavelTp = _mapper.Map<NewconCotaReportavel>(dado);
                        newconCotaReportavelTp.Active = true;
                        newconCotaReportavelTp.NewconCotaId = request.Id;
                        _context.NewconCotaReportavel.Add(newconCotaReportavelTp);
                    }
                }
                else if (newconCotaReportavel.Count > 0)
                {
                    newconCotaReportavel.ForEach(item => item.Active = false);

                    foreach (var dado in request.NewconCotaReportaveis)
                    {
                        dado.NewconCotaId = request.Id;
                        var findDado = newconCotaReportavel
                            .FirstOrDefault(a => a.NewconCotaId == dado.NewconCotaId && a.NewconPessoa.CnpjCpf == (dado?.NewconPessoa?.CnpjCpf ?? ""));

                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconCotaReportavel>(dado);
                            findDado.Active = true;
                            if (dado?.NewconPessoa != null) findDado.NewconPessoaId = await getNewconPessoaIdAsync(dado.NewconPessoa) ?? findDado.NewconPessoaId;
                            _context.NewconCotaReportavel.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                        }
                    }
                    newconCotaReportavel
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaReportavel.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Negociacao */
            if (request.NewconCotaNegociacoes.Count > 0)
            {
                var newconCotaNegociacao = _context.NewconCotaNegociacao
                    .Include(a => a.NewconAssembleia)
                    .Include(a => a.Parcelas)
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaNegociacao.Count == 0)
                {
                    foreach (var dado in request.NewconCotaNegociacoes)
                    {
                        var newconCotaNegociacaoTp = _mapper.Map<NewconCotaNegociacao>(dado);
                        newconCotaNegociacaoTp.Active = true;
                        newconCotaNegociacaoTp.NewconCotaId = request.Id;
                        _context.NewconCotaNegociacao.Add(newconCotaNegociacaoTp);
                    }
                }
                else if (newconCotaNegociacao.Count > 0)
                {
                    newconCotaNegociacao.ForEach(item => item.Active = false);

                    foreach (var dado in request.NewconCotaNegociacoes)
                    {
                        dado.NewconCotaId = request.Id;
                        var findDado = newconCotaNegociacao
                            .FirstOrDefault(a => a.NewconCotaId == dado.NewconCotaId && a.DataConfirmacao == dado.DataConfirmacao && a.TipoNegociacao == dado.TipoNegociacao);

                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconCotaNegociacao>(dado);
                            findDado.Active = true;
                            if (dado.NewconAssembleia != null) findDado.AssembleiaId = await getNewconAssembleiaIdAsync(dado.NewconAssembleia) ?? findDado.AssembleiaId;
                            _context.NewconCotaNegociacao.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                            findDado.DataAssembleia = dado.DataAssembleia != DateTime.MinValue ? dado.DataAssembleia : findDado.DataAssembleia;
                            findDado.DataNegociada = dado.DataNegociada != DateTime.MinValue ? dado.DataNegociada : findDado.DataNegociada;
                            findDado.RateioFundoComum = dado.RateioFundoComum != 0 ? dado.RateioFundoComum : findDado.RateioFundoComum;
                            findDado.RateioFundoReserva = dado.RateioFundoReserva != 0 ? dado.RateioFundoReserva : findDado.RateioFundoReserva;
                            findDado.RateioTaxaAdministracao = dado.RateioTaxaAdministracao != 0 ? dado.RateioTaxaAdministracao : findDado.RateioTaxaAdministracao;
                            findDado.RateioTaxaAdesao = dado.RateioTaxaAdesao != 0 ? dado.RateioTaxaAdesao : findDado.RateioTaxaAdesao;
                            findDado.RateioOutros = dado.RateioOutros != 0 ? dado.RateioOutros : findDado.RateioOutros;
                            findDado.Status = dado.Status;
                            findDado.Aviso = dado.Aviso ?? findDado.Aviso;

                            if (findDado?.Parcelas?.Count > 0)
                            {
                                findDado.Parcelas.ForEach(a => a.Active = false);

                                foreach (var dadoCotaBanco in dado.Parcelas)
                                {
                                    var dadaBanco = findDado!.Parcelas.FirstOrDefault(a => a.Numero == dadoCotaBanco.Numero);
                                    if (dadaBanco is null)
                                    {
                                        dadaBanco = _mapper.Map<NewconCotaNegociacaoParcela>(dadoCotaBanco);
                                        dadaBanco.Active = true;
                                        dadaBanco.NewconCotaNegociacaoId = findDado.Id;
                                        _context.NewconCotaNegociacaoParcela.Add(dadaBanco);
                                    }
                                    else
                                    {
                                        dadaBanco.Active = true;
                                        dadaBanco.PercFundoComum = dadoCotaBanco.PercFundoComum;
                                        dadaBanco.PercFundoReserva = dadoCotaBanco.PercFundoReserva;
                                        dadaBanco.PercTaxaAdministracao = dadoCotaBanco.PercTaxaAdministracao;
                                        dadaBanco.PercAdesao = dadoCotaBanco.PercAdesao;
                                        dadaBanco.PercOutros = dadoCotaBanco.PercOutros;
                                        dadaBanco.Usuario = dadoCotaBanco.Usuario;
                                        dadaBanco.Cancelada = dadoCotaBanco.Cancelada;
                                        _context.NewconCotaNegociacaoParcela.Update(dadaBanco);
                                    }
                                }
                            }

                            if (findDado!.Parcelas.Count == 0)
                            {
                                foreach (var dadoCotaBanco in dado.Parcelas)
                                {
                                    var dadaBanco = _mapper.Map<NewconCotaNegociacaoParcela>(dadoCotaBanco);
                                    dadaBanco.Active = true;
                                    dadaBanco.NewconCotaNegociacaoId = findDado.Id;
                                    _context.NewconCotaNegociacaoParcela.Add(dadaBanco);
                                }
                            }
                        }
                    }
                    newconCotaNegociacao
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaNegociacao.Update(item));
                }

                /* Dados Valores Pagos*/
                if (request.NewconCotaValoresPagos.Count > 0)
                {
                    var newconCotaValoresPago = _context.NewconCotaValoresPago
                        .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                    if (newconCotaValoresPago.Count == 0)
                    {
                        foreach (var dado in request.NewconCotaValoresPagos)
                        {
                            var newconCotaValoresPagoTp = _mapper.Map<NewconCotaValoresPago>(dado);
                            newconCotaValoresPagoTp.Active = true;
                            newconCotaValoresPagoTp.NewconCotaId = request.Id;
                            _context.NewconCotaValoresPago.Add(newconCotaValoresPagoTp);
                        }
                    }
                    else if (newconCotaValoresPago.Count > 0)
                    {
                        newconCotaValoresPago.ForEach(item => item.Active = false);

                        foreach (var dado in request.NewconCotaValoresPagos)
                        {
                            dado.NewconCotaId = request.Id;
                            var findDado = newconCotaValoresPago
                                .FirstOrDefault(a => a.NewconCotaId == dado.NewconCotaId && a.Parcela == dado.Parcela);

                            if (findDado is null)
                            {
                                findDado = _mapper.Map<NewconCotaValoresPago>(dado);
                                findDado.Active = true;
                                _context.NewconCotaValoresPago.Add(findDado);
                            }
                            else
                            {
                                findDado.Active = true;
                                findDado.DataVencimento = dado.DataVencimento != DateTime.MinValue ? dado.DataVencimento : findDado.DataVencimento;
                                findDado.DataPagamento = dado.DataPagamento != DateTime.MinValue ? dado.DataPagamento : findDado.DataPagamento;
                                findDado.DataContabil = dado.DataContabil != DateTime.MinValue ? dado.DataContabil : findDado.DataContabil;
                                findDado.Estornado = dado.Estornado;
                                findDado.CodigoMovimento = dado.CodigoMovimento ?? findDado.CodigoMovimento;
                                findDado.Historico = dado.Historico ?? findDado.Historico;
                                findDado.NumeroAviso = dado.NumeroAviso ?? findDado.NumeroAviso;
                                findDado.PercentualNormal = dado.PercentualNormal != 0 ? dado.PercentualNormal : findDado.PercentualNormal;
                                findDado.PercentualDiferenca = dado.PercentualDiferenca != 0 ? dado.PercentualDiferenca : findDado.PercentualDiferenca;
                                findDado.Valor = dado.Valor != 0 ? dado.Valor : findDado.Valor;
                                _context.NewconCotaValoresPago.Update(findDado);
                            }
                        }
                    }
                    newconCotaValoresPago
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaValoresPago.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Saldo Devedor */
            if (request.NewconCotaSaldoDevedor != null)
            {
                var newconCotaSaldoDevedor = await _context.NewconCotaSaldoDevedor
                    .Include(a => a.NewconCotaSaldoDevedorOutrosValores)
                    .FirstOrDefaultAsync(a => a.NewconCotaId == request.NewconCotaSaldoDevedor.NewconCotaId);

                if (newconCotaSaldoDevedor != null)
                {
                    newconCotaSaldoDevedor.PercentualPago = request.NewconCotaSaldoDevedor.PercentualPago != 0 ? request.NewconCotaSaldoDevedor.PercentualPago : newconCotaSaldoDevedor.PercentualPago;
                    newconCotaSaldoDevedor.PercentualAtraso = request.NewconCotaSaldoDevedor.PercentualAtraso != 0 ? request.NewconCotaSaldoDevedor.PercentualAtraso : newconCotaSaldoDevedor.PercentualAtraso;
                    newconCotaSaldoDevedor.PercentualCobrar = request.NewconCotaSaldoDevedor.PercentualCobrar != 0 ? request.NewconCotaSaldoDevedor.PercentualCobrar : newconCotaSaldoDevedor.PercentualCobrar;
                    newconCotaSaldoDevedor.PercentualTotal = request.NewconCotaSaldoDevedor.PercentualTotal != 0 ? request.NewconCotaSaldoDevedor.PercentualTotal : newconCotaSaldoDevedor.PercentualTotal;
                    newconCotaSaldoDevedor.PercentualMensal = request.NewconCotaSaldoDevedor.PercentualMensal != 0 ? request.NewconCotaSaldoDevedor.PercentualMensal : newconCotaSaldoDevedor.PercentualMensal;
                    newconCotaSaldoDevedor.PercentualIdealPago = request.NewconCotaSaldoDevedor.PercentualIdealPago != 0 ? request.NewconCotaSaldoDevedor.PercentualIdealPago : newconCotaSaldoDevedor.PercentualIdealPago;
                    newconCotaSaldoDevedor.PercentualDiferenca = request.NewconCotaSaldoDevedor.PercentualDiferenca != 0 ? request.NewconCotaSaldoDevedor.PercentualDiferenca : newconCotaSaldoDevedor.PercentualDiferenca;
                    newconCotaSaldoDevedor.PercentualIdealDevido = request.NewconCotaSaldoDevedor.PercentualIdealDevido != 0 ? request.NewconCotaSaldoDevedor.PercentualIdealDevido : newconCotaSaldoDevedor.PercentualIdealDevido;

                    newconCotaSaldoDevedor.PagoFundoComum = request.NewconCotaSaldoDevedor.PagoFundoComum != 0 ? request.NewconCotaSaldoDevedor.PagoFundoComum : newconCotaSaldoDevedor.PagoFundoComum;
                    newconCotaSaldoDevedor.PagoFundoComumPercentual = request.NewconCotaSaldoDevedor.PagoFundoComumPercentual != 0 ? request.NewconCotaSaldoDevedor.PagoFundoComumPercentual : newconCotaSaldoDevedor.PagoFundoComumPercentual;
                    newconCotaSaldoDevedor.PagoFundoReserva = request.NewconCotaSaldoDevedor.PagoFundoReserva != 0 ? request.NewconCotaSaldoDevedor.PagoFundoReserva : newconCotaSaldoDevedor.PagoFundoReserva;
                    newconCotaSaldoDevedor.PagoFundoReservaPercentual = request.NewconCotaSaldoDevedor.PagoFundoReservaPercentual != 0 ? request.NewconCotaSaldoDevedor.PagoFundoReservaPercentual : newconCotaSaldoDevedor.PagoFundoReservaPercentual;
                    newconCotaSaldoDevedor.PagoTaxaAdministracao = request.NewconCotaSaldoDevedor.PagoTaxaAdministracao != 0 ? request.NewconCotaSaldoDevedor.PagoTaxaAdministracao : newconCotaSaldoDevedor.PagoTaxaAdministracao;
                    newconCotaSaldoDevedor.PagoTaxaAdministracaoPercentual = request.NewconCotaSaldoDevedor.PagoTaxaAdministracaoPercentual != 0 ? request.NewconCotaSaldoDevedor.PagoTaxaAdministracaoPercentual : newconCotaSaldoDevedor.PagoTaxaAdministracaoPercentual;
                    newconCotaSaldoDevedor.PagoAdesao = request.NewconCotaSaldoDevedor.PagoAdesao != 0 ? request.NewconCotaSaldoDevedor.PagoAdesao : newconCotaSaldoDevedor.PagoAdesao;
                    newconCotaSaldoDevedor.PagoAdesaoPercentual = request.NewconCotaSaldoDevedor.PagoAdesaoPercentual != 0 ? request.NewconCotaSaldoDevedor.PagoAdesaoPercentual : newconCotaSaldoDevedor.PagoAdesaoPercentual;
                    newconCotaSaldoDevedor.PagoSeguros = request.NewconCotaSaldoDevedor.PagoSeguros != 0 ? request.NewconCotaSaldoDevedor.PagoSeguros : newconCotaSaldoDevedor.PagoSeguros;
                    newconCotaSaldoDevedor.PagoMultas = request.NewconCotaSaldoDevedor.PagoMultas != 0 ? request.NewconCotaSaldoDevedor.PagoMultas : newconCotaSaldoDevedor.PagoMultas;
                    newconCotaSaldoDevedor.PagoJuros = request.NewconCotaSaldoDevedor.PagoJuros != 0 ? request.NewconCotaSaldoDevedor.PagoJuros : newconCotaSaldoDevedor.PagoJuros;
                    newconCotaSaldoDevedor.PagoReajusteSaldoCaixa = request.NewconCotaSaldoDevedor.PagoReajusteSaldoCaixa != 0 ? request.NewconCotaSaldoDevedor.PagoReajusteSaldoCaixa : newconCotaSaldoDevedor.PagoReajusteSaldoCaixa;
                    newconCotaSaldoDevedor.PagoReajusteSaldoCaixaPercentual = request.NewconCotaSaldoDevedor.PagoReajusteSaldoCaixaPercentual != 0 ? request.NewconCotaSaldoDevedor.PagoReajusteSaldoCaixaPercentual : newconCotaSaldoDevedor.PagoReajusteSaldoCaixaPercentual;
                    newconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixa = request.NewconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixa != 0 ? request.NewconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixa : newconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixa;
                    newconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixaPercentual = request.NewconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixaPercentual != 0 ? request.NewconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixaPercentual : newconCotaSaldoDevedor.PagoTxAdmReajSaldoCaixaPercentual;
                    newconCotaSaldoDevedor.PagoTotal = request.NewconCotaSaldoDevedor.PagoTotal != 0 ? request.NewconCotaSaldoDevedor.PagoTotal : newconCotaSaldoDevedor.PagoTotal;
                    newconCotaSaldoDevedor.PagoOutrosValores = request.NewconCotaSaldoDevedor.PagoOutrosValores != 0 ? request.NewconCotaSaldoDevedor.PagoOutrosValores : newconCotaSaldoDevedor.PagoOutrosValores;
                    newconCotaSaldoDevedor.PagoImpostos = request.NewconCotaSaldoDevedor.PagoImpostos != 0 ? request.NewconCotaSaldoDevedor.PagoImpostos : newconCotaSaldoDevedor.PagoImpostos;
                    newconCotaSaldoDevedor.ParcelasPagas = request.NewconCotaSaldoDevedor.ParcelasPagas != 0 ? request.NewconCotaSaldoDevedor.ParcelasPagas : newconCotaSaldoDevedor.ParcelasPagas;
                    newconCotaSaldoDevedor.TotalParcelasPagas = request.NewconCotaSaldoDevedor.TotalParcelasPagas != 0 ? request.NewconCotaSaldoDevedor.TotalParcelasPagas : newconCotaSaldoDevedor.TotalParcelasPagas;

                    newconCotaSaldoDevedor.DevedorFundoComum = request.NewconCotaSaldoDevedor.DevedorFundoComum != 0 ? request.NewconCotaSaldoDevedor.DevedorFundoComum : newconCotaSaldoDevedor.DevedorFundoComum;
                    newconCotaSaldoDevedor.DevedorFundoComumPercentual = request.NewconCotaSaldoDevedor.DevedorFundoComumPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorFundoComumPercentual : newconCotaSaldoDevedor.DevedorFundoComumPercentual;
                    newconCotaSaldoDevedor.DevedorFundoReserva = request.NewconCotaSaldoDevedor.DevedorFundoReserva != 0 ? request.NewconCotaSaldoDevedor.DevedorFundoReserva : newconCotaSaldoDevedor.DevedorFundoReserva;
                    newconCotaSaldoDevedor.DevedorFundoReservaPercentual = request.NewconCotaSaldoDevedor.DevedorFundoReservaPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorFundoReservaPercentual : newconCotaSaldoDevedor.DevedorFundoReservaPercentual;
                    newconCotaSaldoDevedor.DevedorTaxaAdministracao = request.NewconCotaSaldoDevedor.DevedorTaxaAdministracao != 0 ? request.NewconCotaSaldoDevedor.DevedorTaxaAdministracao : newconCotaSaldoDevedor.DevedorTaxaAdministracao;
                    newconCotaSaldoDevedor.DevedorTaxaAdministracaoPercentual = request.NewconCotaSaldoDevedor.DevedorTaxaAdministracaoPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorTaxaAdministracaoPercentual : newconCotaSaldoDevedor.DevedorTaxaAdministracaoPercentual;
                    newconCotaSaldoDevedor.DevedorAdesao = request.NewconCotaSaldoDevedor.DevedorAdesao != 0 ? request.NewconCotaSaldoDevedor.DevedorAdesao : newconCotaSaldoDevedor.DevedorAdesao;
                    newconCotaSaldoDevedor.DevedorAdesaoPercentual = request.NewconCotaSaldoDevedor.DevedorAdesaoPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorAdesaoPercentual : newconCotaSaldoDevedor.DevedorAdesaoPercentual;
                    newconCotaSaldoDevedor.DevedorSeguros = request.NewconCotaSaldoDevedor.DevedorSeguros != 0 ? request.NewconCotaSaldoDevedor.DevedorSeguros : newconCotaSaldoDevedor.DevedorSeguros;
                    newconCotaSaldoDevedor.DevedorSegurosPercentual = request.NewconCotaSaldoDevedor.DevedorSegurosPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorSegurosPercentual : newconCotaSaldoDevedor.DevedorSegurosPercentual;
                    newconCotaSaldoDevedor.DevedorMultas = request.NewconCotaSaldoDevedor.DevedorMultas != 0 ? request.NewconCotaSaldoDevedor.DevedorMultas : newconCotaSaldoDevedor.DevedorMultas;
                    newconCotaSaldoDevedor.DevedorMultasPercentual = request.NewconCotaSaldoDevedor.DevedorMultasPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorMultasPercentual : newconCotaSaldoDevedor.DevedorMultasPercentual;
                    newconCotaSaldoDevedor.DevedorJuros = request.NewconCotaSaldoDevedor.DevedorJuros != 0 ? request.NewconCotaSaldoDevedor.DevedorJuros : newconCotaSaldoDevedor.DevedorJuros;
                    newconCotaSaldoDevedor.DevedorJurosPercentual = request.NewconCotaSaldoDevedor.DevedorJurosPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorJurosPercentual : newconCotaSaldoDevedor.DevedorJurosPercentual;
                    newconCotaSaldoDevedor.DevedorReajusteSaldoCaixa = request.NewconCotaSaldoDevedor.DevedorReajusteSaldoCaixa != 0 ? request.NewconCotaSaldoDevedor.DevedorReajusteSaldoCaixa : newconCotaSaldoDevedor.DevedorReajusteSaldoCaixa;
                    newconCotaSaldoDevedor.DevedorReajusteSaldoCaixaPercentual = request.NewconCotaSaldoDevedor.DevedorReajusteSaldoCaixaPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorReajusteSaldoCaixaPercentual : newconCotaSaldoDevedor.DevedorReajusteSaldoCaixaPercentual;
                    newconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixa = request.NewconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixa != 0 ? request.NewconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixa : newconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixa;
                    newconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixaPercentual = request.NewconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixaPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixaPercentual : newconCotaSaldoDevedor.DevedorTxAdmReajSaldoCaixaPercentual;
                    newconCotaSaldoDevedor.DevedorDiferencaParcela = request.NewconCotaSaldoDevedor.DevedorDiferencaParcela != 0 ? request.NewconCotaSaldoDevedor.DevedorDiferencaParcela : newconCotaSaldoDevedor.DevedorDiferencaParcela;
                    newconCotaSaldoDevedor.DevedorDiferencaParcelaPercentual = request.NewconCotaSaldoDevedor.DevedorDiferencaParcelaPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorDiferencaParcelaPercentual : newconCotaSaldoDevedor.DevedorDiferencaParcelaPercentual;
                    newconCotaSaldoDevedor.DevedorOutrosValores = request.NewconCotaSaldoDevedor.DevedorOutrosValores != 0 ? request.NewconCotaSaldoDevedor.DevedorOutrosValores : newconCotaSaldoDevedor.DevedorOutrosValores;
                    newconCotaSaldoDevedor.DevedorOutrosValoresPercentual = request.NewconCotaSaldoDevedor.DevedorOutrosValoresPercentual != 0 ? request.NewconCotaSaldoDevedor.DevedorOutrosValoresPercentual : newconCotaSaldoDevedor.DevedorOutrosValoresPercentual;
                    newconCotaSaldoDevedor.DevedorTotal = request.NewconCotaSaldoDevedor.DevedorTotal != 0 ? request.NewconCotaSaldoDevedor.DevedorTotal : newconCotaSaldoDevedor.DevedorTotal;
                    newconCotaSaldoDevedor.ParcelasDevedor = request.NewconCotaSaldoDevedor.ParcelasDevedor != 0 ? request.NewconCotaSaldoDevedor.ParcelasDevedor : newconCotaSaldoDevedor.ParcelasDevedor;
                    newconCotaSaldoDevedor.PercentualParcelasDevedor = request.NewconCotaSaldoDevedor.PercentualParcelasDevedor != 0 ? request.NewconCotaSaldoDevedor.PercentualParcelasDevedor : newconCotaSaldoDevedor.PercentualParcelasDevedor;

                    if (newconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores?.Count > 0)
                    {
                        if (request.NewconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores.Count != 0)
                        {
                            newconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores.ForEach(item => item.Active = false);
                            foreach (var dado in request.NewconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores)
                            {
                                var findDado = newconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores
                                    .FirstOrDefault(a => a.Valor == dado.Valor && a.Vencimento == dado.Vencimento);
                                if (findDado is null)
                                {
                                    var newconCotaSaldoDevedorOutrosValorTp = _mapper.Map<NewconCotaSaldoDevedorOutrosValor>(dado);
                                    newconCotaSaldoDevedorOutrosValorTp.Active = true;
                                    newconCotaSaldoDevedorOutrosValorTp.NewconCotaSaldoDevedorId = newconCotaSaldoDevedor.NewconCotaId;
                                    _context.NewconCotaSaldoDevedorOutrosValor.Add(newconCotaSaldoDevedorOutrosValorTp);
                                }
                                else
                                {
                                    findDado.Active = true;
                                    findDado.Historico = dado.Historico ?? findDado.Historico;
                                    _context.NewconCotaSaldoDevedorOutrosValor.Update(findDado);
                                }
                            }
                            newconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores
                                .Where(a => !a.Active).ToList()
                                .ForEach(item => _context.NewconCotaSaldoDevedorOutrosValor.Update(item));
                        }
                    }
                    else
                    {
                        foreach (var dado in request.NewconCotaSaldoDevedor.NewconCotaSaldoDevedorOutrosValores)
                        {
                            var newconCotaSaldoDevedorOutrosValorTp = _mapper.Map<NewconCotaSaldoDevedorOutrosValor>(dado);
                            newconCotaSaldoDevedorOutrosValorTp.NewconCotaSaldoDevedorId = newconCotaSaldoDevedor.NewconCotaId;
                            _context.NewconCotaSaldoDevedorOutrosValor.Add(newconCotaSaldoDevedorOutrosValorTp);
                        }
                    }

                    _context.NewconCotaSaldoDevedor.Update(newconCotaSaldoDevedor);
                }
                else
                {
                    var newconCotaSaldoDevedorTp = _mapper.Map<NewconCotaSaldoDevedor>(request.NewconCotaSaldoDevedor);
                    newconCotaSaldoDevedorTp.NewconCotaId = request.Id;
                    _context.NewconCotaSaldoDevedor.Add(newconCotaSaldoDevedorTp);
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Cota Atrasos */
            if (request.NewconCotaAtrasos.Count > 0)
            {
                var newconCotaAtraso = _context.NewconCotaAtraso
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaAtraso.Count == 0)
                {
                    foreach (var dado in request.NewconCotaAtrasos)
                    {
                        var newconCotaAtrasoTp = _mapper.Map<NewconCotaAtraso>(dado);
                        newconCotaAtrasoTp.Active = true;
                        newconCotaAtrasoTp.NewconCotaId = request.Id;
                        _context.NewconCotaAtraso.Add(newconCotaAtrasoTp);
                    }
                }
                else if (newconCotaAtraso.Count > 0)
                {
                    newconCotaAtraso.ForEach(item => item.Active = false);

                    foreach (var dado in request.NewconCotaAtrasos)
                    {
                        dado.NewconCotaId = request.Id;
                        var findDado = newconCotaAtraso
                            .FirstOrDefault(a => a.NewconCotaId == dado.NewconCotaId && a.Parcela == dado.Parcela);

                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconCotaAtraso>(dado);
                            findDado.Active = true;
                            _context.NewconCotaAtraso.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                            findDado.CodigoMovimento = dado.CodigoMovimento ?? findDado.CodigoMovimento;
                            findDado.Historico = dado.Historico ?? findDado.Historico;
                            findDado.DataVencimento = dado.DataVencimento != DateTime.MinValue ? dado.DataVencimento : findDado.DataVencimento;
                            findDado.PercentualAdesao = dado.PercentualAdesao != 0 ? dado.PercentualAdesao : findDado.PercentualAdesao;
                            findDado.PercentualIdeal = dado.PercentualIdeal != 0 ? dado.PercentualIdeal : findDado.PercentualIdeal;
                            findDado.ValorParcela = dado.ValorParcela != 0 ? dado.ValorParcela : findDado.ValorParcela;
                            findDado.ValorMulta = dado.ValorMulta != 0 ? dado.ValorMulta : findDado.ValorMulta;
                            findDado.ValorJuros = dado.ValorJuros != 0 ? dado.ValorJuros : findDado.ValorJuros;
                            findDado.Total = dado.Total != 0 ? dado.Total : findDado.Total;
                            _context.NewconCotaAtraso.Update(findDado);
                        }
                    }
                    newconCotaAtraso
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaAtraso.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Cota Diferenca Parcela */
            if (request.NewconCotaSaldoDifParcela != null)
            {
                var newconCotaSaldoDifParcela = await _context.NewconCotaDifParcela
                    .FirstOrDefaultAsync(a => a.NewconCotaId == request.NewconCotaSaldoDifParcela.NewconCotaId);

                if (newconCotaSaldoDifParcela != null)
                {
                    newconCotaSaldoDifParcela.FundoComum = request.NewconCotaSaldoDifParcela.FundoComum != 0 ? request.NewconCotaSaldoDifParcela.FundoComum : newconCotaSaldoDifParcela.FundoComum;
                    newconCotaSaldoDifParcela.PercentualFundoComum = request.NewconCotaSaldoDifParcela.PercentualFundoComum != 0 ? request.NewconCotaSaldoDifParcela.PercentualFundoComum : newconCotaSaldoDifParcela.PercentualFundoComum;
                    newconCotaSaldoDifParcela.FundoReserva = request.NewconCotaSaldoDifParcela.FundoReserva != 0 ? request.NewconCotaSaldoDifParcela.FundoReserva : newconCotaSaldoDifParcela.FundoReserva;
                    newconCotaSaldoDifParcela.PercentualFundoReserva = request.NewconCotaSaldoDifParcela.PercentualFundoReserva != 0 ? request.NewconCotaSaldoDifParcela.PercentualFundoReserva : newconCotaSaldoDifParcela.PercentualFundoReserva;
                    newconCotaSaldoDifParcela.TaxaAdministracao = request.NewconCotaSaldoDifParcela.TaxaAdministracao != 0 ? request.NewconCotaSaldoDifParcela.TaxaAdministracao : newconCotaSaldoDifParcela.TaxaAdministracao;
                    newconCotaSaldoDifParcela.PercentualTaxaAdministracao = request.NewconCotaSaldoDifParcela.PercentualTaxaAdministracao != 0 ? request.NewconCotaSaldoDifParcela.PercentualTaxaAdministracao : newconCotaSaldoDifParcela.PercentualTaxaAdministracao;
                    newconCotaSaldoDifParcela.Total = request.NewconCotaSaldoDifParcela.Total != 0 ? request.NewconCotaSaldoDifParcela.Total : newconCotaSaldoDifParcela.Total;
                    newconCotaSaldoDifParcela.PercentualTotal = request.NewconCotaSaldoDifParcela.PercentualTotal != 0 ? request.NewconCotaSaldoDifParcela.PercentualTotal : newconCotaSaldoDifParcela.PercentualTotal;
                    newconCotaSaldoDifParcela.NegociadaFundoComum = request.NewconCotaSaldoDifParcela.NegociadaFundoComum != 0 ? request.NewconCotaSaldoDifParcela.NegociadaFundoComum : newconCotaSaldoDifParcela.NegociadaFundoComum;
                    newconCotaSaldoDifParcela.NegociadaPercentualFundoComum = request.NewconCotaSaldoDifParcela.NegociadaPercentualFundoComum != 0 ? request.NewconCotaSaldoDifParcela.NegociadaPercentualFundoComum : newconCotaSaldoDifParcela.NegociadaPercentualFundoComum;
                    newconCotaSaldoDifParcela.NegociadaFundoReserva = request.NewconCotaSaldoDifParcela.NegociadaFundoReserva != 0 ? request.NewconCotaSaldoDifParcela.NegociadaFundoReserva : newconCotaSaldoDifParcela.NegociadaFundoReserva;
                    newconCotaSaldoDifParcela.NegociadaPercentualFundoReserva = request.NewconCotaSaldoDifParcela.NegociadaPercentualFundoReserva != 0 ? request.NewconCotaSaldoDifParcela.NegociadaPercentualFundoReserva : newconCotaSaldoDifParcela.NegociadaPercentualFundoReserva;
                    newconCotaSaldoDifParcela.NegociadaTaxaAdministracao = request.NewconCotaSaldoDifParcela.NegociadaTaxaAdministracao != 0 ? request.NewconCotaSaldoDifParcela.NegociadaTaxaAdministracao : newconCotaSaldoDifParcela.NegociadaTaxaAdministracao;
                    newconCotaSaldoDifParcela.NegociadaPercentualTaxaAdministracao = request.NewconCotaSaldoDifParcela.NegociadaPercentualTaxaAdministracao != 0 ? request.NewconCotaSaldoDifParcela.NegociadaPercentualTaxaAdministracao : newconCotaSaldoDifParcela.NegociadaPercentualTaxaAdministracao;
                    newconCotaSaldoDifParcela.NegociadaTotal = request.NewconCotaSaldoDifParcela.NegociadaTotal != 0 ? request.NewconCotaSaldoDifParcela.NegociadaTotal : newconCotaSaldoDifParcela.NegociadaTotal;
                    newconCotaSaldoDifParcela.NegociadaPercentualTotal = request.NewconCotaSaldoDifParcela.NegociadaPercentualTotal != 0 ? request.NewconCotaSaldoDifParcela.NegociadaPercentualTotal : newconCotaSaldoDifParcela.NegociadaPercentualTotal;

                    _context.NewconCotaDifParcela.Update(newconCotaSaldoDifParcela);
                }
                else
                {
                    var newconCotaSaldoDifParcelaTp = _mapper.Map<NewconCotaDifParcela>(request.NewconCotaSaldoDifParcela);
                    newconCotaSaldoDifParcelaTp.NewconCotaId = request.Id;
                    _context.NewconCotaDifParcela.Add(newconCotaSaldoDifParcelaTp);
                }
                await _context.SaveChangesAsync();
            }

            /* Valor Parcela */
            if (request.NewconCotaValorParcela != null)
            {
                var newconCotaValorParcela = await _context.NewconCotaValorParcela
                    .FirstOrDefaultAsync(a => a.NewconCotaId == request.NewconCotaValorParcela.NewconCotaId);

                if (newconCotaValorParcela != null)
                {
                    newconCotaValorParcela.FundoComum = request.NewconCotaValorParcela.FundoComum != null ? request.NewconCotaValorParcela.FundoComum ?? 0 : newconCotaValorParcela.FundoComum;
                    newconCotaValorParcela.PercentualFundoComum = request.NewconCotaValorParcela.PercentualFundoComum != null ? request.NewconCotaValorParcela.PercentualFundoComum ?? 0 : newconCotaValorParcela.PercentualFundoComum;
                    newconCotaValorParcela.FundoReserva = request.NewconCotaValorParcela.FundoReserva != null ? request.NewconCotaValorParcela.FundoReserva ?? 0 : newconCotaValorParcela.FundoReserva;
                    newconCotaValorParcela.PercentualFundoReserva = request.NewconCotaValorParcela.PercentualFundoReserva != null ? request.NewconCotaValorParcela.PercentualFundoReserva ?? 0 : newconCotaValorParcela.PercentualFundoReserva;
                    newconCotaValorParcela.TaxaAdministracao = request.NewconCotaValorParcela.TaxaAdministracao != null ? request.NewconCotaValorParcela.TaxaAdministracao ?? 0 : newconCotaValorParcela.TaxaAdministracao;
                    newconCotaValorParcela.PercentualTaxaAdministracao = request.NewconCotaValorParcela.PercentualTaxaAdministracao != null ? request.NewconCotaValorParcela.PercentualTaxaAdministracao ?? 0 : newconCotaValorParcela.PercentualTaxaAdministracao;
                    newconCotaValorParcela.Adesao = request.NewconCotaValorParcela.Adesao != null ? request.NewconCotaValorParcela.Adesao ?? 0 : newconCotaValorParcela.Adesao;
                    newconCotaValorParcela.PercentualAdesao = request.NewconCotaValorParcela.PercentualAdesao != null ? request.NewconCotaValorParcela.PercentualAdesao ?? 0 : newconCotaValorParcela.PercentualAdesao;
                    newconCotaValorParcela.Seguro = request.NewconCotaValorParcela.Seguro != null ? request.NewconCotaValorParcela.Seguro ?? 0 : newconCotaValorParcela.Seguro;
                    newconCotaValorParcela.PercentualSeguro = request.NewconCotaValorParcela.PercentualSeguro != null ? request.NewconCotaValorParcela.PercentualSeguro ?? 0 : newconCotaValorParcela.PercentualSeguro;
                    newconCotaValorParcela.Outros = request.NewconCotaValorParcela.Outros != null ? request.NewconCotaValorParcela.Outros ?? 0 : newconCotaValorParcela.Outros;
                    newconCotaValorParcela.PercentualOutros = request.NewconCotaValorParcela.PercentualOutros != null ? request.NewconCotaValorParcela.PercentualOutros ?? 0 : newconCotaValorParcela.PercentualOutros;
                    newconCotaValorParcela.ContribuicaoMensal = request.NewconCotaValorParcela.ContribuicaoMensal != null ? request.NewconCotaValorParcela.ContribuicaoMensal ?? 0 : newconCotaValorParcela.ContribuicaoMensal;
                    newconCotaValorParcela.PercentualContribuicaoMensal = request.NewconCotaValorParcela.PercentualContribuicaoMensal != null ? request.NewconCotaValorParcela.PercentualContribuicaoMensal ?? 0 : newconCotaValorParcela.PercentualContribuicaoMensal;

                    _context.NewconCotaValorParcela.Update(newconCotaValorParcela);
                }
                else
                {
                    var newconCotaValorParcelaTp = _mapper.Map<NewconCotaValorParcela>(request.NewconCotaValorParcela);
                    newconCotaValorParcelaTp.NewconCotaId = request.Id;
                    _context.NewconCotaValorParcela.Add(newconCotaValorParcelaTp);
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Cota Agendas */
            if (request.NewconCotaAgendas.Count > 0)
            {
                var newconCotaAgendas = _context.NewconCotaAgenda
                    .Where(a => a.Active && a.NewconCotaId == request.Id).ToList();

                if (newconCotaAgendas.Count == 0)
                {
                    foreach (var dado in request.NewconCotaAgendas)
                    {
                        var newconCotaAgendaTp = _mapper.Map<NewconCotaAgenda>(dado);
                        newconCotaAgendaTp.Active = true;
                        newconCotaAgendaTp.NewconCotaId = request.Id;
                        _context.NewconCotaAgenda.Add(newconCotaAgendaTp);
                    }
                }
                else if (newconCotaAgendas.Count > 0)
                {
                    newconCotaAgendas.ForEach(item => item.Active = false);

                    foreach (var dado in request.NewconCotaAgendas)
                    {
                        dado.NewconCotaId = request.Id;
                        var findDado = newconCotaAgendas
                            .FirstOrDefault(a => a.NewconCotaId == dado.NewconCotaId && a.Protocolo == dado.Protocolo);

                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconCotaAgenda>(dado);
                            findDado.Active = true;
                            _context.NewconCotaAgenda.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                            findDado.DataOcorrencia = dado.DataOcorrencia != default ? dado.DataOcorrencia : findDado.DataOcorrencia;
                            findDado.Ocorrencias = !string.IsNullOrWhiteSpace(dado.Ocorrencias) ? dado.Ocorrencias : findDado.Ocorrencias;
                            findDado.Historico = !string.IsNullOrWhiteSpace(dado.Historico) ? dado.Historico : findDado.Historico;
                            _context.NewconCotaAgenda.Update(findDado);
                        }
                    }
                    newconCotaAgendas
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconCotaAgenda.Update(item));
                }
                await _context.SaveChangesAsync();
            }

            /* Dados Cota Comissionado */
            if (request?.NewconComissionado != null)
            {
                var newconComissionado = await _context.NewconComissionado.FirstOrDefaultAsync(a => a.Active && a.NewconCotaId == request.Id);
                if (newconComissionado != null)
                {
                    if (request.NewconComissionado.UsuarioInclusaoId != newconComissionado.UsuarioInclusaoId ||
                        request.NewconComissionado.ComissionadoId != newconComissionado.ComissionadoId ||
                        request.NewconComissionado.ComissionadoIndicadoId != newconComissionado.ComissionadoIndicadoId)
                    {
                        newconComissionado.Active = false;
                        _context.NewconComissionado.Update(newconComissionado);

                        var newConCommissionedTp = _mapper.Map<NewconComissionado>(request.NewconComissionado);
                        newConCommissionedTp.Active = true;
                        newConCommissionedTp.NewconCotaId = request.Id;
                        _context.NewconComissionado.Add(newConCommissionedTp);
                    }
                    else
                    {
                        newconComissionado.UsuarioInclusaoId = request.NewconComissionado.UsuarioInclusaoId ?? newconComissionado.UsuarioInclusaoId;
                        newconComissionado.UsuarioInclusaoNome = request.NewconComissionado.UsuarioInclusaoNome ?? newconComissionado.UsuarioInclusaoNome;
                        newconComissionado.ComissionadoIndicadoId = request.NewconComissionado.ComissionadoIndicadoId ?? newconComissionado.ComissionadoIndicadoId;
                        newconComissionado.ComissionadoIndicadoNome = request.NewconComissionado.ComissionadoIndicadoNome ?? newconComissionado.ComissionadoIndicadoNome;
                        newconComissionado.ComissionadoId = request.NewconComissionado.ComissionadoId ?? newconComissionado.ComissionadoId;
                        newconComissionado.ComissionadoNome = request.NewconComissionado.ComissionadoNome ?? newconComissionado.ComissionadoNome;
                        newconComissionado.Endereco = request.NewconComissionado.Endereco ?? newconComissionado.Endereco;
                        newconComissionado.Contato = request.NewconComissionado.Contato ?? newconComissionado.Contato;
                        newconComissionado.Telefone = request.NewconComissionado.Telefone ?? newconComissionado.Telefone;
                        newconComissionado.Cidade = request.NewconComissionado.Cidade ?? newconComissionado.Cidade;
                        newconComissionado.Uf = request.NewconComissionado.Uf ?? newconComissionado.Uf;
                        newconComissionado.Cep = request.NewconComissionado.Cep ?? newconComissionado.Cep;
                        newconComissionado.Email = request.NewconComissionado.Email ?? newconComissionado.Email;

                        _context.NewconComissionado.Update(newconComissionado);
                    }
                }
                else
                {
                    var newConCommissionedTp = _mapper.Map<NewconComissionado>(request.NewconComissionado);
                    newConCommissionedTp.Active = true;
                    newConCommissionedTp.NewconCotaId = request.Id;
                    _context.NewconComissionado.Add(newConCommissionedTp);
                }
            }

            /* Dados Cota Equipe Venda */
            if (request?.NewconEquipeVenda != null)
            {
                var newconEquipeVenda = await _context.NewconEquipeVenda.FirstOrDefaultAsync(a => a.Active && a.NewconCotaId == request.Id);
                if (newconEquipeVenda != null)
                {
                    if (request.NewconEquipeVenda.Codigo != newconEquipeVenda.Codigo ||
                        request.NewconEquipeVenda.Nome != newconEquipeVenda.Nome)
                    {
                        newconEquipeVenda.Active = false;
                        _context.NewconEquipeVenda.Update(newconEquipeVenda);

                        var newconEquipeVendaTp = _mapper.Map<NewconEquipeVenda>(request.NewconEquipeVenda);
                        newconEquipeVendaTp.Active = true;
                        newconEquipeVendaTp.NewconCotaId = request.Id;
                        _context.NewconEquipeVenda.Add(newconEquipeVendaTp);
                    }
                    else
                    {
                        newconEquipeVenda.Cidade = request.NewconEquipeVenda.Cidade ?? newconEquipeVenda.Cidade;
                        newconEquipeVenda.Uf = request.NewconEquipeVenda.Uf ?? newconEquipeVenda.Uf;
                        newconEquipeVenda.Email = request.NewconEquipeVenda.Email ?? newconEquipeVenda.Email;
                        newconEquipeVenda.DataInclusao = newconEquipeVenda.DataInclusao;

                        _context.NewconEquipeVenda.Update(newconEquipeVenda);
                    }
                }
                else
                {
                    var newConCommissionedTp = _mapper.Map<NewconEquipeVenda>(request.NewconEquipeVenda);
                    newConCommissionedTp.Active = true;
                    newConCommissionedTp.NewconCotaId = request.Id;
                    _context.NewconEquipeVenda.Add(newConCommissionedTp);
                }
            }

            /* Dados Cota Recuperacao */
            if (request?.NewconRecuperacao != null)
            {
                var newconRecuperacao = await _context.NewconRecuperacao
                    .Include(a => a.NewconPessoa)
                    .FirstOrDefaultAsync(a => a.Active && a.NewconCotaId == request.Id);

                if (newconRecuperacao != null)
                {
                    if (request.NewconRecuperacao.NewconPessoa.CnpjCpf != newconRecuperacao.NewconPessoa.CnpjCpf)
                    {
                        newconRecuperacao.Active = false;
                        _context.NewconRecuperacao.Update(newconRecuperacao);

                        var newconRecuperacaoTp = _mapper.Map<NewconRecuperacao>(request.NewconRecuperacao);
                        newconRecuperacaoTp.Active = true;
                        newconRecuperacaoTp.NewconCotaId = request.Id;
                        _context.NewconRecuperacao.Add(newconRecuperacaoTp);
                    }
                    else
                    {
                        newconRecuperacao.Cidade = request.NewconRecuperacao.Distribuicao ?? newconRecuperacao.Distribuicao;
                        newconRecuperacao.Endereco = request.NewconRecuperacao.Endereco ?? newconRecuperacao.Endereco;
                        newconRecuperacao.EnderecoCobranca = request.NewconRecuperacao.EnderecoCobranca ?? newconRecuperacao.EnderecoCobranca;
                        newconRecuperacao.Contato = request.NewconRecuperacao.Contato ?? newconRecuperacao.Contato;
                        newconRecuperacao.Telefone = request.NewconRecuperacao.Telefone ?? newconRecuperacao.Telefone;
                        newconRecuperacao.Cidade = request.NewconRecuperacao.Cidade ?? newconRecuperacao.Cidade;
                        newconRecuperacao.Uf = request.NewconRecuperacao.Uf ?? newconRecuperacao.Uf;
                        newconRecuperacao.Cep = request.NewconRecuperacao.Cep ?? newconRecuperacao.Cep;

                        _context.NewconRecuperacao.Update(newconRecuperacao);
                    }
                }
                else
                {
                    var newConCommissionedTp = _mapper.Map<NewconRecuperacao>(request.NewconRecuperacao);
                    newConCommissionedTp.Active = true;
                    newConCommissionedTp.NewconCotaId = request.Id;
                    _context.NewconRecuperacao.Add(newConCommissionedTp);
                }
            }

            /* Dados Cota Suspensao */
            if (request?.NewconSuspensao != null)
            {
                var newconSuspensao = await _context.NewconSuspensao
                    .Include(a => a.NewconPessoa)
                    .FirstOrDefaultAsync(a => a.Active && a.NewconCotaId == request.Id);

                if (newconSuspensao != null)
                {
                    if (request.NewconSuspensao.NewconPessoa.CnpjCpf != newconSuspensao.NewconPessoa.CnpjCpf)
                    {
                        newconSuspensao.Active = false;
                        _context.NewconSuspensao.Update(newconSuspensao);

                        var newconSuspensaoTp = _mapper.Map<NewconSuspensao>(request.NewconSuspensao);
                        newconSuspensaoTp.Active = true;
                        newconSuspensaoTp.NewconCotaId = request.Id;
                        _context.NewconSuspensao.Add(newconSuspensaoTp);
                    }
                    else
                    {
                        newconSuspensao.Cidade = request.NewconSuspensao.Distribuicao ?? newconSuspensao.Distribuicao;
                        newconSuspensao.Endereco = request.NewconSuspensao.Endereco ?? newconSuspensao.Endereco;
                        newconSuspensao.EnderecoCobranca = request.NewconSuspensao.EnderecoCobranca ?? newconSuspensao.EnderecoCobranca;
                        newconSuspensao.Contato = request.NewconSuspensao.Contato ?? newconSuspensao.Contato;
                        newconSuspensao.Telefone = request.NewconSuspensao.Telefone ?? newconSuspensao.Telefone;
                        newconSuspensao.Cidade = request.NewconSuspensao.Cidade ?? newconSuspensao.Cidade;
                        newconSuspensao.Uf = request.NewconSuspensao.Uf ?? newconSuspensao.Uf;
                        newconSuspensao.Cep = request.NewconSuspensao.Cep ?? newconSuspensao.Cep;

                        _context.NewconSuspensao.Update(newconSuspensao);
                    }
                }
                else
                {
                    var newConCommissionedTp = _mapper.Map<NewconSuspensao>(request.NewconSuspensao);
                    newConCommissionedTp.Active = true;
                    newConCommissionedTp.NewconCotaId = request.Id;
                    _context.NewconSuspensao.Add(newConCommissionedTp);
                }
            }

            /* Dados Cota */
            model.Contrato = request!.Contrato ?? model.Contrato;
            model.TipoCota = request.TipoCota ?? model.TipoCota;
            model.DataNascimento = request.DataNascimento ?? model.DataNascimento;
            model.CnpjCpf = request.CnpjCpf ?? model.CnpjCpf;
            model.DataAdesao = request.DataAdesao ?? model.DataAdesao;
            model.DataAlocacao = request.DataAlocacao != DateTime.MinValue ? request.DataAlocacao : model.DataAlocacao;
            model.DataCadastro = request.DataCadastro != DateTime.MinValue ? request.DataCadastro : model.DataCadastro;
            model.DataVenda = request.DataVenda != DateTime.MinValue ? request.DataVenda : model.DataVenda;
            model.PrazoPlano = request.PrazoPlano == 0 ? model.PrazoPlano : request.PrazoPlano;
            model.TipoVenda = request.TipoVenda ?? model.TipoVenda;
            model.ValorBem = request.ValorBem ?? model.ValorBem;
            model.TaxaAdminstracao = request.TaxaAdminstracao ?? model.TaxaAdminstracao;
            model.TaxaAdesao = request.TaxaAdesao ?? model.TaxaAdesao;
            model.FundoReserva = request.FundoReserva ?? model.FundoReserva;
            model.PercentualCont = request.PercentualCont ?? model.PercentualCont;
            model.ValoresPago = request.ValoresPago ?? model.ValoresPago;
            model.ValoresPagoPercentual = request.ValoresPagoPercentual ?? model.ValoresPagoPercentual;
            model.SaldoDevedor = request.SaldoDevedor ?? model.SaldoDevedor;
            model.SaldoDevedorPercentual = request.SaldoDevedorPercentual ?? model.SaldoDevedorPercentual;
            model.Atraso = request.Atraso ?? model.Atraso;
            model.ArasoPercentual = request.ArasoPercentual ?? model.ArasoPercentual;
            model.DiferencaParcela = request.DiferencaParcela ?? model.DiferencaParcela;
            model.DiferencaParcelaPercentual = request.DiferencaParcelaPercentual ?? model.DiferencaParcelaPercentual;
            model.ValorParcela = request.ValorParcela ?? model.ValorParcela;
            model.ValorParcelaPercentual = request.ValorParcelaPercentual ?? model.ValorParcelaPercentual;
            model.QuantidadeParcelaPaga = request.QuantidadeParcelaPaga ?? model.QuantidadeParcelaPaga;
            model.QuantidadeParcelaFuro = request.QuantidadeParcelaFuro ?? model.QuantidadeParcelaFuro;
            model.LanceMinimo = request.LanceMinimo ?? model.LanceMinimo;
            model.LanceMaximo = request.LanceMaximo ?? model.LanceMaximo;
            model.DataSicronizacao = request.DataSicronizacao != DateTime.MinValue ? request.DataSicronizacao : model.DataSicronizacao;
            model.UpdatedAt = DateTime.Now;
            model.DataSicronizacao = DateTime.Now;
            model.Active = true;

            _context.NewconCota.Update(model);
            await _context.SaveChangesAsync();

            var dto = _mapper.Map<NewconCotaResponse>(model);

            response.Message = "Registro atualizado com sucesso.";
            response.Data = dto;
        }
        catch (Exception ex)
        {
            response.Message = ex.Message;
            response.Success = false;
        }

        return response;
    }

    private async Task<int?> GetNewconDifGrupoAsync(NewconCotaRequest request)
    {
        var newconDifGrupo = await _context.NewconDifGrupo.FirstOrDefaultAsync(a => a.Active && a.Nome == request.NewconDifGrupo!.Nome);
        if (newconDifGrupo is null)
        {
            newconDifGrupo = _mapper.Map<NewconDifGrupo>(request.NewconDifGrupo);
            newconDifGrupo.Active = true;
            _context.NewconDifGrupo.Add(newconDifGrupo);
            await _context.SaveChangesAsync();
        }

        return newconDifGrupo?.Id;
    }

    private async Task<int?> GetNewconDesclassificacaoAsync(NewconCotaRequest request)
    {
        var newconDesclassificacao = await _context.NewconDesclassificacao
                            .Include(a => a.NewconDesclassificacaoFases)
                            .Include(a => a.NewconContemplacao)
                            .Include(a => a.NewconCliente)
                            .Include(a => a.NewconBemObjeto)
                            .FirstOrDefaultAsync(a => a.Active && a.Descricao == request.NewconDesclassificacao!.Descricao);
        if (newconDesclassificacao is null)
        {
            newconDesclassificacao = _mapper.Map<NewconDesclassificacao>(request.NewconDesclassificacao);
            newconDesclassificacao.Active = true;
            _context.NewconDesclassificacao.Add(newconDesclassificacao);
        }
        else
        {
            if (request.NewconDesclassificacao?.NewconContemplacao != null)
            {
                newconDesclassificacao.NewconContemplacaoId = await getNewconContemplacaoIdAsync(request.NewconDesclassificacao.NewconContemplacao) ?? 0;
                newconDesclassificacao.ContemplacaoManual = request.NewconDesclassificacao.ContemplacaoManual ?? newconDesclassificacao.ContemplacaoManual;
                newconDesclassificacao.DataPrimeiraAssembleia = request.NewconDesclassificacao.DataPrimeiraAssembleia != DateTime.MinValue ? request.NewconDesclassificacao.DataPrimeiraAssembleia : newconDesclassificacao.DataPrimeiraAssembleia;
                newconDesclassificacao.DataPrevisaoEncerramento = request.NewconDesclassificacao.DataPrevisaoEncerramento != DateTime.MinValue ? request.NewconDesclassificacao.DataPrevisaoEncerramento : newconDesclassificacao.DataPrevisaoEncerramento;
                newconDesclassificacao.DataChegadaCadastro = request.NewconDesclassificacao.DataChegadaCadastro != DateTime.MinValue ? request.NewconDesclassificacao.DataChegadaCadastro : newconDesclassificacao.DataChegadaCadastro;
                newconDesclassificacao.DataEntregaDocumento = request.NewconDesclassificacao.DataEntregaDocumento != DateTime.MinValue ? request.NewconDesclassificacao.DataEntregaDocumento : newconDesclassificacao.DataEntregaDocumento;
                newconDesclassificacao.DataAprovacaoCadastro = request.NewconDesclassificacao.DataAprovacaoCadastro != DateTime.MinValue ? request.NewconDesclassificacao.DataAprovacaoCadastro : newconDesclassificacao.DataAprovacaoCadastro;
            }

            if (request.NewconDesclassificacao?.NewconCliente != null)
            {
                newconDesclassificacao.NewconClienteId = await getNewconClienteIdAsync(request.NewconDesclassificacao.NewconCliente) ?? newconDesclassificacao.NewconClienteId;
            }

            if (request.NewconDesclassificacao?.NewconBemObjeto != null)
            {
                newconDesclassificacao.NewconBemObjetoId = await getNewconBemObjetoIdAsync(request!.NewconDesclassificacao!.NewconBemObjeto) ?? 0;
            }

            if (request.NewconDesclassificacao?.NewconDesclassificacaoFases?.Count > 0)
            {
                newconDesclassificacao.NewconDesclassificacaoFases.ForEach(a => a.Active = false);

                foreach (var descl in request.NewconDesclassificacao.NewconDesclassificacaoFases)
                {
                    descl.Active = true;
                    var fase = newconDesclassificacao.NewconDesclassificacaoFases
                        .FirstOrDefault(a => a.Descricao == descl.Descricao && a.Ocorrencia == descl.Ocorrencia);
                    if (fase is null)
                    {
                        fase = _mapper.Map<NewconDesclassificacaoFase>(descl);
                        fase.NewconDesclassificacaoId = newconDesclassificacao.Id;
                        fase.Active = true;
                        _context.NewconDesclassificacaoFase.Add(fase);
                    }
                    else
                    {
                        fase.Active = true;
                        fase.Data = descl.Data ?? fase.Data;
                        fase.DataConclusao = descl.DataConclusao ?? fase.DataConclusao;
                        fase.FormaAcessoInc = descl.FormaAcessoInc ?? fase.FormaAcessoInc;
                        fase.FormaAcessoAlt = descl.FormaAcessoAlt ?? fase.FormaAcessoAlt;
                        fase.Observacao = descl.Observacao ?? fase.Observacao;
                        fase.ConsorciadoFase = descl.ConsorciadoFase ?? fase.ConsorciadoFase;
                        fase.UsuarioInclusao = descl.UsuarioInclusao ?? fase.UsuarioInclusao;
                        fase.UsuarioAlteracao = descl.UsuarioAlteracao ?? fase.UsuarioAlteracao;
                        _context.NewconDesclassificacaoFase.Update(fase);
                    }
                }

                newconDesclassificacao.NewconDesclassificacaoFases
                    .Where(a => !a.Active).ToList()
                    .ForEach(item => _context.NewconDesclassificacaoFase.Update(item));
            }
        }
        await _context.SaveChangesAsync();
        return newconDesclassificacao?.Id;
    }

    private async Task<int?> GetNewconPontoEntregaAsync(NewconCotaRequest request)
    {
        var newconPontoEntrega = await _context.NewconPontoEntrega.FirstOrDefaultAsync(a => a.Active && a.Nome == request.NewconPontoEntrega!.Nome);
        if (newconPontoEntrega is null)
        {
            newconPontoEntrega = _mapper.Map<NewconPontoEntrega>(request.NewconPontoEntrega);
            newconPontoEntrega.Active = true;
            _context.NewconPontoEntrega.Add(newconPontoEntrega);
            await _context.SaveChangesAsync();
        }

        return newconPontoEntrega?.Id;
    }

    private async Task<int?> GetNewconPontoVendaAsync(NewconCotaRequest request)
    {
        var newconPontoVenda = await _context.NewconPontoVenda.FirstOrDefaultAsync(a => a.Active && a.Nome == request.NewconPontoVenda.Nome);
        if (newconPontoVenda is null)
        {
            newconPontoVenda = _mapper.Map<NewconPontoVenda>(request.NewconPontoVenda);
            newconPontoVenda.Active = true;
            _context.NewconPontoVenda.Add(newconPontoVenda);
            await _context.SaveChangesAsync();
        }

        return newconPontoVenda?.Id;
    }

    private async Task<int?> GetNewconPlanoVendaAsync(NewconCotaRequest request)
    {
        var newconPlanoVenda = await _context.NewconPlanoVenda.FirstOrDefaultAsync(a => a.Active && a.Descricao == request.NewconPlanoVenda.Descricao);
        if (newconPlanoVenda is null)
        {
            newconPlanoVenda = _mapper.Map<NewconPlanoVenda>(request.NewconPlanoVenda);
            newconPlanoVenda.Active = true;
            _context.NewconPlanoVenda.Add(newconPlanoVenda);
            await _context.SaveChangesAsync();
        }

        return newconPlanoVenda?.Id;
    }

    private async Task<int?> GetNewconUnidadeAsync(NewconCotaRequest request)
    {
        var newconUnidade = await _context.NewconUnidade.FirstOrDefaultAsync(a => a.Active && a.Nome == request.NewconUnidade.Nome);
        if (newconUnidade is null)
        {
            newconUnidade = _mapper.Map<NewconUnidade>(request.NewconUnidade);
            newconUnidade.Active = true;
            _context.NewconUnidade.Add(newconUnidade);
            await _context.SaveChangesAsync();
        }
        return newconUnidade?.Id;
    }

    private async Task<int?> GetNewconEmpresaAsync(NewconCotaRequest request)
    {
        var newconEmpresa = await _context.NewconEmpresa.FirstOrDefaultAsync(a => a.Active && a.Nome == request.NewconEmpresa.Nome);
        if (newconEmpresa is null)
        {
            newconEmpresa = _mapper.Map<NewconEmpresa>(request.NewconEmpresa);
            newconEmpresa.Active = true;
            _context.NewconEmpresa.Add(newconEmpresa);
            await _context.SaveChangesAsync();
        }

        return newconEmpresa?.Id;
    }

    private async Task<int?> getNewconNewconFilialAsync(NewconFilialCreateRequest newconFilialRequest)
    {
        var newconFilialVenda = await _context.NewconFilial.FirstOrDefaultAsync(a => a.Active && a.Nome == newconFilialRequest.Nome);
        if (newconFilialVenda is null)
        {
            newconFilialVenda = _mapper.Map<NewconFilial>(newconFilialRequest);
            newconFilialVenda.Active = true;
            _context.NewconFilial.Add(newconFilialVenda);
        }
        else
        {
            newconFilialVenda.Active = true;
            newconFilialVenda.Nome = newconFilialRequest.Nome ?? newconFilialVenda.Nome;
            newconFilialVenda.Endereco = newconFilialRequest.Endereco ?? newconFilialVenda.Endereco;
            newconFilialVenda.Cidade = newconFilialRequest.Cidade ?? newconFilialVenda.Cidade;
            newconFilialVenda.Uf = newconFilialRequest.Uf ?? newconFilialVenda.Uf;
            newconFilialVenda.Cep = newconFilialRequest.Cep ?? newconFilialVenda.Cep;
            newconFilialVenda.Email = newconFilialRequest.Email ?? newconFilialVenda.Email;
            newconFilialVenda.Telefone = newconFilialRequest.Telefone ?? newconFilialVenda.Telefone;
            newconFilialVenda.Fax = newconFilialRequest.Fax ?? newconFilialVenda.Fax;

            _context.NewconFilial.Update(newconFilialVenda);
        }
        await _context.SaveChangesAsync();
        return newconFilialVenda.Id;
    }

    private async Task<int?> getNewconAssembleiaIdAsync(NewconAssembleiaCreateRequest newconAssembleiaRequest)
    {
        var newconAssembleia = await _context.NewconAssembleia.FirstOrDefaultAsync(a => a.Active && a.Descricao == newconAssembleiaRequest.Descricao);
        if (newconAssembleia is null)
        {
            newconAssembleia = _mapper.Map<NewconAssembleia>(newconAssembleiaRequest);
            newconAssembleia.Active = true;
            _context.NewconAssembleia.Add(newconAssembleia);
        }
        else
        {
            newconAssembleia.DataAssembleia = newconAssembleiaRequest.DataAssembleia != DateTime.MinValue ? newconAssembleiaRequest.DataAssembleia : newconAssembleia.DataAssembleia;
            newconAssembleia.DataVencimento = newconAssembleiaRequest.DataVencimento != DateTime.MinValue ? newconAssembleiaRequest.DataVencimento : newconAssembleia.DataVencimento;
            newconAssembleia.Active = true;
            _context.NewconAssembleia.Update(newconAssembleia);
        }
        await _context.SaveChangesAsync();
        return newconAssembleia.Id;
    }

    private async Task<int?> getNewconPessoaIdAsync(NewconPessoaCreateRequest newconPessoaRequest)
    {
        var newconPessoa = await _context.NewconPessoa
            .Include(a => a.NewconPessoaEnderecos)
            .Include(b => b.NewconPessoaContatos)
            .FirstOrDefaultAsync(a => a.CnpjCpf == newconPessoaRequest.CnpjCpf);

        if (newconPessoa is null)
        {
            newconPessoa = _mapper.Map<NewconPessoa>(newconPessoaRequest);
            newconPessoa.Active = true;
            _context.NewconPessoa.Add(newconPessoa);
        }
        else
        {
            newconPessoa.Documento = newconPessoaRequest.Documento ?? newconPessoa.Documento;
            newconPessoa.OrgaoEmissor = newconPessoaRequest.OrgaoEmissor ?? newconPessoa.OrgaoEmissor;
            newconPessoa.Nacionalidade = newconPessoaRequest.Nacionalidade ?? newconPessoa.Nacionalidade;
            newconPessoa.Profissao = newconPessoaRequest.Profissao ?? newconPessoa.Profissao;
            newconPessoa.EstadoCivil = newconPessoaRequest.EstadoCivil ?? newconPessoa.EstadoCivil;
            newconPessoa.Sexo = newconPessoaRequest.Sexo ?? newconPessoa.Sexo;
            newconPessoa.PerfilCivico = newconPessoaRequest.PerfilCivico ?? newconPessoa.PerfilCivico;

            if (newconPessoaRequest.NewconPessoaEnderecos?.Count > 0)
            {
                newconPessoa.NewconPessoaEnderecos.ForEach(item => item.Active = false);
                foreach (var dado in newconPessoaRequest.NewconPessoaEnderecos)
                {
                    var findDado = newconPessoa.NewconPessoaEnderecos
                        .FirstOrDefault(a => a.Endereco == dado.Endereco);
                    if (findDado is null)
                    {
                        var newconPessoaEnderecoTp = _mapper.Map<NewconPessoaEndereco>(dado);
                        newconPessoaEnderecoTp.Active = true;
                        newconPessoaEnderecoTp.NewconPessoaId = newconPessoa.Id;
                        _context.NewconPessoaEndereco.Add(newconPessoaEnderecoTp);
                    }
                    else
                    {
                        findDado.Active = true;
                        findDado.Cep = dado.Cep ?? findDado.Cep;
                        findDado.CodigoIbge = dado.CodigoIbge ?? findDado.CodigoIbge;
                        findDado.Cidade = dado.Cidade ?? findDado.Cidade;
                        findDado.Uf = dado.Uf ?? findDado.Uf;
                        _context.NewconPessoaEndereco.Update(findDado);
                    }
                }
                newconPessoa.NewconPessoaEnderecos
                    .Where(a => !a.Active).ToList()
                    .ForEach(item => _context.NewconPessoaEndereco.Update(item));
            }

            if (newconPessoaRequest.NewconPessoaContatos?.Count > 0)
            {
                newconPessoa.NewconPessoaContatos.ForEach(item => item.Active = false);
                foreach (var dado in newconPessoaRequest.NewconPessoaContatos)
                {
                    var findDado = newconPessoa.NewconPessoaContatos
                        .FirstOrDefault(a => a.Tipo == dado.Tipo && a.Contato == dado.Contato);

                    if (findDado is null)
                    {
                        var newconPessoaContatoTp = _mapper.Map<NewconPessoaContato>(dado);
                        newconPessoaContatoTp.Active = true;
                        newconPessoaContatoTp.NewconPessoaId = newconPessoa.Id;
                        _context.NewconPessoaContato.Add(newconPessoaContatoTp);
                    }
                    else
                    {
                        findDado.Active = true;
                        findDado.Prefixo = dado.Prefixo ?? findDado.Prefixo;
                        findDado.Observacao = dado.Observacao ?? findDado.Observacao;
                        _context.NewconPessoaContato.Update(findDado);
                    }
                }

                newconPessoa.NewconPessoaContatos
                    .Where(a => !a.Active).ToList()
                    .ForEach(item => _context.NewconPessoaContato.Update(item));
            }

            newconPessoa.Active = true;
            _context.NewconPessoa.Update(newconPessoa);
        }
        await _context.SaveChangesAsync();
        return newconPessoa.Id;
    }

    private async Task<int?> getNewconContemplacaoIdAsync(NewconContemplacaoCreateRequest newconContemplacaoRequest)
    {
        var newconContemplacao = await _context.NewconContemplacao.FirstOrDefaultAsync(a => a.Active && a.Descricao == newconContemplacaoRequest.Descricao);
        if (newconContemplacao is null)
        {
            newconContemplacao = _mapper.Map<NewconContemplacao>(newconContemplacaoRequest);
            newconContemplacao.Active = true;
            _context.NewconContemplacao.Add(newconContemplacao);
        }
        else
        {
            newconContemplacao.Descricao = newconContemplacaoRequest.Descricao;
            newconContemplacao.DataContemplacao = newconContemplacaoRequest.DataContemplacao;
            newconContemplacao.TipoContemplacao = newconContemplacaoRequest.TipoContemplacao;
            newconContemplacao.DataEntrega = newconContemplacaoRequest.DataEntrega;
            newconContemplacao.Active = true;
            _context.NewconContemplacao.Update(newconContemplacao);
        }
        await _context.SaveChangesAsync();
        return newconContemplacao.Id;
    }

    private async Task<int?> getNewconClienteIdAsync(NewconClienteCreateRequest newconClienteRequest)
    {
        var newconCliente = await _context.NewconCliente.FirstOrDefaultAsync(a => a.Active && a.CnpjCpf == newconClienteRequest.CnpjCpf);
        if (newconCliente is null)
        {
            newconCliente = _mapper.Map<NewconCliente>(newconClienteRequest);
            if (newconClienteRequest!.NewconPessoa != null)
                newconCliente.NewconPessoaId = await getNewconPessoaIdAsync(newconClienteRequest.NewconPessoa) ?? newconCliente.NewconPessoaId;
            newconCliente.Active = true;
            _context.NewconCliente.Add(newconCliente);
        }
        else
        {
            if (newconClienteRequest!.NewconPessoa != null)
                newconCliente.NewconPessoaId = await getNewconPessoaIdAsync(newconClienteRequest.NewconPessoa) ?? newconCliente.NewconPessoaId;

            newconCliente.RecebeEmailOferta = newconClienteRequest.RecebeEmailOferta;
            newconCliente.CompartilharComParceiros = newconClienteRequest.CompartilharComParceiros;
            newconCliente.RepresentanteGrupo = newconClienteRequest.RepresentanteGrupo;
            newconCliente.Active = true;

            if (newconClienteRequest.NewconClienteBancos?.Count > 0)
            {
                newconCliente.NewconClienteBancos.ForEach(item => item.Active = false);
                foreach (var dado in newconClienteRequest.NewconClienteBancos)
                {
                    var findDado = newconCliente.NewconClienteBancos
                        .FirstOrDefault(a => a.Banco == dado.Banco && a.Agencia == dado.Agencia && a.Conta == dado.Conta);
                    if (findDado is null)
                    {
                        var newconClienteBancoTp = _mapper.Map<NewconClienteBanco>(dado);
                        newconClienteBancoTp.Active = true;
                        newconClienteBancoTp.NewconClienteId = newconCliente.Id;
                        _context.NewconClienteBanco.Add(newconClienteBancoTp);
                    }
                    else
                    {
                        findDado.Active = true;
                        findDado.ContaDigito = dado.ContaDigito ?? findDado.ContaDigito;
                        findDado.ChavePix = dado.ChavePix ?? findDado.ChavePix;
                        _context.NewconClienteBanco.Update(findDado);
                    }
                }

                newconCliente.NewconClienteBancos
                    .Where(a => !a.Active).ToList()
                    .ForEach(item => _context.NewconClienteBanco.Update(item));
            }
            _context.NewconCliente.Update(newconCliente);
        }
        await _context.SaveChangesAsync();
        return newconCliente.Id;
    }

    private async Task<int?> getNewconBemObjetoIdAsync(NewconBemObjetoCreateRequest newconBemObjetoRequest)
    {
        var newconBemObjeto = await _context.NewconBemObjeto
            .Include(a => a.NewconBemObjetoRendimento)
                .ThenInclude(aa => aa.NewconBemObjetoRendimentoMensal)
            .Include(b => b.NewconBemObjetoFgts)
                .ThenInclude(bb => bb.NewconBemObjetoFgtsMensal)
            .Include(c => c.NewconBemObjetoPagamentos)
            .FirstOrDefaultAsync(a => a.Active && a.Descricao == newconBemObjetoRequest.Descricao && a.NumeroBem == newconBemObjetoRequest.NumeroBem);

        if (newconBemObjeto is null)
        {
            newconBemObjeto = _mapper.Map<NewconBemObjeto>(newconBemObjetoRequest);
            newconBemObjeto.Active = true;
            _context.NewconBemObjeto.Add(newconBemObjeto);
        }
        else
        {
            newconBemObjeto.NumeroBem = newconBemObjetoRequest.NumeroBem;
            newconBemObjeto.Descricao = newconBemObjetoRequest.Descricao;
            newconBemObjeto.Situacao = newconBemObjetoRequest.Situacao;
            newconBemObjeto.TipoPagamento = newconBemObjetoRequest.TipoPagamento;
            newconBemObjeto.TipoAquisicao = newconBemObjetoRequest.TipoAquisicao;
            newconBemObjeto.ValorPago = newconBemObjetoRequest.ValorPago;
            newconBemObjeto.ValorQuitacao = newconBemObjetoRequest.ValorQuitacao;
            newconBemObjeto.ValorBem = newconBemObjetoRequest.ValorBem;
            newconBemObjeto.BemImovel = newconBemObjetoRequest.BemImovel;
            newconBemObjeto.CreditoOriginal = newconBemObjetoRequest.CreditoOriginal;
            newconBemObjeto.CreditoCorrigido = newconBemObjetoRequest.CreditoCorrigido;
            newconBemObjeto.FGTS = newconBemObjetoRequest.FGTS;
            newconBemObjeto.Rendimento = newconBemObjetoRequest.Rendimento;
            newconBemObjeto.ValorAntecipacao = newconBemObjetoRequest.ValorAntecipacao;
            newconBemObjeto.PagamentosRealizados = newconBemObjetoRequest.PagamentosRealizados;
            newconBemObjeto.TaxasPendentes = newconBemObjetoRequest.TaxasPendentes;
            newconBemObjeto.ValorPendenteEntrega = newconBemObjetoRequest.ValorPendenteEntrega;
            newconBemObjeto.ValorAPagar = newconBemObjetoRequest.ValorAPagar;
            newconBemObjeto.DataConfirmacao = newconBemObjetoRequest.DataConfirmacao;
            newconBemObjeto.DataAntecipacao = newconBemObjetoRequest.DataAntecipacao;
            newconBemObjeto.DataEntregaBem = newconBemObjetoRequest.DataEntregaBem;
            newconBemObjeto.Garantia = newconBemObjetoRequest.Garantia;
            newconBemObjeto.Observacao = newconBemObjetoRequest.Observacao;

            if (newconBemObjetoRequest?.NewconBemObjetoRendimento != null)
            {
                newconBemObjeto.NewconBemObjetoRendimento = newconBemObjeto.NewconBemObjetoRendimento ?? new();
                newconBemObjeto.NewconBemObjetoRendimento.NewconBemObjetoId = newconBemObjeto.Id;

                newconBemObjeto.NewconBemObjetoRendimento.DataInicioCorrecao = newconBemObjetoRequest.NewconBemObjetoRendimento.DataInicioCorrecao ?? newconBemObjeto.NewconBemObjetoRendimento.DataInicioCorrecao;
                newconBemObjeto.NewconBemObjetoRendimento.CodigoMoedaCorrecao = newconBemObjetoRequest.NewconBemObjetoRendimento.CodigoMoedaCorrecao ?? newconBemObjeto.NewconBemObjetoRendimento.CodigoMoedaCorrecao;
                newconBemObjeto.NewconBemObjetoRendimento.DescricaoMoedaCorrecao = newconBemObjetoRequest.NewconBemObjetoRendimento.DescricaoMoedaCorrecao ?? newconBemObjeto.NewconBemObjetoRendimento.DescricaoMoedaCorrecao;
                newconBemObjeto.NewconBemObjetoRendimento.TotalRendimento = newconBemObjetoRequest.NewconBemObjetoRendimento.TotalRendimento ?? newconBemObjeto.NewconBemObjetoRendimento.TotalRendimento;
                newconBemObjeto.NewconBemObjetoRendimento.Active = true;

                if (newconBemObjetoRequest?.NewconBemObjetoRendimento?.NewconBemObjetoRendimentoMensal?.Count > 0)
                {
                    newconBemObjeto?.NewconBemObjetoRendimento?.NewconBemObjetoRendimentoMensal?.ForEach(a => a.Active = false);
                    foreach (var dado in newconBemObjetoRequest.NewconBemObjetoRendimento.NewconBemObjetoRendimentoMensal)
                    {
                        var findDado = newconBemObjeto!.NewconBemObjetoRendimento!.NewconBemObjetoRendimentoMensal?
                            .FirstOrDefault(a => a.AnoMes == dado.AnoMes);
                        if (findDado is null)
                        {
                            findDado = _mapper.Map<NewconBemObjetoRendimentoMensal>(dado);
                            findDado.Active = true;
                            findDado.NewconBemObjetoRendimentoId = newconBemObjeto.NewconBemObjetoRendimento.NewconBemObjetoId;
                            _context.NewconBemObjetoRendimentoMensal.Add(findDado);
                        }
                        else
                        {
                            findDado.Active = true;
                            findDado.ValorRendimento = dado.ValorRendimento;
                            findDado.ValorIR = dado.ValorIR;
                            findDado.ValorRendimentoLiquido = dado.ValorRendimentoLiquido;

                            _context.NewconBemObjetoRendimentoMensal.Update(findDado);
                        }
                    }
                }
            }

            if (newconBemObjetoRequest?.NewconBemObjetoFgts != null)
            {
                if (newconBemObjeto!.NewconBemObjetoFgts == null)
                {
                    newconBemObjeto.NewconBemObjetoFgts = new();
                    newconBemObjeto.NewconBemObjetoFgts.NewconBemObjetoId = newconBemObjeto.Id;
                    newconBemObjeto.NewconBemObjetoFgts.Active = true;
                    _context.NewconBemObjetoFgts.Add(newconBemObjeto.NewconBemObjetoFgts);
                }
                else
                {
                    newconBemObjeto.NewconBemObjetoFgts.Active = true;
                    newconBemObjeto.NewconBemObjetoFgts.ValorSolicitacao = newconBemObjetoRequest.NewconBemObjetoFgts.ValorSolicitacao;
                    newconBemObjeto.NewconBemObjetoFgts.ValorRepasse = newconBemObjetoRequest.NewconBemObjetoFgts.ValorRepasse;

                    newconBemObjeto.NewconBemObjetoFgts?.NewconBemObjetoFgtsMensal?.ForEach(a => a.Active = false);

                    if (newconBemObjetoRequest.NewconBemObjetoFgts?.NewconBemObjetoFgtsMensal?.Count() > 0)
                    {
                        foreach (var fgts in newconBemObjetoRequest.NewconBemObjetoFgts.NewconBemObjetoFgtsMensal)
                        {
                            var fgtsMensal = _context.NewconBemObjetoFgtsMensal
                                .FirstOrDefault(a => a.CodigoMovimento == fgts.CodigoMovimento);

                            if (fgtsMensal is null)
                            {
                                fgtsMensal = _mapper.Map<NewconBemObjetoFgtsMensal>(fgts);
                                fgtsMensal.Active = true;
                                fgtsMensal.NewconBemObjetoFgtsId = newconBemObjeto.NewconBemObjetoFgts!.NewconBemObjetoId;
                                _context.NewconBemObjetoFgtsMensal.Add(fgtsMensal);
                            }
                            else
                            {
                                fgtsMensal.Active = true;
                                fgtsMensal.Valor = fgts.Valor;
                                fgtsMensal.Contabilizacao = fgts.Contabilizacao;
                                fgtsMensal.Histotico = fgts.Histotico;
                                _context.NewconBemObjetoFgtsMensal.Update(fgtsMensal);
                            }
                        }
                        newconBemObjeto!.NewconBemObjetoFgts?.NewconBemObjetoFgtsMensal
                            .Where(a => !a.Active).ToList()
                            .ForEach(item => _context.NewconBemObjetoFgtsMensal.Update(item));
                    }
                }

                if (newconBemObjetoRequest?.NewconBemObjetoPagamentos?.Count > 0)
                {
                    newconBemObjeto!.NewconBemObjetoPagamentos?.ForEach(a => a.Active = false);

                    foreach (var pag in newconBemObjetoRequest.NewconBemObjetoPagamentos)
                    {
                        var pagamento = _context.NewconBemObjetoPagamento
                            .FirstOrDefault(a => a.Parcela == pag.Parcela && a.CodigoMovimento == pag.CodigoMovimento);
                        if (pagamento is null)
                        {
                            pagamento = _mapper.Map<NewconBemObjetoPagamento>(pag);
                            pagamento.Active = true;
                            pagamento.NewconBemObjetoId = newconBemObjeto.Id;
                            _context.NewconBemObjetoPagamento.Add(pagamento);
                        }
                        else
                        {
                            pagamento.Active = true;
                            pagamento.Valor = pag.Valor;
                            pagamento.Historico = pag.Historico;
                            pagamento.DataVencimento = pag.DataVencimento;
                            pagamento.DataPagamento = pag.DataPagamento;
                            pagamento.PercentualNormal = pag.PercentualNormal;
                            pagamento.PercentualDiferenca = pag.PercentualDiferenca;
                            _context.NewconBemObjetoPagamento.Update(pagamento);
                        }
                    }
                    newconBemObjeto.NewconBemObjetoPagamentos!
                        .Where(a => !a.Active).ToList()
                        .ForEach(item => _context.NewconBemObjetoPagamento.Update(item));
                }
            }
        }
        await _context.SaveChangesAsync();
        return newconBemObjeto!.Id;
    }
}
