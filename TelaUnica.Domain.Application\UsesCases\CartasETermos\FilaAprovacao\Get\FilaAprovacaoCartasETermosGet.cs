using System;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;
using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Get;

public class FilaAprovacaoCartasETermosGet(
    IPedidoCartasETermosRepository repo,
    IDatacobDapperRepository dapperDCRep
    ) : IFilaAprovacaoCartasETermosGet
{
    public IPedidoCartasETermosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<List<ItemFilaAprovacaoCartasETermosResponse>>> Handle(CancellationToken cancellationToken)
    {
        var response = new ServiceResponse<List<ItemFilaAprovacaoCartasETermosResponse>>();

        try
        {
            var listaPedidos = await _repo.GetList(cancellationToken);
            List<ItemFilaAprovacaoCartasETermosResponse> lista = [];
            if (listaPedidos.Count > 0)
            {

                foreach (var item in listaPedidos)
                {
                    var contrato = (await _dapperDcRep.SimpleInfoByContratctId(item.Crm, item.IdContrato)).Data ?? throw new("Dados do contrato não encontrados.");
                    lista.Add(new ItemFilaAprovacaoCartasETermosResponse()
                    {
                        Id = item.Id,
                        IdOperador = item.IdOperador,
                        Operador = item.User.Name,
                        IdFinanciado = item.IdFinanciado,
                        Financiado = contrato.Nome ?? string.Empty,
                        Status = item.Status,
                        Tipo = (int)item.Tipo,
                        TipoDescricao = item.Tipo.GetDisplayName(),
                        IdContrato = item.IdContrato,
                        CreatedAt = item.CreatedAt
                    });
                }
            }

            response.Success = true;
            response.Data = lista;

        }
        catch (Exception ex)
        {
            response.Success = false;
            response.Data = [];
            response.Message = ex.Message;
        }
        return response;
    }
}
