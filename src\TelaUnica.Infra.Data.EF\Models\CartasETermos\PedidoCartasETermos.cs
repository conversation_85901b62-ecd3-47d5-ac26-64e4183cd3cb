using System;
using System.ComponentModel.DataAnnotations.Schema;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Infra.Data.EF.Models.CartasETermos;

public class PedidoCartasETermos : Entity
{
    [ForeignKey("User")]
    public int IdOperador { get; set; }
    public User User { get; set; } = null!;
    public int IdFinanciado { get; set; }
    public int IdCliente { get; set; }
    public int IdGrupo { get; set; }

    public string? Status { get; set; }

    // [ForeignKey("TipoC")]
    public TipoCartaTermo Tipo { get; set; }
    // public virtual TipoCartaETermo? TipoC { get; set; }

    public virtual Termos.PedidoTermoInfos? PedidoTermoInfos { get; set; }
    public virtual DocCartasETermos? DocCartasETermos { get; set; }

    public int IdContrato { get; set; }
    public GvcRodobens Crm { get; set; }

}
