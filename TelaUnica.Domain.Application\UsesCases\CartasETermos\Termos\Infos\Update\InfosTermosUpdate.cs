using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.PedidoInfos;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Common;
using TelaUnica.Domain.Utils;
using Models = TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Update;

public class InfosTermosUpdate(
    IPedidoTermoInfosRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IInfosTermosUpdate
{
    public IPedidoTermoInfosRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<InfosTermosResponse>> Handle(InfosTermoUpdateInput input, CancellationToken cancellationToken)
    {
        ServiceResponse<InfosTermosResponse> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = await _repo.Get(input.Id, cancellationToken) ?? throw new("Infos do termo não encontrada");
            
            if (input.Parcelas != null && input.Parcelas.Count > 0)
            {
                input.Parcelas.ForEach(x => x.Validate());
                await _repo.DeleteParcelas(model, cancellationToken);
                List<Models.PedidoTermoParcelas> modelParcela = [.. input.Parcelas.Select(parc => new Models.PedidoTermoParcelas(){
                    InfosId = model.Id,
                    Numero = parc.Numero ?? 0,
                    Vencimento = parc.Vencimento ?? DateOnly.MinValue,
                    Valor = parc.Valor ?? 0
                })];
                
                await _repo.InsertParcelas(modelParcela, cancellationToken);
            }

            model.JurisdicaoAtual = input.JurisdicaoAtual ?? model.JurisdicaoAtual;
            model.NrAtual = input.NrAtual ?? model.NrAtual;
            model.ClientePrincipal = input.ClientePrincipal ?? model.ClientePrincipal;
            model.TipoAcao = input.TipoAcao ?? model.TipoAcao;
            model.AdversoPrincipal = input.AdversoPrincipal ?? model.AdversoPrincipal;
            model.GrupoCotaContrato = input.GrupoCotaContrato ?? model.GrupoCotaContrato;
            model.NrParcelasVencidas = input.NrParcelasVencidas ?? model.NrParcelasVencidas;
            model.ValorParcelasVencidas = input.ValorParcelasVencidas ?? model.ValorParcelasVencidas;
            model.MultaJuros = input.MultaJuros ?? model.MultaJuros;
            model.Custas = input.Custas ?? model.Custas;
            model.NrParcelasVincendas = input.NrParcelasVincendas ?? model.NrParcelasVincendas;
            model.ValorParcelasVincendas = input.ValorParcelasVincendas ?? model.ValorParcelasVincendas;
            model.Honorarios = input.Honorarios ?? model.Honorarios;
            model.Total = input.Total ?? model.Total;
            model.DataBase = input.DataBase ?? model.DataBase;
            model.QtdParcelasAcordadas = input.QtdParcelasAcordadas ?? model.QtdParcelasAcordadas;
            model.ValorAcordado = input.ValorAcordado ?? model.ValorAcordado;
            model.DescricaoVeiculo = input.DescricaoVeiculo ?? model.DescricaoVeiculo;

            await _repo.Update(model, cancellationToken);
            response.Data = InfosTermosResponse.FromModel(model);
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
