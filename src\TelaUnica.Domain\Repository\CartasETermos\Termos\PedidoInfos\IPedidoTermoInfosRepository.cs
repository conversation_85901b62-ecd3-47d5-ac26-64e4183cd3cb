using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Domain.Repository.CartasETermos.Termos.PedidoInfos;

public interface IPedidoTermoInfosRepository : IGenericRepository<PedidoTermoInfos>
{
    public Task<List<PedidoTermoInfos>> GetList(CancellationToken cancellationToken);
    public Task InsertParcelas(List<PedidoTermoParcelas> aggregate, CancellationToken cancellationToken);
    public Task DeleteParcelas(PedidoTermoInfos aggregate, CancellationToken cancellationToken);
    public Task<PedidoTermoInfos?> GetByPedido(Guid PedidoId, CancellationToken cancellationToken);
}
