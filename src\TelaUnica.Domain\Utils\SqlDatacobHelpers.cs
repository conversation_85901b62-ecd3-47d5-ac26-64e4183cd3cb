using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Utils;

public class SqlDatacobHelpers
{

    private readonly DatacobHelpers _helper;
    public SqlDatacobHelpers(DatacobHelpers helper)
    {
        _helper = helper;
    }
    public string SqlNegociacoes(int IdAgrupamento, int? IdParcela, bool? DetalheParcela)
    {
        var sql = @$"SELECT n.Id_Negociacao,
                        n.Id_Financiado,
                        n.Id_Agrupamento,
                        n.Id_Cliente_Web,
                        n.Descricao,
                        n.Dt_Negociacao,
                        n.Vl_Negociacao,
                        n.Id_<PERSON>ua<PERSON>,
                        u.Email  AS 'Usuario',
                        u.Nome  AS 'NomeUsuario',
                        n.Dt_Cadastro_Negociacao,
                        n.Status,
                        n.Liberado,
                        n.Dt_Liberacao,
                        n.Id_Usuario_Liberou,
                        ul.Email AS 'Usuario_Liberou',
                        n.Id_Usuario_Bloqueou,
                        ul.Email AS 'Usuario_Bloqueou',
                        n.Negociacao_Enviada,
                        n.Id_Usuario_Enviou_Negociacao,
                        n.Dt_Negocicao_Enviada,
                        n.Negociacao_Sobre,
                        n.Atraso,
                        n.Id_Parametro_Calculo_Pc,
                        n.Id_Faixa_Calculo,
                        n.Id_Faixa_Pc,
                        n.Id_Acordo,
                        n.Id_Cobrador_Lote,
                        n.Id_Cobrador_Gerador,
                        n.Id_Cobrador_Externo,
                        n.Id_Filial,
                        n.Envio_Robo,
                        n.Tipo_Calculo_Manual,
                        n.Nr_Aprovacao,
                        n.Situacao_Contrato,
                        n.Tipo_Negociacao,
                        n.Tipo_Atualizacao_Quitacao,
                        n.IsBoletagem,
                        n.IsDataPact,
                        n.Vl_Desconto_Seguro_Prestamista,
                        pcp.Descricao AS 'Parametro_Calculo',
                        b.Id_Boleto
                FROM   cob.Negociacao (nolock) n
                        INNER JOIN cob.Contrato (nolock) c
                                ON n.Id_Agrupamento = c.Id_Agrupamento
                        INNER JOIN par.Cliente (nolock) cl
                                ON cl.Id_Cliente = c.Id_Cliente
                        INNER JOIN cob.Financiado (nolock) f
                                ON f.Id_Financiado = n.Id_Financiado
                                LEFT JOIN cob.Boleto (nolock) b
                                        ON b.Id_Negociacao = n.Id_Negociacao";
        if (IdParcela is not null)
            sql += $@"        INNER JOIN cob.Negociacao_Parcela (nolock) np
                                ON np.Id_Negociacao = n.Id_Negociacao";

        sql += $@"                INNER JOIN usu.Usuario (nolock) u
                                ON u.Id_Usuario = n.Id_Usuario
                        LEFT JOIN usu.Usuario (nolock) ul
                                ON ul.Id_Usuario = n.Id_Usuario_Liberou
                        LEFT JOIN usu.Usuario (nolock) ub
                                ON ub.Id_Usuario = n.Id_Usuario_Bloqueou   
                        LEFT JOIN Par.Parametro_Calculo_Pc (nolock) pcp
                                ON pcp.Id_Parametro_Calculo_Pc = n.Id_Parametro_Calculo_Pc
                WHERE  c.Id_Contrato = {IdAgrupamento}";

        if (IdParcela is not null)
            sql += $@"         AND np.Id_Parcela = {IdParcela}";
        if (DetalheParcela is not null && DetalheParcela == true)
            sql += $@"         AND n.Negociacao_Sobre = 'P' ";

        sql += $@" GROUP  BY n.Id_Negociacao,
                        n.Id_Financiado,
                        n.Id_Agrupamento,
                        n.Id_Cliente_Web,
                        n.Descricao,
                        n.Dt_Negociacao,
                        n.Vl_Negociacao,
                        n.Id_Usuario,
                        u.Email,
                        u.Nome,
                        n.Dt_Cadastro_Negociacao,
                        n.Status,
                        n.Liberado,
                        n.Dt_Liberacao,
                        n.Id_Usuario_Liberou,
                        ul.Email,
                        n.Id_Usuario_Bloqueou,
                        ul.Email,
                        n.Negociacao_Enviada,
                        n.Id_Usuario_Enviou_Negociacao,
                        n.Dt_Negocicao_Enviada,
                        n.Negociacao_Sobre,
                        n.Atraso,
                        n.Id_Parametro_Calculo_Pc,
                        n.Id_Faixa_Calculo,
                        n.Id_Faixa_Pc,
                        n.Id_Acordo,
                        n.Id_Cobrador_Lote,
                        n.Id_Cobrador_Gerador,
                        n.Id_Cobrador_Externo,
                        n.Id_Filial,
                        n.Envio_Robo,
                        n.Tipo_Calculo_Manual,
                        n.Nr_Aprovacao,
                        n.Situacao_Contrato,
                        n.Tipo_Negociacao,
                        n.Tipo_Atualizacao_Quitacao,
                        n.IsBoletagem,
                        n.IsDataPact,
                        n.Vl_Desconto_Seguro_Prestamista,
                        pcp.Descricao,
                        b.Id_Boleto";
        return sql;
    }

    public string SqlNegociacoesContrato(string numeroContrato, int IdAgrupamento, int? IdParcela, bool? DetalheParcela)
    {
        var sql = @$"SELECT n.Id_Negociacao,
                        n.Id_Financiado,
                        n.Id_Agrupamento,
                        n.Id_Cliente_Web,
                        n.Descricao,
                        n.Dt_Negociacao,
                        n.Vl_Negociacao,
                        n.Id_Usuario,
                        u.Email  AS 'Usuario',
                        u.Nome  AS 'NomeUsuario',
                        n.Dt_Cadastro_Negociacao,
                        n.Status,
                        n.Liberado,
                        n.Dt_Liberacao,
                        n.Id_Usuario_Liberou,
                        ul.Email AS 'Usuario_Liberou',
                        n.Id_Usuario_Bloqueou,
                        ul.Email AS 'Usuario_Bloqueou',
                        n.Negociacao_Enviada,
                        n.Id_Usuario_Enviou_Negociacao,
                        n.Dt_Negocicao_Enviada,
                        n.Negociacao_Sobre,
                        n.Atraso,
                        n.Id_Parametro_Calculo_Pc,
                        n.Id_Faixa_Calculo,
                        n.Id_Faixa_Pc,
                        n.Id_Acordo,
                        n.Id_Cobrador_Lote,
                        n.Id_Cobrador_Gerador,
                        n.Id_Cobrador_Externo,
                        n.Id_Filial,
                        n.Envio_Robo,
                        n.Tipo_Calculo_Manual,
                        n.Nr_Aprovacao,
                        n.Situacao_Contrato,
                        n.Tipo_Negociacao,
                        n.Tipo_Atualizacao_Quitacao,
                        n.IsBoletagem,
                        n.IsDataPact,
                        n.Vl_Desconto_Seguro_Prestamista,
                        pcp.Descricao AS 'Parametro_Calculo',
                        b.Id_Boleto
                FROM   cob.Negociacao (nolock) n
                        INNER JOIN cob.Contrato (nolock) c
                                ON n.Id_Agrupamento = c.Id_Agrupamento
                        INNER JOIN par.Cliente (nolock) cl
                                ON cl.Id_Cliente = c.Id_Cliente
                        INNER JOIN cob.Financiado (nolock) f
                                ON f.Id_Financiado = n.Id_Financiado
                                LEFT JOIN cob.Boleto (nolock) b
                                        ON b.Id_Negociacao = n.Id_Negociacao";
        if (IdParcela is not null)
            sql += $@"        INNER JOIN cob.Negociacao_Parcela (nolock) np
                                ON np.Id_Negociacao = n.Id_Negociacao";

        sql += $@"                INNER JOIN usu.Usuario (nolock) u
                                ON u.Id_Usuario = n.Id_Usuario
                        LEFT JOIN usu.Usuario (nolock) ul
                                ON ul.Id_Usuario = n.Id_Usuario_Liberou
                        LEFT JOIN usu.Usuario (nolock) ub
                                ON ub.Id_Usuario = n.Id_Usuario_Bloqueou   
                        LEFT JOIN Par.Parametro_Calculo_Pc (nolock) pcp
                                ON pcp.Id_Parametro_Calculo_Pc = n.Id_Parametro_Calculo_Pc
                WHERE  c.Id_Contrato != {IdAgrupamento} and c.Numero_Contrato = '{numeroContrato}'";

        if (IdParcela is not null)
            sql += $@"         AND np.Id_Parcela != {IdParcela}";
        if (DetalheParcela is not null && DetalheParcela == true)
            sql += $@"         AND n.Negociacao_Sobre = 'P' ";

        sql += $@" GROUP  BY n.Id_Negociacao,
                        n.Id_Financiado,
                        n.Id_Agrupamento,
                        n.Id_Cliente_Web,
                        n.Descricao,
                        n.Dt_Negociacao,
                        n.Vl_Negociacao,
                        n.Id_Usuario,
                        u.Email,
                        u.Nome,
                        n.Dt_Cadastro_Negociacao,
                        n.Status,
                        n.Liberado,
                        n.Dt_Liberacao,
                        n.Id_Usuario_Liberou,
                        ul.Email,
                        n.Id_Usuario_Bloqueou,
                        ul.Email,
                        n.Negociacao_Enviada,
                        n.Id_Usuario_Enviou_Negociacao,
                        n.Dt_Negocicao_Enviada,
                        n.Negociacao_Sobre,
                        n.Atraso,
                        n.Id_Parametro_Calculo_Pc,
                        n.Id_Faixa_Calculo,
                        n.Id_Faixa_Pc,
                        n.Id_Acordo,
                        n.Id_Cobrador_Lote,
                        n.Id_Cobrador_Gerador,
                        n.Id_Cobrador_Externo,
                        n.Id_Filial,
                        n.Envio_Robo,
                        n.Tipo_Calculo_Manual,
                        n.Nr_Aprovacao,
                        n.Situacao_Contrato,
                        n.Tipo_Negociacao,
                        n.Tipo_Atualizacao_Quitacao,
                        n.IsBoletagem,
                        n.IsDataPact,
                        n.Vl_Desconto_Seguro_Prestamista,
                        pcp.Descricao,
                        b.Id_Boleto";
        return sql;
    }
    public string SqlNegociacaoParcelas(int IdNegociacao) => @$"SELECT p.Dt_Vencimento,
                                                                    p.Nr_Parcela,
                                                                    p.Nr_Plano,
                                                                    p.Tipo_Parcela,
                                                                    np.*,
                                                                    c.Numero_Contrato,
                                                                    CASE
                                                                        WHEN Datediff(day, p.Dt_Vencimento, Getdate()) < 0 THEN 0
                                                                        ELSE Datediff(day, p.Dt_Vencimento, Getdate())
                                                                    END AS 'NrAtraso',
                                                                    n.Liberado,
                                                                    n.Dt_Negociacao,
                                                                    n.Vl_Negociacao
                                                                FROM   cob.Negociacao_Parcela (nolock) np
                                                                    INNER JOIN cob.Negociacao (nolock) n
                                                                            ON n.Id_Negociacao = np.Id_Negociacao
                                                                    INNER JOIN cob.Parcela (nolock) p
                                                                            ON np.Id_Parcela = p.Id_Parcela
                                                                    INNER JOIN cob.Contrato (nolock) c
                                                                            ON p.Id_Contrato = c.Id_Contrato
                                                                WHERE  np.Id_Negociacao IN ( {IdNegociacao} ) ";
    public string SqlNegociacaoParcelasContrato(string numeroContrato, int idNegociacao = 0) => @$"SELECT p.Dt_Vencimento,
                                                                    p.Nr_Parcela,
                                                                    p.Nr_Plano,
                                                                    p.Tipo_Parcela,
                                                                    np.*,
                                                                    c.Numero_Contrato,
                                                                    CASE
                                                                        WHEN Datediff(day, p.Dt_Vencimento, Getdate()) < 0 THEN 0
                                                                        ELSE Datediff(day, p.Dt_Vencimento, Getdate())
                                                                    END AS 'NrAtraso',
                                                                    n.Liberado,
                                                                    n.Dt_Negociacao,
                                                                    n.Vl_Negociacao
                                                                FROM   cob.Negociacao_Parcela (nolock) np
                                                                    INNER JOIN cob.Negociacao (nolock) n
                                                                            ON n.Id_Negociacao = np.Id_Negociacao
                                                                    INNER JOIN cob.Parcela (nolock) p
                                                                            ON np.Id_Parcela = p.Id_Parcela
                                                                    INNER JOIN cob.Contrato (nolock) c
                                                                            ON p.Id_Contrato = c.Id_Contrato
                                                                WHERE  c.Numero_Contrato = '{numeroContrato}'" + (idNegociacao != 0 ? $" and np.Id_Negociacao != {idNegociacao}" : "");

    public string SqlGrupoContratoAberto(string Cpfcnpj, int IdAgrupamento) => $@"SELECT Contrato.Id_Contrato,
                                                    Contrato.Numero_Contrato,
                                                    Contrato.Id_Agrupamento,
                                                    Contrato.Id_Cliente,
                                                    Grupo.Descricao,
                                                    Financiado.Id_Financiado,
                                                    Grupo.Id_Grupo AS 'IdGrupo'
                                                FROM   cob.Contrato (nolock)
                                                    INNER JOIN cob.Financiado (nolock)
                                                            ON Financiado.Id_Financiado = Contrato.Id_Financiado
                                                    INNER JOIN par.Cliente (nolock)
                                                            ON Cliente.Id_Cliente = Contrato.Id_Cliente
                                                    INNER JOIN par.Grupo (nolock)
                                                            ON Grupo.Id_Grupo = Cliente.Id_Grupo
                                                WHERE  Financiado.Cpfcnpj = '{Cpfcnpj}'
                                                    AND Contrato.Contrato_Aberto = 1
                                                    AND Contrato.Id_Agrupamento != {IdAgrupamento}; ";

    public string SqlDadosAvalistas(int IdAgrupamento) => $@"SELECT f.Id_Cliente,
                                                f.Nome,
                                                f.Cpfcnpj,
                                                f.Rg,
                                                f.Sexo,
                                                f.Tipo_Financiado,
                                                c.Numero_Contrato
                                            FROM   cob.Financiado (nolock) f
                                                INNER JOIN cob.Contrato (nolock) c
                                                        ON c.Id_Contrato = f.Id_Contrato
                                            WHERE  f.Id_Agrupamento = {IdAgrupamento}
                                                AND f.Tipo_Financiado > 1 ";

    public string SqlDadosBens(int IdContrato) => $@"SELECT 'VEICULO' AS 'Tipo',
        g.*,
        v.Ano_Modelo,
        v.Ano_Fabricacao,
        v.Placa,
        v.Cor,
        v.Renavam,
        vg.Dt_Venda,
        vg.Vl_Venda,
        c.Numero_Contrato
    FROM   cob.Garantia (nolock) g
        INNER JOIN cob.Contrato (nolock) c
                ON g.Id_Contrato = c.Id_Contrato
        LEFT JOIN cob.Veiculo (nolock) v
                ON g.Id_Garantia = v.Id_Garantia
        LEFT JOIN cob.Veiculo_Juridico (nolock) vg
                ON g.Id_Garantia = vg.Id_Garantia
    WHERE  c.Id_Agrupamento = {IdContrato}
    AND c.Contrato_Aberto = 1
    ORDER  BY g.Id_Garantia DESC";

    public string GetDetalhesContratos(int IdFinanciado, int IdGrupo, bool isRodobens = false)
    {
        string sql = $@"SELECT c.*,
            p.Empresa                          AS 'Plataforma',
            r.Descricao                        AS 'Regua',
            f.Descricao                        AS 'Fase',
            f.Cod_Fase,
            f.Cor,
            af.Agencia,
            l.Nome_Loja,
            (SELECT TOP(1) Parcela.Dt_Vencimento
                FROM   cob.Parcela (nolock)
                    INNER JOIN cob.Contrato (nolock)
                            ON Parcela.Id_Contrato = Contrato.Id_Contrato
                    INNER JOIN par.Cliente (nolock)
                            ON Parcela.Id_Cliente = Cliente.Id_Cliente
                WHERE  Contrato.Id_Financiado = {IdFinanciado}
                    AND Cliente.Id_Grupo = {IdGrupo}";

        if (isRodobens) sql += " AND c.Id_Cliente_Web = 53 ";

        sql += $@"ORDER  BY Parcela.Nr_Parcela ASC) as 'Data_Atraso',
            CASE
                WHEN Datediff(day, (SELECT TOP(1) Parcela.Dt_Vencimento
                FROM   cob.Parcela (nolock)
                    INNER JOIN cob.Contrato (nolock)
                            ON Parcela.Id_Contrato = Contrato.Id_Contrato
                    INNER JOIN par.Cliente (nolock)
                            ON Parcela.Id_Cliente = Cliente.Id_Cliente
                WHERE  Contrato.Id_Financiado = {IdFinanciado}
                    AND Cliente.Id_Grupo = {IdGrupo}";

        if (isRodobens) sql += " AND c.Id_Cliente_Web = 53 ";

        sql += $@"ORDER  BY Parcela.Nr_Parcela ASC), Getdate()) < 0 THEN 0
                ELSE Datediff(day, (SELECT TOP(1) Parcela.Dt_Vencimento
                FROM   cob.Parcela (nolock)
                    INNER JOIN cob.Contrato (nolock)
                            ON Parcela.Id_Contrato = Contrato.Id_Contrato
                    INNER JOIN par.Cliente (nolock)
                            ON Parcela.Id_Cliente = Cliente.Id_Cliente
                WHERE  Contrato.Id_Financiado = {IdFinanciado}
                    AND Cliente.Id_Grupo = {IdGrupo}";

        if (isRodobens) sql += " AND c.Id_Cliente_Web = 53 ";

        sql += $@"ORDER  BY Parcela.Nr_Parcela ASC), Getdate())
            END AS 'Atraso'
        FROM   cob.Contrato (nolock) AS c
            LEFT JOIN par.Fase (nolock) f
                    ON f.Id_Fase = c.Id_Fase
            LEFT JOIN par.Plataforma (nolock) p
                    ON p.Id_Plataforma = c.Id_Plataforma
            LEFT JOIN par.Agencia_Filial (nolock) af
                    ON af.Id_Agencia_Filial = c.Id_Agencia_Filial
            LEFT JOIN par.Regua (nolock) r
                    ON r.Id_Regua = c.Id_Regua
            LEFT JOIN cob.Loja (nolock) l
                    ON l.Id_Loja = c.Id_Loja
        WHERE  c.Id_Financiado = {IdFinanciado}
            AND c.Id_Grupo = {IdGrupo}";

        if (isRodobens) sql += " AND c.Id_Cliente_Web = 53 ";
        return sql;
    }

    public string SqlContratosAtivos(int IdFinanciado, int IdAgrupamento, int? Ativo, string? statusInstallment = "", bool isRodobens = false)
    {
        var sql = $@"SELECT Parcela.Status,
                        Parcela.Nr_Parcela,
                        Parcela.Nr_Plano,
                        Parcela.Tipo_Parcela,
                        Tipos_Parcela.Descricao AS 'Nome_Tipo_Parcela',
                        Parcela.Id_Parcela,
                        Parcela.Dt_Vencimento,
                        Parcela.Vl_Saldo,
                        Parcela.Vl_Original,
                        Parcela.Vl_Saldo_Atualizado,
                        Parcela.Dt_Venc_Boleto,
                        Parcela.Qtde_Boleto_Emitido,
                        Parcela.Status_Negativacao,
                        Parcela.Dt_Inclusao,
                        Parcela.Documento,
                        Acordo.Nr_Acordo,
                        Acordo.Id_Acordo,
                        Negociacao.Dt_Negociacao,
                        Contrato.Id_Contrato,
                        Contrato.Numero_Contrato,
                        Contrato.Contrato_Aberto,
                        Contrato.Vl_contr,
                        CASE
                            WHEN Datediff(day, Parcela.Dt_Vencimento, Getdate()) < 0 THEN 0
                            ELSE Datediff(day, Parcela.Dt_Vencimento, Getdate())
                        END                     AS 'Atraso',
                        Grupo.Descricao AS 'Grupo',
                        Grupo.Id_Grupo AS 'GrupoId',
                        0.00 AS 'Vl_Atualizado',
                        0.00 AS 'Vl_Desc_Max',
                        Fase.Id_Fase,
                        Fase.Cod_Fase AS 'Fase',
                        Financiado.Cpfcnpj,
                        Boleto.Nr_Boleto,
                        Boleto.Dt_Status,
                        Boleto.Vl_Boleto,
                        Boleto.Id_Negociacao,
                        Negociacao.Descricao AS Neg_Descricao,
                        Contrato.Tx_Contrato,
                        Contrato.Tx_Mora,
                        Contrato.Tx_Multa,
                        Cliente.Nome_Res AS 'Cliente'
                    FROM cob.Contrato (nolock)
                        INNER JOIN par.Cliente (nolock)
                                ON Contrato.Id_Cliente = Cliente.Id_Cliente
                        INNER JOIN cob.Financiado (nolock)
                                ON Financiado.Id_Financiado = Contrato.Id_Financiado
                        LEFT JOIN cob.Parcela (nolock)
                                ON Parcela.Id_Contrato = Contrato.Id_Contrato            
                                {(statusInstallment == "Ab" || statusInstallment == "Ac" ? " and Parcela.status = 'A' " :
                                    !string.IsNullOrEmpty(statusInstallment) ? " and Parcela.status = '" + statusInstallment + "'" : "")}
                        LEFT JOIN cob.Negociacao_Parcela (nolock)
                                ON Negociacao_Parcela.Id_Parcela = Parcela.Id_Parcela
                        LEFT JOIN cob.Negociacao (nolock)
                                ON Negociacao_Parcela.Id_Negociacao = Negociacao.Id_Negociacao
                        LEFT JOIN cob.Acordo (nolock)
                                ON Parcela.Id_Acordo = Acordo.Id_Acordo
                        LEFT JOIN par.Tipos_Parcela (nolock)
                                ON Tipos_Parcela.Id_Tipos_Parcela = Parcela.Tipo_Parcela
                        LEFT JOIN par.Grupo (nolock)
                                ON Grupo.Id_Grupo = Cliente.Id_Grupo
                        LEFT JOIN par.Fase (nolock)
                                ON Fase.Id_Fase = Contrato.Id_Fase
                        LEFT JOIN cob.Boleto (nolock) 
                                ON Boleto.Id_Negociacao = Negociacao.Id_Negociacao
                    WHERE  Financiado.Id_Financiado = {IdFinanciado}
                        AND Contrato.Id_Agrupamento = {IdAgrupamento}        
                        AND Contrato.Contrato_Aberto in ";
        if (Ativo is null)
            sql += "(0,1)";
        else
            sql += $"({Ativo})";
        if (isRodobens)
            sql += " AND Contrato.Id_Cliente_Web = 53 ";
        sql += " ORDER  BY Contrato.Id_Contrato, Parcela.Nr_Parcela ASC ";
        return sql;
    }

    public string SqlContratosAtivosOther(string numero_contrato, int IdFinanciado, int IdAgrupamento, int? Ativo, string? statusInstallment = "", bool isRodobens = false)
    {
        var sql = $@"SELECT Parcela.Status,
                        Parcela.Nr_Parcela,
                        Parcela.Nr_Plano,
                        Parcela.Tipo_Parcela,
                        Tipos_Parcela.Descricao AS 'Nome_Tipo_Parcela',
                        Parcela.Id_Parcela,
                        Parcela.Dt_Vencimento,
                        Parcela.Vl_Saldo,
                        Parcela.Vl_Original,
                        Parcela.Vl_Saldo_Atualizado,
                        Parcela.Dt_Venc_Boleto,
                        Parcela.Qtde_Boleto_Emitido,
                        Parcela.Status_Negativacao,
                        Parcela.Dt_Inclusao,
                        Parcela.Documento,
                        Acordo.Nr_Acordo,
                        Acordo.Id_Acordo,
                        Negociacao.Dt_Negociacao,
                        Contrato.Id_Contrato,
                        Contrato.Numero_Contrato,
                        Contrato.Contrato_Aberto,
                        Contrato.Vl_contr,
                        CASE
                            WHEN Datediff(day, Parcela.Dt_Vencimento, Getdate()) < 0 THEN 0
                            ELSE Datediff(day, Parcela.Dt_Vencimento, Getdate())
                        END                     AS 'Atraso',
                        Grupo.Descricao AS 'Grupo',
                        Grupo.Id_Grupo AS 'GrupoId',
                        0.00 AS 'Vl_Atualizado',
                        0.00 AS 'Vl_Desc_Max',
                        Fase.Id_Fase,
                        Fase.Cod_Fase AS 'Fase',
                        Financiado.Cpfcnpj,
                        Boleto.Nr_Boleto,
                        Boleto.Dt_Status,
                        Boleto.Vl_Boleto,
                        Boleto.Id_Negociacao,
                        Negociacao.Descricao AS Neg_Descricao,
                        Contrato.Tx_Contrato,
                        Contrato.Tx_Mora,
                        Contrato.Tx_Multa
                    FROM cob.Contrato (nolock)
                        INNER JOIN par.Cliente (nolock)
                                ON Contrato.Id_Cliente = Cliente.Id_Cliente
                        INNER JOIN cob.Financiado (nolock)
                                ON Financiado.Id_Financiado = Contrato.Id_Financiado
                        LEFT JOIN cob.Parcela (nolock)
                                ON Parcela.Id_Contrato = Contrato.Id_Contrato            
                                {(statusInstallment == "Ab" || statusInstallment == "Ac" ? " and Parcela.status = 'A' " :
                                    !string.IsNullOrEmpty(statusInstallment) ? " and Parcela.status = '" + statusInstallment + "'" : "")}
                        LEFT JOIN cob.Negociacao_Parcela (nolock)
                                ON Negociacao_Parcela.Id_Parcela = Parcela.Id_Parcela
                        LEFT JOIN cob.Negociacao (nolock)
                                ON Negociacao_Parcela.Id_Negociacao = Negociacao.Id_Negociacao
                        LEFT JOIN cob.Acordo (nolock)
                                ON Parcela.Id_Acordo = Acordo.Id_Acordo
                        LEFT JOIN par.Tipos_Parcela (nolock)
                                ON Tipos_Parcela.Id_Tipos_Parcela = Parcela.Tipo_Parcela
                        LEFT JOIN par.Grupo (nolock)
                                ON Grupo.Id_Grupo = Cliente.Id_Grupo
                        LEFT JOIN par.Fase (nolock)
                                ON Fase.Id_Fase = Contrato.Id_Fase
                        LEFT JOIN cob.Boleto (nolock) 
                                ON Boleto.Id_Negociacao = Negociacao.Id_Negociacao
                    WHERE not (Financiado.Id_Financiado = {IdFinanciado}  AND Contrato.Id_Agrupamento = {IdAgrupamento}) 
                        AND Financiado.Cpfcnpj = (select top 1 Cpfcnpj from cob.Financiado where Id_Financiado = {IdFinanciado})
                        AND Contrato.Numero_Contrato = '{numero_contrato}'
                        AND Contrato.Contrato_Aberto in ";
        if (Ativo is null)
            sql += "(0,1)";
        else
            sql += $"({Ativo})";
        if (isRodobens)
            sql += " AND Contrato.Id_Cliente_Web = 53 ";
        sql += " ORDER  BY Contrato.Id_Contrato, Parcela.Nr_Parcela ASC ";
        return sql;
    }

    public string SqlEnderecos(int IdFinanciado) => $@"SELECT     e.*,
                                                                CASE
                                                                        WHEN e.id_endereco = f.id_endereco_carta THEN 1
                                                                        ELSE 0
                                                                END AS 'EnviarCarta'
                                                        FROM       cob.Endereco (nolock) e
                                                        INNER JOIN cob.Financiado (nolock) f
                                                        ON         f.id_financiado = e.id_financiado
                                                        WHERE      e.id_financiado = {IdFinanciado} ";
    public string SqlTelefones(int IdFinanciado, string numeroContrato) => $@"SELECT f.Id_Telefone,
	                                                        f.Ddd,
	                                                        f.Fone,
	                                                        CAST(CASE
		                                                        WHEN tb.IsBlackList IS NOT NULL AND tb.IsBlackList = 1 THEN -1
		                                                        ELSE f.Status
	                                                        END AS INT) AS 'Status',
	                                                        f.Id_Tipo_Telefone,
	                                                        f.Ramal,
	                                                        f.Descricao,
	                                                        f.Contato,
	                                                        f.Referencia_Telefone,
	                                                        f.Id_Operadora,
	                                                        f.DtInclusao,
	                                                        f.Dt_Ultima_Ligacao,
	                                                        f.IsHotNumber,
	                                                        f.IsFaceBook,
	                                                        f.IsWhatsApp,
                                                           o.Codigo,
                                                           o.Descricao  AS 'Operadora',
                                                           tt.Descricao AS 'Tipo_Telefone'
                                                        FROM cob.Telefone (nolock) f 
                                                           INNER JOIN cob.Contrato (nolock) c ON c.Id_Financiado = f.Id_Financiado
                                                           LEFT JOIN par.Operadora (nolock) o ON o.Id_Operadora = f.Id_Operadora
                                                           LEFT JOIN par.Tipo_TelefoneEmail (nolock) tt ON tt.Id_Tipo_TelefoneEmail = f.Id_Tipo_Telefone
                                                           LEFT JOIN cob.Telefone_BlackList tb ON f.Id_Telefone = tb.Id_Telefone
                                                        WHERE  f.Id_Financiado = {IdFinanciado} and c.Numero_Contrato = '{numeroContrato}'
                                                       union all
                                                       SELECT f.Id_Telefone,
	                                                        f.Ddd,
	                                                        f.Fone,
	                                                        CAST(CASE
		                                                        WHEN tb.IsBlackList IS NOT NULL AND tb.IsBlackList = 1 THEN -1
		                                                        ELSE f.Status
	                                                        END AS INT) AS 'Status',
	                                                        f.Id_Tipo_Telefone,
	                                                        f.Ramal,
	                                                        f.Descricao,
	                                                        f.Contato,
	                                                        f.Referencia_Telefone,
	                                                        f.Id_Operadora,
	                                                        f.DtInclusao,
	                                                        f.Dt_Ultima_Ligacao,
	                                                        f.IsHotNumber,
	                                                        f.IsFaceBook,
	                                                        f.IsWhatsApp,
                                                           o.Codigo,
                                                           o.Descricao  AS 'Operadora',
                                                           tt.Descricao AS 'Tipo_Telefone'
                                                        FROM cob.Telefone (nolock) f 
                                                           INNER JOIN cob.Contrato (nolock) c ON c.Id_Financiado = f.Id_Financiado
                                                           INNER join cob.Financiado (nolock) fin on fin.Id_Financiado = f.Id_Financiado
                                                           LEFT JOIN par.Operadora (nolock) o ON o.Id_Operadora = f.Id_Operadora
                                                           LEFT JOIN par.Tipo_TelefoneEmail (nolock) tt ON tt.Id_Tipo_TelefoneEmail = f.Id_Tipo_Telefone
                                                           LEFT JOIN cob.Telefone_BlackList tb ON f.Id_Telefone = tb.Id_Telefone
                                                        WHERE ( f.Id_Financiado != {IdFinanciado} and c.Numero_Contrato = '{numeroContrato}') 
                                                            and fin.Cpfcnpj = (select top 1 CpfCnpj from cob.Financiado f1 where f1.Id_Financiado = {IdFinanciado})
                                                        ";
    public string SqlEmails(int IdFinanciado, string numeroContrato) => $@"SELECT e.*,
                                                            CASE
                                                                    WHEN e.id_Email = f.Id_Email_Carta THEN 1
                                                                    ELSE 0
                                                            END AS 'EnviarCarta'
                                                    FROM cob.Email e (nolock)
                                                    INNER JOIN cob.Financiado (nolock) f ON f.id_financiado = e.id_financiado
                                                    INNER JOIN cob.Contrato (nolock) c ON c.Id_Financiado = e.id_financiado
                                                    WHERE e.Id_Financiado = {IdFinanciado} and c.Numero_Contrato = '{numeroContrato}'
                                                    UNION ALL
                                                    SELECT e.*,
                                                            CASE
                                                                    WHEN e.id_Email = f.Id_Email_Carta THEN 1
                                                                    ELSE 0
                                                            END AS 'EnviarCarta'
                                                    FROM cob.Email e (nolock)
                                                    INNER JOIN cob.Financiado (nolock) f ON f.id_financiado = e.id_financiado
                                                    INNER JOIN cob.Contrato (nolock) c ON c.Id_Financiado = e.id_financiado
                                                    WHERE ( e.Id_Financiado != {IdFinanciado} and c.Numero_Contrato = '{numeroContrato}')
                                                        and f.Cpfcnpj = (select top 1 CpfCnpj from cob.Financiado f1 where f1.Id_Financiado = {IdFinanciado})";

    public string SqlGrupo() => @"SELECT c.Id_Grupo,
                                        c.Descricao
                                    FROM   par.Grupo (nolock) AS c 
                                order by c.Descricao ";
    public string SqlCliente() => @"SELECT c.Id_Cliente,
                                        c.Razao,
                                        c.Nome_Res,
                                        c.Cnpj
                                    FROM   par.Cliente (nolock) AS c
                                    WHERE  c.Ativo = 1 ";
    public string SqlStatusContrato() => @"SELECT sc.* FROM par.Status_Contrato (nolock) AS sc ";
    public string SqlUsuario() => @"SELECT u.Id_Usuario            AS 'Id_Usuario',
                                        u.Login                 AS 'Login',
                                        u.Nome                  AS 'Nome',
                                        u.Email                 AS 'Email',
                                        u.Ativo                 AS 'Ativo',
                                        gu.Id_Grupo_Usuario     AS 'IdGrupo',
                                        gu.Id_Grupo_Usuario_Pai AS 'IdGrupoPai',
                                        gu.Grupo                AS 'Grupo'
                                    FROM   usu.Usuario (nolock) u
                                        INNER JOIN usu.Grupo_Usuario (nolock) gu
                                                ON gu.Id_Grupo_Usuario = u.Id_Grupo_Usuario
                                    WHERE  u.Ativo = 1 ";
    public string SqlUsuarioLogin() => @"SELECT u.Id_Usuario            AS 'Id_Usuario',
                                        u.Login                 AS 'Login',
                                        u.Nome                  AS 'Nome',
                                        u.Email                 AS 'Email',
                                        u.Ativo                 AS 'Ativo',
                                        gu.Id_Grupo_Usuario     AS 'IdGrupo',
                                        gu.Id_Grupo_Usuario_Pai AS 'IdGrupoPai',
                                        gu.Grupo                AS 'Grupo'
                                    FROM   usu.Usuario (nolock) u
                                        INNER JOIN usu.Grupo_Usuario (nolock) gu
                                                ON gu.Id_Grupo_Usuario = u.Id_Grupo_Usuario
                                    WHERE  u.Login = @username ";

    public string SqlBuscaDadosFinanciados(int? aberto, bool isRodobens = false)
    {
        string sql = $@"SELECT TOP(1000) f.Id_Financiado,
                                                        c.Id_Contrato,
                                                        c.Id_Agrupamento,
                                                        c.Numero_Contrato,
                                                        c.Contrato_Aberto,
                                                        c.Vl_contr,
                                                        f.Nome,
                                                        f.Cpfcnpj,
                                                        f.Dt_Nascimento,
                                                        f.Data_Enriquecimento,
                                                        f.Sexo,
                                                        f.Est_Civil,
                                                        f.Score_Serasa,
                                                        f.Tipo_Financiado,
                                                        f.Rg,
                                                        f.Dt_Emiss_Rg,
                                                        f.Orgao_Emiss_Rg,
                                                        f.Uf_Emiss_Rg,
                                                        f.Tipo_Pessoa,
                                                        f.Conjugue,
                                                        f.Mae,
                                                        f.Pai,
                                                        cli.Id_Cliente,
                                                        cli.Id_Grupo,
                                                        cli.Nome_Res AS 'Cliente',
                                                        g.Descricao  AS 'Grupo',
                                                        fa.Id_Fase,
                                                        fa.Cod_Fase,
                                                        fa.Descricao AS 'Fase',
                                                        fa.Cor,
                                                        sc.Descricao AS 'Status'
                                                    FROM   cob.Financiado (nolock) AS f
                                                        LEFT JOIN cob.Contrato (nolock) c
                                                                ON f.Id_Financiado = c.Id_Financiado
                                                        INNER JOIN par.Cliente (nolock) cli
                                                                ON cli.Id_Cliente = c.Id_Cliente
                                                        INNER JOIN par.Grupo (nolock) g
                                                                ON g.Id_Grupo = cli.Id_Grupo
                                                        INNER JOIN par.Fase (nolock) fa
                                                                ON fa.Id_Fase = c.Id_Fase
                                                        LEFT JOIN cob.Distribuicao d 
                                                            ON d.Id_Agrupamento = c.Id_Agrupamento
                                                        LEFT JOIN par.Status_Contrato sc 
                                                            ON sc.Id_Status_Contrato = d.Id_Status_Contrato
                                                    WHERE  cli.Ativo = 1";
        if (aberto == 1)
        {
            sql += $" AND c.Contrato_Aberto = {aberto} ";
        }

        if (isRodobens)
            sql += " AND c.Id_Cliente_Web = 53 ";
        return sql;
    }

    public string SqlBuscaDadosFinanciadosGroupBy() => $@" GROUP  BY f.Id_Financiado, c.Id_Contrato, c.Id_Agrupamento, c.Numero_Contrato, c.Contrato_Aberto, f.Nome, f.Cpfcnpj, f.Dt_Nascimento, f.Data_Enriquecimento, f.Sexo, f.Est_Civil, f.Score_Serasa, f.Tipo_Financiado, f.Rg, f.Dt_Emiss_Rg, f.Orgao_Emiss_Rg, f.Uf_Emiss_Rg, f.Tipo_Pessoa, f.Conjugue, f.Mae, f.Pai, cli.Id_Cliente, cli.Id_Grupo, cli.Nome_Res, g.Descricao,fa.Id_fase, fa.Cod_Fase, fa.Descricao, fa.Cor, sc.Descricao, c.Vl_contr ORDER  BY c.Id_Agrupamento DESC  ";

    public string SqlNegociacoesCalculo(int IdNegociacao) => $@"SELECT np.*,
        np.Vl_Saldo_Original                            AS 'Real_Original',
        np.Vl_Principal                                 AS 'Real_Negociacao',
        np.Vl_Multa                          AS 'Multa',
        np.Vl_Multa_Original                          AS 'Multa_Original_Desconto',
        np.Percentual_Desconto_Principal                AS 'Percentual_Real_Atualizado_Negociacao',
        np.Percentual_Desconto                          AS 'Percentual_Multa_Negociacao',
        ( np.Vl_Saldo_Original + np.Vl_Multa_Original ) AS 'Subtotal_Original_Desconto',
        np.Vl_Principal_Original                                 AS 'Subtotal_Negociacao',
        np.Vl_Honorario_Original                        AS 'Honorarios_Original_Desconto',
        ( np.Vl_Saldo_Original + np.Vl_Multa_Original + np.Vl_Honorario_Original )                  AS 'Total_Original_Desconto',
        np.Vl_Custas                                    AS 'Custas_Negociacao',
        np.Vl_Honorario                                 AS 'Honorarios_Negociacao',
        np.Vl_Total                                     AS 'Total_Negociacao',
        np.Percentual_Honorario                         AS 'Percentual_Honorarios_Negociacao',
        0.0                                             AS 'Percentual_Total_Negociacao',
        p.Nr_parcela                                    AS 'Primeira_Parcela',
        0                                               AS 'Ultima_Parcela'
    FROM   cob.Negociacao_Parcela (nolock) np
        INNER JOIN cob.Parcela (nolock) p
                ON p.Id_Parcela = np.Id_Parcela
    WHERE  np.Id_Negociacao = {IdNegociacao} ";
    public string SqlNegociacoesCalculoParametro(int parametro, int faixa) => $@"SELECT fc.* FROM Par.Faixa_Calculo fc
INNER JOIN Par.Parametro_Calculo_Pc pc ON pc.Id_Parametro_Calculo_Pc = fc.Id_Parametro_Calculo_Pc
WHERE pc.Id_Parametro_Calculo_Pc = {parametro} AND fc.Id_Faixa_Calculo = {faixa} ";

    public string SqlNegociacaoAcordos(int IdAgrupamento, GvcRodobens crm) => $@"SELECT Cast('{crm}' as varchar(20))  Crm, a.*
                                                                FROM   cob.Acordo (nolock) a
                                                                INNER JOIN cob.Negociacao (nolock) n
                                                                        ON a.Id_Negociacao = n.Id_Negociacao OR a.Id_Negociacao = n.Id_Negociacao
                                                                LEFT JOIN cob.Parcela_Acordo (nolock) pa
                                                                        ON a.Id_Primeira_Parcela_Acordo = pa.Id_Parcela_Acordo
                                                                WHERE  n.Id_Agrupamento IN ( {IdAgrupamento} )";

    public string SqlNegociacaoAcordosContrato(string numeroContrato, int? idAgrupamento = 0) => $@"SELECT Cast('{GvcRodobens.Rodobens}' as varchar(20)) Crm, a.*
                                                                FROM   cob.Acordo (nolock) a
                                                                INNER JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = a.Id_Agrupamento
                                                                INNER JOIN cob.Negociacao (nolock) n
                                                                        ON a.Id_Negociacao = n.Id_Negociacao OR a.Id_Negociacao = n.Id_Negociacao
                                                                LEFT JOIN cob.Parcela_Acordo (nolock) pa
                                                                        ON a.Id_Primeira_Parcela_Acordo = pa.Id_Parcela_Acordo
                                                                WHERE c.Numero_Contrato = '{numeroContrato}'" + (idAgrupamento != 0 ? $" and n.Id_Agrupamento = {idAgrupamento}" : "");

    public string SqlParcelasAcordo(int IdAcordo, GvcRodobens crm) => @$"SELECT Cast('{crm}' as varchar(20)) as Crm, pa.*,
                                                            pa.Vl_Saldo                  AS 'Vl_Atualizado',
                                                            ( pa.Vl_Parcela - Vl_Saldo ) AS 'Vl_Pago',
                                                            0                            AS 'Atraso'
                                                        FROM   cob.Parcela_Acordo (nolock) pa
                                                        WHERE  pa.Id_Acordo IN ( {IdAcordo} ) ";

    public string SqlParcelasAcordoContrato(string numeroContrato, int idAcordo) => @$"SELECT Cast('{GvcRodobens.Rodobens}' as varchar(20)) Crm, pa.*,
                                                            pa.Vl_Saldo                  AS 'Vl_Atualizado',
                                                            ( pa.Vl_Parcela - Vl_Saldo ) AS 'Vl_Pago',
                                                            0                            AS 'Atraso'
                                                        FROM cob.Parcela_Acordo (nolock) pa
                                                        INNER JOIN cob.Acordo (nolock) a ON a.Id_Acordo = pa.Id_Acordo
                                                        INNER JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = a.Id_Agrupamento
                                                        WHERE  c.Numero_Contrato = '{numeroContrato}' and a.Id_Acordo = {idAcordo}";
    public string SqlParcelasAcordoBoleto(int IdParcelaAcordo) => @$"SELECT b.*,
                                                                            bc.Banco,
                                                                            c.Conta,
                                                                            m.Descricao AS 'Moeda',
                                                                            f.Descricao AS 'Fase'
                                                                    FROM   cob.Parcela_Acordo (nolock) pa
                                                                        INNER JOIN cob.Boleto (nolock) b
                                                                                ON b.Id_Boleto = pa.Id_boleto
                                                                        INNER JOIN par.Conta (nolock) c
                                                                                ON b.Id_Conta = c.Id_Conta
                                                                        INNER JOIN par.Banco (nolock) bc
                                                                                ON b.Id_Banco = bc.Id_Banco
                                                                        INNER JOIN par.Moeda (nolock) m
                                                                                ON b.Id_Moeda = m.Id_Moeda
                                                                        INNER JOIN par.Fase (nolock) f
                                                                                ON b.Id_Fase = f.Id_Fase   
                                                                    WHERE  pa.Id_Parcela_Acordo = {IdParcelaAcordo} ";

    public string SqlParcelasAcordoBoletoContrato(string numeroContrato, int IdParcelaAcordo) => @$"SELECT b.*,
                                                                            bc.Banco,
                                                                            c.Conta,
                                                                            m.Descricao AS 'Moeda',
                                                                            f.Descricao AS 'Fase'
                                                                    FROM cob.Parcela_Acordo (nolock) pa
                                                                        INNER JOIN cob.Boleto (nolock) b ON b.Id_Boleto = pa.Id_boleto
                                                                        INNER JOIN par.Conta (nolock) c ON b.Id_Conta = c.Id_Conta
                                                                        INNER JOIN par.Banco (nolock) bc ON b.Id_Banco = bc.Id_Banco
                                                                        INNER JOIN par.Moeda (nolock) m ON b.Id_Moeda = m.Id_Moeda
                                                                        INNER JOIN par.Fase (nolock) f ON b.Id_Fase = f.Id_Fase   
                                                                        INNER JOIN cob.Acordo (nolock) a ON a.Id_Acordo = pa.Id_Acordo
                                                                        INNER JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = a.Id_Agrupamento
                                                                    WHERE c.Numero_Contrato = '{numeroContrato}' and pa.Id_Parcela_Acordo = {IdParcelaAcordo}";
    public string SqlParcelasAcordoRecibo(int IdParcelaAcordo) => @$"SELECT r.*,
                                                                            b.Nr_Boleto,
                                                                            c.Razao,
                                                                            f.Descricao AS 'Fase'
                                                                        FROM   cob.Parcela_Acordo (nolock) pa
                                                                            INNER JOIN cob.Boleto (nolock) b
                                                                                    ON b.Id_Boleto = pa.Id_boleto
                                                                            INNER JOIN cob.Recibo (nolock) r
                                                                                    ON r.Id_Boleto = b.Id_Boleto
                                                                            INNER JOIN par.Cliente (nolock) c
                                                                                    ON c.Id_Cliente = r.Id_Cliente
                                                                            INNER JOIN par.Fase (nolock) f
                                                                                    ON f.Id_Fase = r.Id_Fase
                                                                        WHERE  pa.Id_Parcela_Acordo = {IdParcelaAcordo} ";

    public string SqlParcelasAcordoReciboContrato(string numeroContrato, int IdParcelaAcordo) => @$"SELECT r.*,
                                                                            b.Nr_Boleto,
                                                                            c.Razao,
                                                                            f.Descricao AS 'Fase'
                                                                        FROM cob.Parcela_Acordo (nolock) pa
                                                                            INNER JOIN cob.Boleto (nolock) b ON b.Id_Boleto = pa.Id_boleto
                                                                            INNER JOIN cob.Recibo (nolock) r ON r.Id_Boleto = b.Id_Boleto
                                                                            INNER JOIN par.Cliente (nolock) c ON c.Id_Cliente = r.Id_Cliente
                                                                            INNER JOIN par.Fase (nolock) f ON f.Id_Fase = r.Id_Fase
                                                                            INNER JOIN cob.Acordo (nolock) a ON a.Id_Acordo = pa.Id_Acordo
                                                                            INNER JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = a.Id_Agrupamento
                                                                        WHERE c.Numero_Contrato = '{numeroContrato}' and pa.Id_Parcela_Acordo = {IdParcelaAcordo}";
    public string SqlParcelasAcordoNegociacao(int IdParcelaAcordo) => @$"SELECT n.*
                                                                            FROM   cob.Parcela_Acordo (nolock) pa
                                                                                INNER JOIN cob.Boleto (nolock) b
                                                                                        ON b.Id_Boleto = pa.Id_boleto
                                                                                INNER JOIN cob.Negociacao (nolock) n
                                                                                        ON n.Id_Negociacao = b.Id_Negociacao
                                                                            WHERE  pa.Id_Parcela_Acordo = {IdParcelaAcordo} ";
    public string SqlParcelasAcordoNegociacaoContrato(string numeroContrato, int IdParcelaAcordo) => @$"SELECT n.*
                                                                            FROM cob.Parcela_Acordo (nolock) pa
                                                                                INNER JOIN cob.Boleto (nolock) b ON b.Id_Boleto = pa.Id_boleto
                                                                                INNER JOIN cob.Negociacao (nolock) n ON n.Id_Negociacao = b.Id_Negociacao
                                                                                INNER JOIN cob.Acordo (nolock) a ON a.Id_Acordo = pa.Id_Acordo
                                                                                INNER JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = a.Id_Agrupamento
                                                                            WHERE c.Numero_Contrato = '{numeroContrato}' and pa.Id_Parcela_Acordo = {IdParcelaAcordo}";

    public string SqlNegociacoesDetalhes(int IdNegociacao) => $@"SELECT n.* FROM cob.Negociacao (nolock) n
                                                                WHERE n.Id_Negociacao = {IdNegociacao}";
    public string SqlCustas(int IdContrato) => $@"SELECT 
                                                        c.Id_Contrato,
                                                        cu.Id_Custas,c.Numero_Contrato,
                                                        cu.Tipo_Comprov,
                                                        cu.Nr_Comprov,
                                                        cu.Dt_Despesa,
                                                        cu.Vl_Despesa,
                                                        cu.Dt_Inc,
                                                        cu.Vl_saldo,
                                                        cu.Nr_Registro_Cartorio,
                                                        cu.Dt_Dev,
                                                        cu.Forma_Dev,
                                                        cu.Motivo_Dev,
                                                        cu.Dt_Reat,
                                                        cu.Forma_Reat,
                                                        cu.Id_Parcela,
                                                        cu.Motivo,
                                                        cu.Codigo_Rastreio_Correio,
                                                        cu.Impugnado,
                                                        cu.Dt_Retorno_Notificacao,
                                                        cu.Notificacao_Positiva,
                                                        cu.Nr_Notificacao,
                                                        ds.Cod_Despesa,
                                                        ds.Descricao_Despesa_Sistema,
                                                        dc.Cod_Despesa_Cliente,
                                                        f.Descricao AS 'Filial',
                                                        cu.Cobrar_Financiado_Financeira,
                                                        cu.Cobrado_Cliente,
	                                                md.Descricao AS 'Devolucao_Motivo'
                                                FROM   cob.Custas (nolock) cu
                                                INNER JOIN cob.Contrato (nolock) c
                                                        ON c.Id_Contrato = cu.Id_Contrato
                                                LEFT JOIN par.Despesa_Sistema (nolock) ds 
                                                        ON ds.Id_Despesa_Sistema = cu.Id_Despesa_Sistema
                                                LEFT JOIN par.Despesa_Cliente (nolock) dc 
                                                        ON dc.Id_Despesa_Cliente = cu.Id_Despesa_Cliente
                                                LEFT JOIN usu.Filial (nolock) f 
                                                        ON f.Id_Filial = cu.Id_Filial
                                                LEFT JOIN par.Motivo_Devolucao (nolock) md 
                                                        ON md.Id_Motivo_Devolucao = cu.Motivo_Dev
                                                WHERE  c.Id_Agrupamento = {IdContrato} ";

    public string SqlCustasContrato(int IdContrato) => $@"SELECT 
                                                        c.Id_Contrato,
                                                        cu.Id_Custas,c.Numero_Contrato,
                                                        cu.Tipo_Comprov,
                                                        cu.Nr_Comprov,
                                                        cu.Dt_Despesa,
                                                        cu.Vl_Despesa,
                                                        cu.Dt_Inc,
                                                        cu.Vl_saldo,
                                                        cu.Nr_Registro_Cartorio,
                                                        cu.Dt_Dev,
                                                        cu.Forma_Dev,
                                                        cu.Motivo_Dev,
                                                        cu.Dt_Reat,
                                                        cu.Forma_Reat,
                                                        cu.Id_Parcela,
                                                        cu.Motivo,
                                                        cu.Codigo_Rastreio_Correio,
                                                        cu.Impugnado,
                                                        cu.Dt_Retorno_Notificacao,
                                                        cu.Notificacao_Positiva,
                                                        cu.Nr_Notificacao,
                                                        ds.Cod_Despesa,
                                                        ds.Descricao_Despesa_Sistema,
                                                        dc.Cod_Despesa_Cliente,
                                                        f.Descricao AS 'Filial',
                                                        cu.Cobrar_Financiado_Financeira,
                                                        cu.Cobrado_Cliente,
	                                                md.Descricao AS 'Devolucao_Motivo'
                                                FROM   cob.Custas (nolock) cu
                                                INNER JOIN cob.Contrato (nolock) c
                                                        ON c.Id_Contrato = cu.Id_Contrato
                                                LEFT JOIN par.Despesa_Sistema (nolock) ds 
                                                        ON ds.Id_Despesa_Sistema = cu.Id_Despesa_Sistema
                                                LEFT JOIN par.Despesa_Cliente (nolock) dc 
                                                        ON dc.Id_Despesa_Cliente = cu.Id_Despesa_Cliente
                                                LEFT JOIN usu.Filial (nolock) f 
                                                        ON f.Id_Filial = cu.Id_Filial
                                                LEFT JOIN par.Motivo_Devolucao (nolock) md 
                                                        ON md.Id_Motivo_Devolucao = cu.Motivo_Dev
                                                WHERE  c.Id_Contrato = {IdContrato} ";
    public string SqlHistoricoAtendimento(int Id_Agrupamento) => $@"SELECT
                                                                        h.Id_Historico,
                                                                        h.Id_Cliente,
                                                                        h.Id_Financiado,
                                                                        h.Id_Agrupamento,
                                                                        h.Id_Contrato,
                                                                        h.Dt_Ocorr,
                                                                        h.Id_Ocorrencia_Cliente,
                                                                        h.Id_Ocorrencia_Sistema,
                                                                        h.Complemento,
                                                                        h.Observacao,
                                                                        h.Id_Usuario_Alterou,
                                                                        h.Id_Usuario_Inseriu,
                                                                        os.Cod_Ocorr_Sistema,
                                                                        os.Descricao,
                                                                        os.Descricao_Complemento,
                                                                        u.Id_Usuario,
                                                                        U.Nome,
                                                                        u.Login,
                                                                        c.Numero_Contrato,
                                                                        CASE
                                                                        WHEN tel.Ddi IS NULL
                                                                                OR tel.Ddi = '' THEN
                                                                        CASE
                                                                                WHEN tel.Ddd IS NOT NULL THEN '+55'
                                                                                ELSE ''
                                                                        END
                                                                        ELSE '+' + CONVERT(VARCHAR, tel.Ddi)
                                                                        END + ' ' + '(' + Replace(tel.Ddd, ' ', '') + ')' + ' ' +
                                                                                CASE
                                                                                WHEN Len(Replace(tel.Fone, ' ', '')) > 8 THEN
                                                                                Substring(Replace(tel.Fone, ' ', ''), 1, 5)
                                                                                + '-'
                                                                                + Substring(Replace(tel.Fone, ' ', ''), 6, 9)
                                                                                ELSE Substring(Replace(tel.Fone, ' ', ''), 1, 4)
                                                                                        + '-'
                                                                                        + Substring(Replace(tel.Fone, ' ', ''), 5, 8)
                                                                                END        AS 'DddFone',
                                                                        CASE
                                                                        WHEN telh.Ddi IS NULL
                                                                                OR telh.Ddi = '' THEN
                                                                        CASE
                                                                                WHEN telh.Ddd IS NOT NULL THEN '+55'
                                                                                ELSE ''
                                                                        END
                                                                        ELSE '+' + CONVERT(VARCHAR, telh.Ddi)
                                                                        END + ' ' + '(' + Replace(telh.Ddd, ' ', '') + ')' + ' ' +
                                                                                CASE
                                                                                WHEN Len(Replace(telh.Fone, ' ', '')) > 8 THEN
                                                                                Substring(Replace(telh.Fone, ' ', ''), 1, 5)
                                                                                + '-'
                                                                                + Substring(Replace(telh.Fone, ' ', ''), 6, 9)
                                                                                ELSE Substring(Replace(telh.Fone, ' ', ''), 1, 4)
                                                                                        + '-'
                                                                                        + Substring(Replace(telh.Fone, ' ', ''), 5, 8)
                                                                                END        AS 'DddFoneRetorno',
                                                                        '' AS 'Ligacao'
                                                                    FROM   cob.Historico (nolock) h
                                                                    INNER JOIN par.Ocorrencia_Sistema (nolock) os
                                                                            ON os.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
                                                                    INNER JOIN par.Cliente (nolock) cl
                                                                            ON cl.Id_Cliente = h.Id_Cliente
                                                                    INNER JOIN cob.Financiado (nolock) f
                                                                            ON f.Id_Financiado = h.Id_Financiado
                                                                    INNER JOIN cob.Contrato (nolock) c
                                                                            ON c.Id_Contrato = h.Id_Contrato
                                                                    INNER JOIN usu.Usuario (nolock) u
                                                                            ON u.Id_Usuario = h.Id_Usuario_Inseriu
                                                                    LEFT JOIN cob.Rel_Historico_Telefone (nolock) rlt
                                                                            ON rlt.Id_Historico = h.Id_Historico
                                                                    LEFT JOIN cob.Telefone (nolock) tel
                                                                            ON rlt.Id_Telefone = tel.Id_Telefone
                                                                    LEFT JOIN cob.Telefone (nolock) telh
                                                                            ON h.Id_Telefone_Retorno = telh.Id_Telefone
                                                                    WHERE  c.Id_Agrupamento = {Id_Agrupamento}
                                                                    ORDER BY h.Dt_Ocorr DESC";

    public string SqlHistoricoAtendimentoContrato(string numeroContrato) => $@"SELECT
                                                                        h.Id_Historico,
                                                                        h.Id_Cliente,
                                                                        h.Id_Financiado,
                                                                        h.Id_Agrupamento,
                                                                        h.Id_Contrato,
                                                                        h.Dt_Ocorr,
                                                                        h.Id_Ocorrencia_Cliente,
                                                                        h.Id_Ocorrencia_Sistema,
                                                                        h.Complemento,
                                                                        h.Observacao,
                                                                        h.Id_Usuario_Alterou,
                                                                        h.Id_Usuario_Inseriu,
                                                                        os.Cod_Ocorr_Sistema,
                                                                        os.Descricao,
                                                                        os.Descricao_Complemento,
                                                                        u.Id_Usuario,
                                                                        U.Nome,
                                                                        u.Login,
                                                                        c.Numero_Contrato,
                                                                        CASE
                                                                        WHEN tel.Ddi IS NULL
                                                                                OR tel.Ddi = '' THEN
                                                                        CASE
                                                                                WHEN tel.Ddd IS NOT NULL THEN '+55'
                                                                                ELSE ''
                                                                        END
                                                                        ELSE '+' + CONVERT(VARCHAR, tel.Ddi)
                                                                        END + ' ' + '(' + Replace(tel.Ddd, ' ', '') + ')' + ' ' +
                                                                                CASE
                                                                                WHEN Len(Replace(tel.Fone, ' ', '')) > 8 THEN
                                                                                Substring(Replace(tel.Fone, ' ', ''), 1, 5)
                                                                                + '-'
                                                                                + Substring(Replace(tel.Fone, ' ', ''), 6, 9)
                                                                                ELSE Substring(Replace(tel.Fone, ' ', ''), 1, 4)
                                                                                        + '-'
                                                                                        + Substring(Replace(tel.Fone, ' ', ''), 5, 8)
                                                                                END        AS 'DddFone',
                                                                        CASE
                                                                        WHEN telh.Ddi IS NULL
                                                                                OR telh.Ddi = '' THEN
                                                                        CASE
                                                                                WHEN telh.Ddd IS NOT NULL THEN '+55'
                                                                                ELSE ''
                                                                        END
                                                                        ELSE '+' + CONVERT(VARCHAR, telh.Ddi)
                                                                        END + ' ' + '(' + Replace(telh.Ddd, ' ', '') + ')' + ' ' +
                                                                                CASE
                                                                                WHEN Len(Replace(telh.Fone, ' ', '')) > 8 THEN
                                                                                Substring(Replace(telh.Fone, ' ', ''), 1, 5)
                                                                                + '-'
                                                                                + Substring(Replace(telh.Fone, ' ', ''), 6, 9)
                                                                                ELSE Substring(Replace(telh.Fone, ' ', ''), 1, 4)
                                                                                        + '-'
                                                                                        + Substring(Replace(telh.Fone, ' ', ''), 5, 8)
                                                                                END        AS 'DddFoneRetorno',
                                                                        '' AS 'Ligacao'
                                                                    FROM   cob.Historico (nolock) h
                                                                    INNER JOIN par.Ocorrencia_Sistema (nolock) os
                                                                            ON os.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
                                                                    INNER JOIN par.Cliente (nolock) cl
                                                                            ON cl.Id_Cliente = h.Id_Cliente
                                                                    INNER JOIN cob.Financiado (nolock) f
                                                                            ON f.Id_Financiado = h.Id_Financiado
                                                                    INNER JOIN cob.Contrato (nolock) c
                                                                            ON c.Id_Contrato = h.Id_Contrato
                                                                    INNER JOIN usu.Usuario (nolock) u
                                                                            ON u.Id_Usuario = h.Id_Usuario_Inseriu
                                                                    LEFT JOIN cob.Rel_Historico_Telefone (nolock) rlt
                                                                            ON rlt.Id_Historico = h.Id_Historico
                                                                    LEFT JOIN cob.Telefone (nolock) tel
                                                                            ON rlt.Id_Telefone = tel.Id_Telefone
                                                                    LEFT JOIN cob.Telefone (nolock) telh
                                                                            ON h.Id_Telefone_Retorno = telh.Id_Telefone
                                                                    WHERE  c.Numero_Contrato = '{numeroContrato}'
                                                                    ORDER BY h.Dt_Ocorr DESC";

    public string SqlNegociacaoParcelaDetalhes(int IdParcela, int IdNegociacao) => $@"SELECT np.*,
                                                                                            np.Vl_Saldo_Original                            AS 'Real_Original',
                                                                                            np.Vl_Principal                                 AS 'Real_Negociacao',
                                                                                            np.Vl_Multa_Original                            AS
                                                                                            'Multa_Original_Desconto',
                                                                                            np.Percentual_Desconto_Principal                AS
                                                                                            'Percentual_Real_Atualizado_Negociacao',
                                                                                            np.Percentual_Desconto                          AS
                                                                                            'Percentual_Multa_Negociacao',
                                                                                            ( np.Vl_Saldo_Original + np.Vl_Multa_Original ) AS
                                                                                            'Subtotal_Original_Desconto',
                                                                                            np.Vl_Principal                                 AS 'Subtotal_Negociacao',
                                                                                            np.Vl_Honorario_Original                        AS
                                                                                            'Honorarios_Original_Desconto',
                                                                                            ( np.Vl_Saldo_Original + np.Vl_Multa_Original
                                                                                                + np.Vl_Honorario_Original )                  AS
                                                                                            'Total_Original_Desconto',
                                                                                            np.Vl_Custas                                    AS 'Custas_Negociacao',
                                                                                            np.Vl_Honorario                                 AS
                                                                                            'Honorarios_Negociacao',
                                                                                            np.Vl_Total                                     AS 'Total_Negociacao',
                                                                                            np.Percentual_Honorario                         AS 'Percentual_Honorarios_Negociacao',
                                                                                            0.0                                               AS 'Percentual_Total_Negociacao'
                                                                                        FROM   cob.Negociacao_Parcela (nolock) np
                                                                                            INNER JOIN cob.Parcela (nolock) p
                                                                                                    ON p.Id_Parcela = np.Id_Parcela
                                                                                        WHERE  p.Id_Parcela = {IdParcela}
                                                                                            AND np.Id_Negociacao = {IdNegociacao} ";

    public string SqlOcorrencias(string username) => @$"SELECT
                                          os.Id_Ocorrencia_Sistema,
                                          os.Cod_Ocorr_Sistema,
                                          os.Descricao    
                                        FROM par.Ocorrencia_Sistema os (nolock)
                                        INNER JOIN par.Ocorrencia_Sistema_Grupo_Usuario osgu ON os.Id_Ocorrencia_Sistema = osgu.Id_Ocorrencia_Sistema
                                        INNER JOIN usu.Usuario u ON u.Id_Grupo_Usuario = osgu.Id_Grupo_Usuario
                                        WHERE u.Login = '{username}'";

    public string SqlHistoricoResumo(int Id_Agrupamento) => $@"SELECT
                                                h.Id_Historico,
                                                h.Id_Cliente,
                                                h.Id_Financiado,
                                                h.Id_Agrupamento,
                                                h.Id_Contrato,
                                                h.Dt_Ocorr,
                                                h.Complemento,
                                                h.Observacao,
                                                os.Cod_Ocorr_Sistema,
                                                os.Descricao,
                                                U.Nome,
                                                CASE
                                                WHEN tel.Ddi IS NULL
                                                        OR tel.Ddi = '' THEN
                                                CASE
                                                        WHEN tel.Ddd IS NOT NULL THEN '+55'
                                                        ELSE ''
                                                END
                                                ELSE '+' + CONVERT(VARCHAR, tel.Ddi)
                                                END + ' ' + '(' + Replace(tel.Ddd, ' ', '') + ')' + ' ' +
                                                        CASE
                                                        WHEN Len(Replace(tel.Fone, ' ', '')) > 8 THEN
                                                        Substring(Replace(tel.Fone, ' ', ''), 1, 5)
                                                        + '-'
                                                        + Substring(Replace(tel.Fone, ' ', ''), 6, 9)
                                                        ELSE Substring(Replace(tel.Fone, ' ', ''), 1, 4)
                                                                + '-'
                                                                + Substring(Replace(tel.Fone, ' ', ''), 5, 8)
                                                        END        AS 'DddFone'
                                            FROM   cob.Historico (nolock) h
                                            INNER JOIN par.Ocorrencia_Sistema (nolock) os
                                                ON os.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
                                            INNER JOIN par.Cliente (nolock) cl
                                                ON cl.Id_Cliente = h.Id_Cliente
                                            INNER JOIN cob.Financiado (nolock) f
                                                ON f.Id_Financiado = h.Id_Financiado
                                            INNER JOIN cob.Contrato (nolock) c
                                                ON c.Id_Contrato = h.Id_Contrato
                                            INNER JOIN usu.Usuario (nolock) u
                                                    ON u.Id_Usuario = h.Id_Usuario_Inseriu
                                            LEFT JOIN cob.Rel_Historico_Telefone (nolock) rlt
                                                    ON rlt.Id_Historico = h.Id_Historico
                                            LEFT JOIN cob.Telefone (nolock) tel
                                                    ON rlt.Id_Telefone = tel.Id_Telefone
                                            WHERE  h.Id_Agrupamento = {Id_Agrupamento}
                                            ORDER BY h.Dt_Ocorr DESC";

    public string SqlHistoricoResumoContrato(int Id_Agrupamento, string numeroContrato) => $@"SELECT
                                                h.Id_Historico,
                                                h.Id_Cliente,
                                                h.Id_Financiado,
                                                h.Id_Agrupamento,
                                                h.Id_Contrato,
                                                h.Dt_Ocorr,
                                                h.Complemento,
                                                h.Observacao,
                                                os.Cod_Ocorr_Sistema,
                                                os.Descricao,
                                                U.Nome,
                                                CASE
                                                WHEN tel.Ddi IS NULL
                                                        OR tel.Ddi = '' THEN
                                                CASE
                                                        WHEN tel.Ddd IS NOT NULL THEN '+55'
                                                        ELSE ''
                                                END
                                                ELSE '+' + CONVERT(VARCHAR, tel.Ddi)
                                                END + ' ' + '(' + Replace(tel.Ddd, ' ', '') + ')' + ' ' +
                                                        CASE
                                                        WHEN Len(Replace(tel.Fone, ' ', '')) > 8 THEN
                                                        Substring(Replace(tel.Fone, ' ', ''), 1, 5)
                                                        + '-'
                                                        + Substring(Replace(tel.Fone, ' ', ''), 6, 9)
                                                        ELSE Substring(Replace(tel.Fone, ' ', ''), 1, 4)
                                                                + '-'
                                                                + Substring(Replace(tel.Fone, ' ', ''), 5, 8)
                                                        END        AS 'DddFone'
                                            FROM cob.Historico (nolock) h
                                            INNER JOIN par.Ocorrencia_Sistema (nolock) os ON os.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
                                            INNER JOIN par.Cliente (nolock) cl ON cl.Id_Cliente = h.Id_Cliente
                                            INNER JOIN cob.Financiado (nolock) f ON f.Id_Financiado = h.Id_Financiado
                                            INNER JOIN cob.Contrato (nolock) c ON c.Id_Agrupamento = h.Id_Agrupamento
                                            INNER JOIN usu.Usuario (nolock) u ON u.Id_Usuario = h.Id_Usuario_Inseriu
                                            LEFT JOIN cob.Rel_Historico_Telefone (nolock) rlt ON rlt.Id_Historico = h.Id_Historico
                                            LEFT JOIN cob.Telefone (nolock) tel ON rlt.Id_Telefone = tel.Id_Telefone
                                            WHERE c.Numero_Contrato = '{numeroContrato}' and h.Id_Agrupamento != {Id_Agrupamento}
                                            ORDER BY h.Dt_Ocorr DESC";

    public string SqlParametroCalculo(int IdGrupo)
    {
        var sql = $@"select 
                        cp.Id_Configuracao_Calculo_Pc,
                        cp.Descricao,
                        pc.Tipo_Param,
                        g.Descricao as descricao_grupo,
                        g.Id_Grupo as ID_grupo,
                        fc.Id_Faixa_Calculo,
                        fc.Id_Parametro_Calculo_Pc,
                        fc.Dias_De_Calc,
                        fc.Dias_Ate_Calc,
                        fc.Indice_Faixa,
                        fc.Dias_Ind_Faixa,
                        fc.Desc_Original,
                        fc.Desc_Sobre,
                        fc.Desc_Obrigatorio,
                        fc.Juros_Md,
                        fc.Tipo_Juros,
                        fc.Perc_Juros,
                        fc.Dias_Juros,
                        fc.Juros_Sobre,
                        fc.Corr_Juros,
                        fc.Perc_Desc_Juros,
                        fc.Taxap_Md,
                        fc.Tipo_Taxap,
                        fc.Perc_Taxap,
                        fc.Dias_Taxap,
                        fc.Taxap_Sobre,
                        fc.Perc_Desc_Taxap,
                        fc.Perc_Multa,
                        fc.Dias_Multa,
                        fc.Multa_Sobre,
                        fc.Perc_Desc_Multa,
                        fc.Perc_Honor,
                        fc.Dias_Honor,
                        fc.Perc_Honor_Vincenda,
                        fc.Grp_Cheque,
                        fc.Grp_Dinheiro,
                        fc.Grp_Boleto,
                        fc.Desc_Custas,
                        fc.Define_Faixa_Valor,
                        fc.Faixa_Valor_De,
                        fc.Faixa_Valor_Ate,
                        fc.Forma_Faixa,
                        fc.Calc_Desagio,
                        fc.Calc_Desagio_Tx_Mensal,
                        fc.Calc_Desagio_Tx_Contrato,
                        fc.Juros_Usar_Tx_Contrato,
                        fc.Fase_Atuacao,
                        fc.Quantidade_Pmt,
                        fc.Atraso_Minimo_Negociacao,
                        fc.Desconto_Original_Vincenda,
                        fc.Perc_Desc_Hono,
                        fc.Indice_Desagio,
                        fc.Dias_Inicio_Creliq,
                        fc.Dias_Inicio_Prejuizo,
                        fc.Juros_Prejuizo,
                        fc.Multa_Usar_Tx_Contrato,
                        fc.Mora_Usar_Tx_Contrato
                    from par.Relaciona_Configuracao_Calculo_Pc rpc 
                        inner join par.Configuracao_Calculo_Pc cp on cp.Id_Configuracao_Calculo_Pc = rpc.Id_Configuracao_Calculo_Pc
                        inner join par.Parametro_Calculo_Pc pc on pc.Id_Configuracao_Calculo_Pc = cp.Id_Configuracao_Calculo_Pc
                        inner join par.Grupo g on g.Id_Grupo = rpc.Id_Chave
                        INNER JOIN Par.Faixa_Calculo fc (nolock) ON pc.Id_Parametro_Calculo_Pc = fc.Id_Parametro_Calculo_Pc and rpc.Id_Configuracao_Calculo_Pc = cp.Id_Configuracao_Calculo_Pc
                    where pc.Status = 1
                        and rpc.Tabela = 4
                        and g.Id_Grupo = {IdGrupo}
                        and pc.Tipo_Param = 'N'";

        return sql;
    }
    public string SqlParcelasNegociacaoBoleto(int IdNegociacao) => @$"SELECT b.*,
                                                                            bc.Banco,
                                                                            c.Conta,
                                                                            m.Descricao AS 'Moeda',
                                                                            f.Descricao AS 'Fase',
                                                                            n.Id_Cobrador_Gerador,
                                                                            n.Id_Negociacao
                                                                    FROM   cob.Negociacao (nolock) n
                                                                        INNER JOIN cob.Boleto (nolock) b
                                                                                ON b.Id_Negociacao = n.Id_Negociacao
                                                                        INNER JOIN par.Conta (nolock) c
                                                                                ON b.Id_Conta = c.Id_Conta
                                                                        INNER JOIN par.Banco (nolock) bc
                                                                                ON b.Id_Banco = bc.Id_Banco
                                                                        INNER JOIN par.Moeda (nolock) m
                                                                                ON b.Id_Moeda = m.Id_Moeda
                                                                        INNER JOIN par.Fase (nolock) f
                                                                                ON b.Id_Fase = f.Id_Fase   
                                                                    WHERE  n.Id_Negociacao = {IdNegociacao}";
    public string SqlParcelasNegociacaoBoletoContrato(string numeroContrato) => @$"SELECT b.*,
                                                                            bc.Banco,
                                                                            c.Conta,
                                                                            m.Descricao AS 'Moeda',
                                                                            f.Descricao AS 'Fase',
                                                                            n.Id_Cobrador_Gerador,
                                                                            n.Id_Negociacao
                                                                    FROM cob.Negociacao (nolock) n
                                                                        INNER JOIN cob.Boleto (nolock) b ON b.Id_Negociacao = n.Id_Negociacao
                                                                        INNER JOIN par.Conta (nolock) c ON b.Id_Conta = c.Id_Conta
                                                                        INNER JOIN par.Banco (nolock) bc ON b.Id_Banco = bc.Id_Banco
                                                                        INNER JOIN par.Moeda (nolock) m ON b.Id_Moeda = m.Id_Moeda
                                                                        INNER JOIN par.Fase (nolock) f ON b.Id_Fase = f.Id_Fase   
                                                                        INNER JOIN cob.Contrato (nolock) cc ON cc.Id_Agrupamento = n.Id_Agrupamento
                                                                    WHERE cc.Numero_Contrato = '{numeroContrato}'";
    public string SqlParcelasNegociacaoRecibo(int IdNegociacao) => @$"SELECT r.*,
                                                                            b.Nr_Boleto,
                                                                            c.Razao,
                                                                            f.Descricao AS 'Fase'
                                                                        FROM cob.Negociacao (nolock) pa
                                                                            INNER JOIN cob.Boleto (nolock) b
                                                                                    ON b.Id_Negociacao = pa.Id_Negociacao
                                                                            INNER JOIN cob.Recibo (nolock) r
                                                                                    ON r.Id_Boleto = b.Id_Boleto
                                                                            INNER JOIN par.Cliente (nolock) c
                                                                                    ON c.Id_Cliente = r.Id_Cliente
                                                                            INNER JOIN par.Fase (nolock) f
                                                                                    ON f.Id_Fase = r.Id_Fase
                                                                        WHERE  pa.Id_Negociacao = {IdNegociacao}";
    public string SqlTipoParcela() => @$"SELECT pa.* FROM  par.Tipos_Parcela (nolock) pa";
    public string SqlBuscaFinanciadoDoc(string documento) => @$"SELECT TOP(100) f.Id_Financiado FROM  cob.Financiado (nolock) f WHERE f.Cpfcnpj = '{documento}' ORDER BY f.Id_Financiado DESC";
    public string SqlBuscaFinanciadoTelefone(string telefone) => @$"SELECT TOP(100) t.Id_Financiado FROM  cob.Telefone (nolock) t WHERE REPLACE(t.Ddd, ' ', '') + REPLACE(t.Fone, ' ', '') LIKE '%{telefone}%' ORDER BY t.Id_Telefone DESC";
    public string SqlBuscaFinanciadoEmail(string email) => @$"SELECT TOP(100) e.Id_Financiado FROM  cob.Email (nolock) e WHERE e.Endereco_Email = '{email}' ORDER BY e.Id_Email DESC";
    public string SqlBuscaFinanciadoNome(string nome) => @$"SELECT TOP(100) f.Id_Financiado FROM  cob.Financiado (nolock) f WHERE (REPLACE(f.Nome, ' ', '') LIKE '%{_helper.RemoveAccents(nome)}%' OR REPLACE(f.Nome, ' ', '') LIKE '%{nome}%') ORDER BY f.Id_Financiado DESC";
    public string SqlParcelasContrato(int IdContrato) => $@"SELECT Parcela.Status,
                        Parcela.Nr_Parcela,
                        Parcela.Nr_Plano,
                        Parcela.Tipo_Parcela,
                        Tipos_Parcela.Descricao AS 'Nome_Tipo_Parcela',
                        Parcela.Id_Parcela,
                        Parcela.Dt_Vencimento,
                        Parcela.Vl_Saldo,
                        Parcela.Vl_Original,
                        Parcela.Vl_Saldo_Atualizado,
                        Parcela.Dt_Venc_Boleto,
                        Parcela.Qtde_Boleto_Emitido,
                        Parcela.Status_Negativacao,
                        Parcela.Dt_Inclusao,
                        Parcela.Documento,
                        f.Id_Fase,  
                        f.Descricao as Fase,
                        Cliente.Id_Grupo as GrupoId,
                        Acordo.Nr_Acordo,
                        Acordo.Id_Acordo,
                        Negociacao.Dt_Negociacao,
                        Contrato.Id_Contrato,
                        Contrato.Numero_Contrato,
                        Contrato.Contrato_Aberto,
                        Contrato.Vl_Contr,
                        CASE
                            WHEN Datediff(day, Parcela.Dt_Vencimento, Getdate()) < 0 THEN 0
                            ELSE Datediff(day, Parcela.Dt_Vencimento, Getdate())
                        END                     AS 'Atraso',
                        Grupo.Descricao AS 'Grupo',
                        0.00 AS 'Vl_Atualizado',
                        0.00 AS 'Vl_Desc_Max',
                        Financiado.CpfCnpj
                    FROM   cob.Contrato (nolock)
                        INNER JOIN par.Cliente (nolock)
                                ON Contrato.Id_Cliente = Cliente.Id_Cliente
                        LEFT JOIN par.Fase (nolock) f
                                ON f.Id_Fase = Contrato.Id_Fase
                        INNER JOIN cob.Financiado (nolock)
                                ON Financiado.Id_Financiado = Contrato.Id_Financiado
                        LEFT JOIN cob.Parcela (nolock)
                                ON Parcela.Id_Contrato = Contrato.Id_Contrato
                        LEFT JOIN cob.Negociacao_Parcela (nolock)
                                ON Negociacao_Parcela.Id_Parcela = Parcela.Id_Parcela
                        LEFT JOIN cob.Negociacao (nolock)
                                ON Negociacao_Parcela.Id_Negociacao = Negociacao.Id_Negociacao
                        LEFT JOIN cob.Acordo (nolock)
                                ON Parcela.Id_Acordo = Acordo.Id_Acordo
                        INNER JOIN par.Tipos_Parcela (nolock)
                                ON Tipos_Parcela.Id_Tipos_Parcela = Parcela.Tipo_Parcela
                        INNER JOIN par.Grupo (nolock)
                                ON Grupo.Id_Grupo = Cliente.Id_Grupo
                    WHERE  Contrato.Id_Contrato = {IdContrato} AND Parcela.Status = 'A'
                    ORDER  BY Negociacao.Dt_Negociacao DESC ";
    public string SqlContratos(int IdAgrupamento)
    {
        var sql = $@"SELECT Contrato.Id_Contrato,
                        Contrato.Numero_Contrato,
                        Contrato.Contrato_Aberto
                    FROM   cob.Contrato (nolock)
                    WHERE  Contrato.Id_Agrupamento = {IdAgrupamento}
                        ORDER  BY Negociacao.Dt_Negociacao DESC";
        return sql;
    }

    public string SqlParcelasContratoCalculo(int IdContrato) => $@"SELECT 
                        Parcela.Vl_Original,
                        Contrato.Id_Grupo as 'GrupoId',
                        CASE
                            WHEN Datediff(day, Parcela.Dt_Vencimento, Getdate()) < 0 THEN 0
                            ELSE Datediff(day, Parcela.Dt_Vencimento, Getdate())
                        END                     AS 'Atraso'
                    FROM   cob.Contrato (nolock)
                        INNER JOIN par.Cliente (nolock)
                                ON Contrato.Id_Cliente = Cliente.Id_Cliente
                        LEFT JOIN par.Fase (nolock) f
                                ON f.Id_Fase = Contrato.Id_Fase
                        INNER JOIN cob.Financiado (nolock)
                                ON Financiado.Id_Financiado = Contrato.Id_Financiado
                        LEFT JOIN cob.Parcela (nolock)
                                ON Parcela.Id_Contrato = Contrato.Id_Contrato
                        INNER JOIN par.Tipos_Parcela (nolock)
                                ON Tipos_Parcela.Id_Tipos_Parcela = Parcela.Tipo_Parcela
                        INNER JOIN par.Grupo (nolock)
                                ON Grupo.Id_Grupo = Cliente.Id_Grupo
                    WHERE  Contrato.Id_Contrato = {IdContrato}
                    AND Parcela.Status = 'A'";

    public string SqlFases() => $@"SELECT * FROM Par.Fase (nolock) f";

    public string SqlCalculoDataAcordo(int IdAcordo) => $@"select a.Id_Acordo,
                                        a.Nr_Acordo,
                                        a.Situacao,
                                        a.Status,
                                        pa.Id_Parametro_Acordo,
                                        pa.Dias_Stadby_Acordo,
                                        pa.Dias_Cancelamento_Acordo_1_Parc
                                        from cob.Acordo (nolock) a
                                        inner join par.Parametro_Acordo (nolock) pa on pa.Id_Parametro_Acordo=a.Id_Parametro_Acordo
                                        where a.Id_Acordo={IdAcordo}";

    public string SqlWSFinanDetails(int?[] IdsFinan, bool isRodobens = false)
    {
        string sql = $@"select 
            ag.Id_Agrupamento as 'IdAgrupamento',
            g.Id_Grupo as 'IdGrupo',
            g.Descricao as 'Grupo',
            f.Id_Financiado as 'IdFinanciado',
            f.Nome as 'Financiado',
            f.Cpfcnpj,
            cli.Razao as 'Cliente',
            ag.Numero_Contrato as 'Contrato'
        from cob.Financiado (nolock) f
        inner join cob.Contrato (nolock) c on c.Id_Financiado = f.Id_Financiado
        inner join cob.Contrato (nolock) ag on c.Id_Agrupamento = ag.Id_Contrato
        inner JOIN par.Cliente (nolock) cli on cli.Id_Cliente = c.Id_Cliente
        inner join par.Grupo (nolock) g on g.Id_Grupo = c.Id_Grupo
        where f.Id_Financiado in ({string.Join(',', IdsFinan)})";

        if (isRodobens)
            sql += " AND c.Id_Cliente_Web = 53 ";

        sql += @"GROUP BY 
            ag.Id_Agrupamento,
            g.Id_Grupo,
            g.Descricao,
            f.Id_Financiado,
            f.Nome,
            f.Cpfcnpj,
            cli.Razao,
            ag.Numero_Contrato";
        return sql;
    }
    public string SqlWSCustomerDetails(int customerId, string fone) => $@"select 
                                                            ag.Id_Agrupamento as 'IdAgrupamento',
                                                            g.Id_Grupo as 'IdGrupo',
                                                            g.Descricao as 'Grupo',
                                                            f.Id_Financiado as 'IdFinanciado',
                                                            f.Nome as 'Financiado',
                                                            f.Cpfcnpj,
                                                            cli.Razao as 'Cliente',
                                                            ag.Numero_Contrato as 'Contrato'
                                                        from cob.Financiado (nolock) f
                                                        inner join cob.Contrato (nolock) c on c.Id_Financiado = f.Id_Financiado
                                                        inner join cob.Contrato (nolock) ag on c.Id_Agrupamento = ag.Id_Contrato
                                                        inner JOIN par.Cliente (nolock) cli on cli.Id_Cliente = c.Id_Cliente
                                                        inner join par.Grupo (nolock) g on g.Id_Grupo = c.Id_Grupo
                                                        INNER JOIN cob.Telefone tel ON tel.Id_Financiado = f.Id_Financiado
                                                        where c.Id_Contrato = {customerId}
                                                        GROUP BY 
                                                            ag.Id_Agrupamento,
                                                            g.Id_Grupo,
                                                            g.Descricao,
                                                            f.Id_Financiado,
                                                            f.Nome,
                                                            f.Cpfcnpj,
                                                            cli.Razao,
                                                            ag.Numero_Contrato";

    public string SqlCidadeUf() => $@"select * from par.DDD_Cidade_UF (nolock)";
    public string SqlUf() => $@"select distinct UF from par.DDD_Cidade_UF (nolock)";
    public string SqlHistoricoId(int ContratoId, string OcorrenciaCod) => $@"select top(1) 
                                                                            h.Id_Historico,
                                                                            h.Id_Financiado,
                                                                            h.Id_Contrato
                                                                        from Cob.Historico (nolock) h
                                                                        inner join Par.Ocorrencia_Sistema os on os.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
                                                                        where h.Id_Contrato = {ContratoId} and os.Cod_Ocorr_Sistema = {OcorrenciaCod}
                                                                        order by h.Id_Historico desc";
    public string SqlHistoricoIdInt(int ContratoId, int OcorrenciaCod) => $@"select top(1) 
                                                                            h.Id_Historico,
                                                                            h.Id_Financiado,
                                                                            h.Id_Contrato
                                                                        from Cob.Historico (nolock) h
                                                                        inner join Par.Ocorrencia_Sistema os on os.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
                                                                        where h.Id_Contrato = {ContratoId} and os.Id_Ocorrencia_Sistema = {OcorrenciaCod}
                                                                        order by h.Id_Historico desc";

    public string SqlTelefoneByAgrupamento(string telefone, int IdAgrupamento) => @$"SELECT TOP(1) 
                                                                                        t.Id_Telefone,
                                                                                        t.Id_Financiado
                                                                                    FROM  Cob.Telefone (nolock) t 
                                                                                    INNER JOIN Cob.Financiado (nolock) f ON f.Id_Financiado = t.Id_Financiado
                                                                                    INNER JOIN Cob.Contrato (nolock) c ON c.Id_Financiado = t.Id_Financiado
                                                                                    WHERE REPLACE(t.Ddd, ' ', '') + REPLACE(t.Fone, ' ', '') LIKE '%{telefone}%' 
                                                                                    AND c.Id_Agrupamento = {IdAgrupamento}
                                                                                    ORDER BY t.Id_Telefone DESC";

    public string SqlIndicadoresCampanha(int IdContrato) => $@"select mo.Mensagem from cob.Contrato (nolock) c
inner join cob.Contrato_Mensagem (nolock) m on m.Id_Contrato = c.Id_Contrato
inner join par.Mensagem_Operacao (nolock) mo on mo.Id_Mensagem_Operacao = m.Id_Mensagem_Operacao
where c.Id_Contrato = {IdContrato}";


    public string SqlWSFinanGarantia(int IdContrato) => $@"
select 
    g.Marca + g.Modelo as 'Bens'
from cob.Garantia (nolock) g
where g.Id_Contrato = {IdContrato}";

    public string SqlContratoPadrao(int IdContrato) => $@"select top(1) * from cob.Contrato_Padrao (nolock) b where b.Id_Contrato = {IdContrato}";
    public string SqlFindCrm(int IdContrato, string doc) => $@"select top(1) b.Id_Contrato from cob.Contrato (nolock) b 
INNER JOIN cob.Financiado (nolock) f ON f.Id_Financiado = b.Id_Financiado
where b.Id_Contrato = {IdContrato} AND f.CpfCnpj = '{doc}'";
    public string SqlContractBtgInfo(int IdContrato) => $@"SELECT TOP(1) 
	e.Endereco_Email, 
	c.Numero_Contrato,
	CASE
	   WHEN Datediff(day, p.Dt_Vencimento, Getdate()) < 0 THEN 0
	   ELSE Datediff(day, p.Dt_Vencimento, Getdate())
	END AS 'NrAtraso'
FROM Cob.Contrato (nolock) c 
INNER JOIN Cob.Financiado (nolock) f ON c.Id_Financiado = f.Id_Financiado
INNER JOIN Cob.Parcela (nolock) p ON c.Id_Contrato = p.Id_Contrato
LEFT JOIN Cob.Email (nolock) e ON e.Id_Financiado = f.Id_Financiado
WHERE c.Id_Contrato = {IdContrato} AND p.Status = 'A'
ORDER BY p.Dt_Vencimento ASC";
    public string SqlOpenNegRPA(int IdGrouping, int IdUsuario, decimal VlNeg) => $@"SELECT TOP(1) Id_Negociacao FROM Cob.Negociacao (nolock) neg
WHERE neg.Liberado = 0 AND neg.Status = 'A' AND neg.Id_Agrupamento = {IdGrouping} AND Id_Usuario = {IdUsuario} AND Vl_Negociacao = {VlNeg.ToString().Replace(",", ".")} ORDER BY Id_Negociacao DESC";

    public string SqlOpenNegCRM() => $@"SELECT TOP(1) 
        neg.Id_Negociacao
    FROM Cob.Negociacao (nolock) neg
    LEFT JOIN Cob.Boleto (nolock) b ON b.Id_Negociacao = neg.Id_Negociacao
    WHERE neg.Status = 'A' 
        AND neg.Id_Agrupamento = @IdGrouping
        AND Id_Usuario = @IdUsuario 
        AND Vl_Negociacao = @VlNeg
        AND Dt_Negociacao = @DtNegociacao
        AND b.Id_Boleto IS NULL
    ORDER BY Id_Negociacao DESC";

    public string SqNegById(int Id) => $@"SELECT Id_Negociacao, Status, Liberado FROM Cob.Negociacao (nolock) neg
WHERE neg.Id_Negociacao = {Id}";
    public string SqlIndicadoresOcorrencia(int Id) => $@"SELECT count(cs.Id_Ocorrencia_Sistema) as QtdOcorrencias, cs.Descricao, u.Nome from cob.Historico (nolock) h
inner join cob.Contrato (nolock) c on h.Id_Contrato = c.Id_Contrato
inner join par.Ocorrencia_Sistema (nolock) cs on cs.Id_Ocorrencia_Sistema = h.Id_Ocorrencia_Sistema
inner join usu.Usuario (nolock) u on u.Id_Usuario = h.Id_Usuario_Inseriu
where c.Id_Agrupamento = {Id}
group by cs.Descricao, u.Nome ";
    public string SqlNrNegociacao(int Id) => $@"SELECT count(n.Id_Negociacao) as Qtd from cob.Negociacao (nolock) n
inner join cob.Contrato (nolock) c on n.Id_Agrupamento = c.Id_Agrupamento
where c.Id_Agrupamento = {Id}";

    public string SqlOutrosContratos(bool IdGrupoIgnorar = true, bool isRodobens = false)
    {
        string sql = @$"SELECT c.Numero_Contrato as NrContrato, f.Id_Financiado as IdFinanciado, c.Id_Agrupamento as IdAgrupamento, c.Id_Grupo as IdGrupo, g.Descricao AS Grupo, c.Contrato_Aberto as Ativo, cli.Razao as Cliente
        FROM Cob.Financiado (nolock) f 
        INNER JOIN Cob.Contrato (nolock) c ON c.Id_Financiado = f.Id_Financiado 
        INNER JOIN Par.Grupo (nolock) g ON c.Id_Grupo = g.Id_Grupo
        LEFT JOIN Par.Cliente (nolock) cli on cli.Id_Cliente = c.Id_Cliente
        WHERE f.CpfCnpj = @Documento AND c.Id_Agrupamento = c.Id_Contrato";

        if (IdGrupoIgnorar)
        {
            sql += $@" AND c.Id_Grupo != @IdGrupoIgnorar";
        }
        if (isRodobens)
            sql += @" AND c.Id_Cliente_Web = 53 ";
        return sql;
    }

    public string SqlBoletosContrato() => @$"SELECT b.Id_Boleto AS IdBoleto, b.Status, b.Dt_Venc AS DtVenc, p.Nr_Parcela AS NrParcela, b.Id_Negociacao AS IdNegociacao
            FROM Cob.Boleto b
            INNER JOIN Cob.Contrato (nolock) c ON c.Id_Agrupamento = b.Id_Agrupamento
            INNER JOIN Cob.Negociacao_Parcela (nolock) np ON np.Id_Negociacao = b.Id_Negociacao
            INNER JOIN Cob.Parcela (nolock) p ON p.Id_Parcela = np.Id_Parcela
            WHERE c.Id_Agrupamento = @Id
            GROUP BY b.Id_Boleto, b.Status, b.Dt_Venc, p.Nr_Parcela, b.Id_Negociacao";

    public string SqlBoletosContratoBoleto() => @$"SELECT b.Id_Boleto AS IdBoleto, b.Status, b.Dt_Venc AS DtVenc, p.Nr_Parcela AS NrParcela, b.Id_Negociacao AS IdNegociacao
        FROM Cob.Boleto b
        INNER JOIN Cob.Contrato (nolock) c ON c.Id_Agrupamento = b.Id_Agrupamento
        INNER JOIN Cob.Negociacao_Parcela (nolock) np ON np.Id_Negociacao = b.Id_Negociacao
        INNER JOIN Cob.Parcela (nolock) p ON p.Id_Parcela = np.Id_Parcela
        WHERE c.Numero_Contrato = @numero_Contrato and c.Id_Agrupamento != @idAgrupamento
        GROUP BY b.Id_Boleto, b.Status, b.Dt_Venc, p.Nr_Parcela, b.Id_Negociacao";

    public string SqlCheckDNR(string Ids) => @$"select
    c.Id_Contrato,
    Numero_Contrato,
    Nome,
    Cpfcnpj,
    g.Descricao as 'Grupo',
    c.Id_Grupo,
    cl.Nome_Res as 'Cliente',
    c.Id_Cliente,
    gu.Grupo as 'Escritorio'
from cob.Distribuicao (nolock) d
Inner JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = d.Id_Agrupamento
Inner Join cob.Financiado (nolock) f on f.Id_Financiado = c.Id_Financiado
Inner Join par.Grupo (nolock) g on g.Id_Grupo = c.Id_Grupo
Inner join par.Cliente (nolock) cl on cl.Id_Cliente = c.Id_Cliente
Inner Join usu.Grupo_Usuario (nolock) gu on gu.Id_Grupo_Usuario = d.Id_Escob
where Id_Escob in (772, 775)
and c.Contrato_Aberto = 1
AND c.Id_Contrato IN ({Ids})";



    public string SqlDetalhesParcelas() => @$"select
    c.Id_Contrato,
    p.Id_Parcela,
    c.Numero_Contrato,
    p.Nr_Parcela
from cob.Parcela (nolock) p
Inner JOIN cob.Contrato (nolock) c on c.Id_Contrato = p.Id_Contrato
where p.Id_Parcela IN @Parcelas";

    public string SqlMotivoDevolucao() => @$"select
    Id_Motivo_Devolucao,
    Descricao
from Par.Motivo_Devolucao (nolock)";
    public string SqlListarBoleto() => @$"SELECT b.*,
        bc.Banco,
        c.Conta,
        m.Descricao AS 'Moeda',
        f.Descricao AS 'Fase',
        n.Id_Cobrador_Gerador,
        n.Id_Negociacao
    FROM   cob.Negociacao (nolock) n
    INNER JOIN cob.Boleto (nolock) b
        ON b.Id_Negociacao = n.Id_Negociacao
    INNER JOIN par.Conta (nolock) c
        ON b.Id_Conta = c.Id_Conta
    INNER JOIN par.Banco (nolock) bc
        ON b.Id_Banco = bc.Id_Banco
    INNER JOIN par.Moeda (nolock) m
        ON b.Id_Moeda = m.Id_Moeda
    INNER JOIN par.Fase (nolock) f
        ON b.Id_Fase = f.Id_Fase   
    WHERE  n.Id_Agrupamento = @IdAgrupamento";

    public string SqlListarBoletoContrato() => @$"SELECT b.*,
        bc.Banco,
        c.Conta,
        m.Descricao AS 'Moeda',
        f.Descricao AS 'Fase',
        n.Id_Cobrador_Gerador,
        n.Id_Negociacao
    FROM cob.Negociacao (nolock) n
    INNER JOIN cob.Contrato (nolock) co ON n.Id_Agrupamento = co.Id_Agrupamento
    INNER JOIN cob.Boleto (nolock) b ON b.Id_Negociacao = n.Id_Negociacao
    INNER JOIN par.Conta (nolock) c ON b.Id_Conta = c.Id_Conta
    INNER JOIN par.Banco (nolock) bc ON b.Id_Banco = bc.Id_Banco
    INNER JOIN par.Moeda (nolock) m ON b.Id_Moeda = m.Id_Moeda
    INNER JOIN par.Fase (nolock) f ON b.Id_Fase = f.Id_Fase   
    WHERE co.Numero_Contrato = @numeroContrato and n.Id_Agrupamento != @idAgrupamento";

    public string SqlFilterSafraCampaign() => @$"SELECT 
	c.Id_Agrupamento AS 'GroupingId',
	c.Id_Contrato AS 'ContractId',
	c.Numero_Contrato AS 'Contract',
	f.Nome AS 'Name',
	f.CpfCnpj AS 'Document',
   f.Id_Financiado AS 'FinancedId',
	COUNT(pc.Id_Parcela) AS 'InstallmentAmount',
	SUM(pc.Vl_Saldo) AS 'InstallmentTotalValue'
FROM Cob.Contrato (nolock) c
INNER JOIN Cob.Parcela (nolock) pc ON pc.Id_Contrato = c.Id_Contrato AND pc.Status = 'A'
INNER JOIN Cob.Financiado (nolock) f ON f.Id_Financiado = c.Id_Financiado
LEFT JOIN Cob.Acordo (nolock) a ON a.Id_Agrupamento = c.Id_Agrupamento AND a.Status != 'C' AND Situacao = 'L'
WHERE c.Id_Cliente IN @clientIds AND LEN(c.Numero_Contrato) = 25 AND a.Id_Acordo IS NULL
GROUP BY
	c.Id_Agrupamento,
	c.Id_Contrato,
	f.Id_Financiado,
	c.Numero_Contrato,
	f.Nome,
	f.CpfCnpj
HAVING 
	(
		SELECT TOP(1) 
		  CASE
		      WHEN Datediff(day, p.Dt_Vencimento, Getdate()) < 0 THEN 0
		      ELSE Datediff(day, p.Dt_Vencimento, Getdate())
		  END
		FROM Cob.Parcela (nolock) p
		WHERE p.Id_Contrato = c.Id_Contrato AND p.Status = 'A'
		ORDER BY p.Dt_Vencimento ASC
	) <= @delay
	AND COUNT(pc.Id_Parcela) <= @amount";
    public string SqlBuscaDadosFinanciadosSafra()
    {
        string sql = $@"SELECT TOP(1000) f.Id_Financiado,
            c.Id_Contrato,
            c.Id_Agrupamento,
            c.Numero_Contrato,
            c.Contrato_Aberto,
            c.Vl_contr,
            f.Nome,
            f.Cpfcnpj,
            f.Dt_Nascimento,
            f.Data_Enriquecimento,
            f.Sexo,
            f.Est_Civil,
            f.Score_Serasa,
            f.Tipo_Financiado,
            f.Rg,
            f.Dt_Emiss_Rg,
            f.Orgao_Emiss_Rg,
            f.Uf_Emiss_Rg,
            f.Tipo_Pessoa,
            f.Conjugue,
            f.Mae,
            f.Pai,
            cli.Id_Cliente,
            cli.Id_Grupo,
            cli.Nome_Res AS 'Cliente',
            g.Descricao  AS 'Grupo',
            fa.Id_Fase,
            fa.Cod_Fase,
            fa.Descricao AS 'Fase',
            fa.Cor,
            sc.Descricao AS 'Status'
        FROM   cob.Financiado (nolock) AS f
            LEFT JOIN cob.Contrato (nolock) c
                    ON f.Id_Financiado = c.Id_Financiado
            INNER JOIN par.Cliente (nolock) cli
                    ON cli.Id_Cliente = c.Id_Cliente
            INNER JOIN par.Grupo (nolock) g
                    ON g.Id_Grupo = cli.Id_Grupo
            INNER JOIN par.Fase (nolock) fa
                    ON fa.Id_Fase = c.Id_Fase
            LEFT JOIN cob.Distribuicao d 
                ON d.Id_Agrupamento = c.Id_Agrupamento
            LEFT JOIN par.Status_Contrato sc 
                ON sc.Id_Status_Contrato = d.Id_Status_Contrato
        WHERE  cli.Ativo = 1 AND c.Id_Contrato = @contractId";
        return sql;
    }
    public string SqlPrimeiraParcelaSafra()
    {
        string sql = $@"SELECT TOP(1) p.Nr_Parcela, p.Vl_Original, p.Dt_Vencimento, p.Id_Parcela
        FROM   cob.Parcela (nolock) AS p
        WHERE p.Id_Contrato = @contractId And Status = 'A'";
        return sql;
    }
    public string SqlFinancedValidEmail(bool uniqueFinancied = true) => $@"SELECT TOP(1) * FROM Cob.Email (nolock) e
        WHERE e.Id_Financiado {(uniqueFinancied ? "= @idFinanced" : "IN (@idFinanced)")} AND e.Status_Email IN (2,1)
        ORDER BY e.Status_Email DESC";

    public string SqlFinancedSimple() => $@"SELECT Id_Financiado, CpfCnpj, Nome FROM Cob.Financiado (nolock) e
        WHERE e.Id_Financiado IN @idFinanceds";

        public string SqlFinancedSimpleById() => $@"SELECT Id_Financiado, CpfCnpj, Nome FROM Cob.Financiado (nolock) e
        WHERE e.Id_Financiado = @Id_Financiado";

        public string SqlSimpleInfoByContractId() => $@"SELECT 
                f.Id_Financiado as 'IdFinanciado',
                f.CpfCnpj, 
                f.Nome,
                c.Id_Contrato as 'IdContrato',
                c.Numero_Contrato as 'NumeroContrato'
                c.Id_Grupo as 'IdGrupo',
                c.Id_Cliente as 'IdCliente',
        FROM Cob.Contrato c (nolock) e
        INNER JOIN Cob.Financiado (nolock) f ON f.Id_Financiado = c.Id_Financiado
        WHERE e.Id_Contrato = @IdContrato";

        public string SqlFilterSafraCampaignExcel() => @$"SELECT 
	c.Id_Agrupamento AS 'GroupingId',
	c.Id_Contrato AS 'ContractId',
	c.Numero_Contrato AS 'Contract',
	f.Nome AS 'Name',
	f.CpfCnpj AS 'Document',
   f.Id_Financiado AS 'FinancedId',
	COUNT(pc.Id_Parcela) AS 'InstallmentAmount',
	SUM(pc.Vl_Saldo) AS 'InstallmentTotalValue'
FROM Cob.Contrato (nolock) c
INNER JOIN Cob.Parcela (nolock) pc ON pc.Id_Contrato = c.Id_Contrato AND pc.Status = 'A'
INNER JOIN Cob.Financiado (nolock) f ON f.Id_Financiado = c.Id_Financiado
LEFT JOIN Cob.Acordo (nolock) a ON a.Id_Agrupamento = c.Id_Agrupamento AND a.Status != 'C' AND Situacao = 'L'
WHERE c.Numero_Contrato IN @contratos
GROUP BY
	c.Id_Agrupamento,
	c.Id_Contrato,
	f.Id_Financiado,
	c.Numero_Contrato,
	f.Nome,
	f.CpfCnpj";


    public string SqlParcelasIds() => $@"SELECT Id_Parcela FROM Cob.Parcela (nolock) p
WHERE p.Id_Contrato = @IdContrato and p.Nr_Parcela in @nrParcelas";

    public string SqlCheckStatusContrato() => @$"select
    c.Id_Contrato,
    sc.Descricao as 'Status'
from cob.Distribuicao (nolock) d
Inner JOIN cob.Contrato (nolock) c on c.Id_Agrupamento = d.Id_Agrupamento
Inner JOIN Par.Status_Contrato (nolock) sc on sc.Id_Status_Contrato = d.Id_Status_Contrato
where c.Id_Agrupamento = @idAgrupamento";


    public string SqlBoletosUsuarioLogado() => @$"select 
        b.Nr_Boleto as 'NrBoleto',
        f.Cpfcnpj as 'CpfCnpj',
        f.Nome as 'NomeFinanciado',
        b.Vl_Boleto as 'ValorBoleto',
        b.Dt_Venc as 'DataVencimento',
        b.Status as 'StatusBoleto',
        u.Nome as 'NomeOperador',
        u.Id_Usuario as 'IdOperador',
        c.Nome_Res as 'NomeCliente',
        ct.Numero_Contrato as 'NrContrato',
        g.Descricao as 'NomeGrupo',
        np.TotalHonorarios as 'TotalHonorarios',
        n.Id_Negociacao as 'IdNegociacao'
    from cob.Boleto b (nolock)    
    inner join cob.Negociacao n (nolock) on n.Id_Negociacao = b.Id_Negociacao
    inner join cob.Contrato ct (nolock) on ct.Id_Contrato = b.Id_Agrupamento
    inner join cob.Financiado f (nolock) on f.Id_Financiado = ct.Id_Financiado
    inner join usu.Usuario u (nolock) on u.Id_Usuario = n.Id_Cobrador_Gerador
    inner join par.Cliente c (nolock) on c.Id_Cliente = ct.Id_Cliente
    inner join par.Grupo g (nolock) on g.Id_Grupo = c.Id_Grupo
    left join (
        select 
            Id_Negociacao,
            sum(Vl_Honorario) as TotalHonorarios
        from cob.Negociacao_Parcela (nolock)
        group by Id_Negociacao
    ) np on np.Id_Negociacao = n.Id_Negociacao
    where b.Dt_Emiss >= @dtInicio and b.Dt_Emiss <= @dtFim
    and u.Id_Usuario = @idUsuario";

    public string SqlControleUsuarioGroupBy() => @$"
    group by 
        f.Nome,
        c.Numero_Contrato,
        b.Dt_Venc ,
        cc.Nome_Res ,
        g.Descricao ,
        b.Dt_Emiss ,
        b.Vl_Boleto,
        np.Vl_Honorario ,
        b.Nr_Boleto ,
        b.Status,
        u.Nome,
        p.Nr_Parcela,
        tp.Descricao,
        c.Numero_Contrato,
        u.Id_Usuario,
        b.Id_Boleto,
        b.Id_Negociacao";

    public string SqlControleUsuario() => @$"select 
        c.Numero_Contrato as NumeroContrato,
        p.Nr_Parcela as NumeroParcelaEnvolvida,
        tp.Descricao as DescricaoTipoParcela,
        f.Nome as NomeCliente,
        null as BP,
        SUBSTRING(c.Numero_Contrato,0,5) as EMP,
        b.Dt_Venc as Vencimento,
        cc.Nome_Res as CarteiraEmpreendimento,
        g.Descricao as DescricaoCarteira,
        b.Dt_Emiss as DataEmissao,
        b.Vl_Boleto as ValorCaixa,
        np.Vl_Honorario as Honorario,
        b.Nr_Boleto as NumeroBoleto,
        CASE WHEN b.Status = 'A' THEN 'ABERTO'
            WHEN b.Status = 'C' THEN 'CANCELADO'
            WHEN b.Status = 'P' THEN 'PAGO'
            ELSE 'INDEFINIDO' END as StatusBoleto,
        null as Observacao,
        u.Nome as NomeUsuario,
        u.Id_Usuario as IdUsuario,
        b.Id_Boleto AS IdBoleto,
        b.Id_Negociacao as IdNegociacao
    from cob.Boleto b
    inner join cob.Negociacao n on n.Id_Negociacao = b.Id_Negociacao
    inner join cob.Negociacao_Parcela np on np.Id_Negociacao = n.Id_Negociacao
    inner join cob.Parcela p on p.Id_Parcela = np.Id_Parcela
    inner join cob.Contrato c on c.Id_Contrato = p.Id_Contrato
    inner join cob.Financiado f on f.Id_Financiado = c.Id_Financiado
    inner join par.Cliente cc on cc.Id_Cliente = c.Id_Cliente
    inner join par.Grupo g on g.Id_Grupo = c.Id_Grupo
    inner join usu.Usuario u on u.Id_Usuario = n.Id_Cobrador_Gerador
    inner join par.Tipos_Parcela tp on tp.Id_Tipos_Parcela = p.Tipo_Parcela
    ";

    public string SqlListContratoPadrao(int IdContrato) => $@"select  * from cob.Contrato_Padrao (nolock) b where b.Id_Contrato = {IdContrato}";


        public string SqlListDadosMailing(GvcRodobens crm, string groupId, string? fase, int? dayDueStart, int? dayDueEnd, float? debtValueStart = 0, float? debtValueEnd = 0, List<int> list_ids_financiado = null)
        {
                var sql = $@"
            WITH 
            VlOriginalPorContrato AS (
                SELECT 
                    Id_Contrato,
                    SUM(p.Vl_Original) AS Soma_Vl_Original
                FROM cob.Parcela p (NOLOCK)
                WHERE p.status = 'A'
                GROUP BY Id_Contrato
            ),
            ContratoPorCliente AS (
            SELECT
                f.Id_Financiado, c.Id_Contrato,
                c.Id_Agrupamento, c.Numero_Contrato, c.Contrato_Aberto, c.Vl_contr, f.Nome,
                f.Cpfcnpj, f.Dt_Nascimento, f.Data_Enriquecimento, f.Sexo, f.Est_Civil,
                f.Score_Serasa, f.Tipo_Financiado, f.Rg, f.Dt_Emiss_Rg, f.Orgao_Emiss_Rg,
                f.Uf_Emiss_Rg, f.Tipo_Pessoa, f.Conjugue, f.Mae, f.Pai, cli.Id_Cliente,
                cli.Id_Grupo, cli.Nome_Res AS 'Cliente', g.Descricao AS 'Grupo', fa.Id_Fase,
                fa.Cod_Fase, fa.Descricao AS 'Fase', fa.Cor, sc.Descricao AS 'Status',
                ROW_NUMBER() OVER (PARTITION BY f.Cpfcnpj ORDER BY c.Numero_Contrato) AS RowNum,
                vo.Soma_Vl_Original
            FROM cob.Contrato (NOLOCK) c
                INNER JOIN par.Cliente (NOLOCK) cli ON c.Id_Cliente = cli.Id_Cliente
                INNER JOIN cob.Financiado (NOLOCK) f ON f.Id_Financiado = c.Id_Financiado
                INNER JOIN par.Grupo (NOLOCK) g ON g.Id_Grupo = cli.Id_Grupo
                INNER JOIN par.Fase (NOLOCK) fa ON fa.Id_Fase = c.Id_Fase
                INNER JOIN cob.Parcela (NOLOCK) p ON p.Id_Contrato = c.Id_Contrato    
                INNER JOIN cob.Distribuicao d ON d.Id_Agrupamento = c.Id_Agrupamento
                INNER JOIN par.Status_Contrato sc ON sc.Id_Status_Contrato = d.Id_Status_Contrato
                INNER JOIN VlOriginalPorContrato vo ON vo.Id_Contrato = c.Id_Contrato
            WHERE p.status = 'A' 
                AND cli.Ativo = 1
                AND c.Contrato_Aberto = 1 
                AND cli.Id_Grupo in ({groupId})";
                if (dayDueStart > 0 && dayDueEnd > 0)
                        sql += $@" AND DATEDIFF(day, p.Dt_Vencimento, GETDATE()) BETWEEN {dayDueStart} AND {dayDueEnd} ";
                if (debtValueStart > 0)
                        sql += $@" AND Soma_Vl_Original >= {debtValueStart.ToString().Replace(",", ".")} ";
                if (debtValueEnd > 0)
                        sql += $@" AND Soma_Vl_Original <= {debtValueEnd.ToString().Replace(",", ".")} ";
                if (list_ids_financiado != null)
                        sql += $@" AND f.Id_Financiado in ({string.Join(',', list_ids_financiado)} )";
                if (crm == GvcRodobens.Rodobens)
                        sql += " AND c.Id_Cliente_Web = 53 ";
                if (!string.IsNullOrEmpty(fase))
                {
                        sql += $@" AND fa.Cod_Fase = '{fase}' ";
                }
                sql += $@")
            SELECT Id_Financiado, Id_Contrato, Id_Agrupamento, Numero_Contrato, Contrato_Aberto,
                Vl_contr, Soma_Vl_Original, Nome, Cpfcnpj, Dt_Nascimento, Data_Enriquecimento, Sexo, Est_Civil, Score_Serasa,
                Tipo_Financiado, Rg, Dt_Emiss_Rg, Orgao_Emiss_Rg, Uf_Emiss_Rg, Tipo_Pessoa, Conjugue,
                Mae, Pai, Id_Cliente, Id_Grupo, Cliente, Grupo, Id_Fase, Cod_Fase, Fase, Cor, Status
            FROM ContratoPorCliente
            WHERE RowNum = 1
            ORDER BY Id_Contrato ASC ";

        return sql;
    }
    public string SqlDashboardListContrato(GvcRodobens crm, int? groupId, DateTime? startDate, DateTime? endDate)
    {
        string condition = string.Empty;
        if (startDate is null) startDate = DateTime.Now.AddDays(-5);

        if (startDate != null && endDate != null)
        {
            condition += $" Convert(date,p.Dt_Inclusao) >= '{startDate.Value:yyyy-MM-dd}' and Convert(date,p.Dt_Inclusao) <= '{endDate.Value:yyyy-MM-dd}'";
        }
        else if (startDate != null)
        {
            condition += $" p.Dt_Inclusao >= '{startDate.Value:yyyy-MM-dd}'";
        }

        if (groupId != null && groupId > 0) condition += $" and c.Id_Grupo = {groupId}";

        return $@"SELECT distinct 1 AS Count
                     ,cast('{crm.ToString()}' as varchar(30)) as crm 
                     ,c.Id_Contrato
                     ,g.Descricao
                     ,p.Dt_Inclusao as Dt_Contr
                     ,c.Numero_Contrato
                     ,c.Plano
                     ,f.Cpfcnpj
                    FROM cob.Contrato (nolock) c
                    INNER JOIN cob.Financiado (nolock) f on f.Id_Financiado = c.Id_Financiado
                    INNER JOIN par.Grupo (nolock) g on g.Id_Grupo = c.Id_Grupo
                    INNER JOIN cob.Parcela (nolock) p on p.Id_Contrato = c.Id_Contrato
                        and p.Nr_Parcela = (Select min(Nr_Parcela) from cob.Parcela where Id_Contrato = c.Id_Contrato)
                    where {condition} 
                    order by p.Dt_Inclusao";

    }
}
