﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="EPPlus" Version="7.4.2" />
    <PackageReference Include="itext7" Version="9.0.0" />
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="9.0.0" />
    <PackageReference Include="itext7.pdfhtml" Version="6.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.5" />
    <PackageReference Include="System.DirectoryServices" Version="8.0.0" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="8.0.0" />
    <PackageReference Include="System.DirectoryServices.Protocols" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TelaUnica.Infra.Data.EF\TelaUnica.Infra.Data.EF.csproj" />
    <PackageReference Include="EPPlus" Version="7.4.2" />
  </ItemGroup>

</Project>
