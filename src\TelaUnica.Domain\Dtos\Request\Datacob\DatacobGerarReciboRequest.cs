using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Dtos.Request.Datacob;

public class DatacobGerarReciboRequest
{
    public int IdContrato { get; set; }
    public DateTime DtPagamento { get; set; }
    public decimal VlPagamento { get; set; }
    public List<int> Parcelas { get; set; } = new();
    public int FormaDesconto { get; set; }
    public string TipoPagamento { get; set; } = string.Empty;
    public GvcRodobens? Crm { get; set; }
}