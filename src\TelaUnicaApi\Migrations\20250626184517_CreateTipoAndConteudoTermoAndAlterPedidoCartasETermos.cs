﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class CreateTipoAndConteudoTermoAndAlterPedidoCartasETermos : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PedidoCartasETermos_TipoCartaETermo_TipoId",
                table: "PedidoCartasETermos");

            migrationBuilder.DropIndex(
                name: "IX_PedidoCartasETermos_TipoId",
                table: "PedidoCartasETermos");

            migrationBuilder.DropColumn(
                name: "TipoId",
                table: "PedidoCartasETermos");

            migrationBuilder.AddColumn<int>(
                name: "IdCliente",
                table: "PedidoCartasETermos",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "IdGrupo",
                table: "PedidoCartasETermos",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Tipo",
                table: "PedidoCartasETermos",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "DocCartasETermos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PedidoCartasETermosId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Documento = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DocCartasETermos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DocCartasETermos_PedidoCartasETermos_PedidoCartasETermosId",
                        column: x => x.PedidoCartasETermosId,
                        principalTable: "PedidoCartasETermos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TipoTermo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Nome = table.Column<string>(type: "varchar(255)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TipoTermo", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ConteudoTermo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TipoTermoId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Html = table.Column<string>(type: "text", nullable: false),
                    CabecalhoImg = table.Column<string>(type: "text", nullable: false),
                    RodapeImg = table.Column<string>(type: "text", nullable: false),
                    GrupoId = table.Column<int>(type: "int", nullable: false),
                    Crm = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConteudoTermo", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ConteudoTermo_TipoTermo_TipoTermoId",
                        column: x => x.TipoTermoId,
                        principalTable: "TipoTermo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PedidoTermoInfos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PedidoId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TipoTermoId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    JurisdicaoAtual = table.Column<string>(type: "varchar(255)", nullable: false),
                    NrAtual = table.Column<string>(type: "varchar(255)", nullable: false),
                    ClientePrincipal = table.Column<string>(type: "varchar(255)", nullable: false),
                    TipoAcao = table.Column<string>(type: "varchar(255)", nullable: false),
                    AdversoPrincipal = table.Column<string>(type: "varchar(255)", nullable: false),
                    GrupoCotaContrato = table.Column<string>(type: "varchar(255)", nullable: false),
                    NrParcelasVencidas = table.Column<string>(type: "varchar(255)", nullable: false),
                    ValorParcelasVencidas = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MultaJuros = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Custas = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NrParcelasVincendas = table.Column<string>(type: "varchar(255)", nullable: true),
                    ValorParcelasVincendas = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Honorarios = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Total = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    DataBase = table.Column<DateOnly>(type: "date", nullable: false),
                    QtdParcelasAcordadas = table.Column<int>(type: "int", nullable: false),
                    ValorAcordado = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    DescricaoVeiculo = table.Column<string>(type: "varchar(255)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PedidoTermoInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PedidoTermoInfos_PedidoCartasETermos_PedidoId",
                        column: x => x.PedidoId,
                        principalTable: "PedidoCartasETermos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PedidoTermoInfos_TipoTermo_TipoTermoId",
                        column: x => x.TipoTermoId,
                        principalTable: "TipoTermo",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PedidoTermoParcelas",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InfosId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Numero = table.Column<int>(type: "int", nullable: false),
                    Vencimento = table.Column<DateOnly>(type: "date", nullable: false),
                    Valor = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PedidoTermoParcelas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PedidoTermoParcelas_PedidoTermoInfos_InfosId",
                        column: x => x.InfosId,
                        principalTable: "PedidoTermoInfos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ConteudoTermo_TipoTermoId",
                table: "ConteudoTermo",
                column: "TipoTermoId");

            migrationBuilder.CreateIndex(
                name: "IX_DocCartasETermos_PedidoCartasETermosId",
                table: "DocCartasETermos",
                column: "PedidoCartasETermosId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PedidoTermoInfos_PedidoId",
                table: "PedidoTermoInfos",
                column: "PedidoId");

            migrationBuilder.CreateIndex(
                name: "IX_PedidoTermoInfos_TipoTermoId",
                table: "PedidoTermoInfos",
                column: "TipoTermoId");

            migrationBuilder.CreateIndex(
                name: "IX_PedidoTermoParcelas_InfosId",
                table: "PedidoTermoParcelas",
                column: "InfosId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ConteudoTermo");

            migrationBuilder.DropTable(
                name: "DocCartasETermos");

            migrationBuilder.DropTable(
                name: "PedidoTermoParcelas");

            migrationBuilder.DropTable(
                name: "PedidoTermoInfos");

            migrationBuilder.DropTable(
                name: "TipoTermo");

            migrationBuilder.DropColumn(
                name: "IdCliente",
                table: "PedidoCartasETermos");

            migrationBuilder.DropColumn(
                name: "IdGrupo",
                table: "PedidoCartasETermos");

            migrationBuilder.DropColumn(
                name: "Tipo",
                table: "PedidoCartasETermos");

            migrationBuilder.AddColumn<int>(
                name: "TipoId",
                table: "PedidoCartasETermos",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_PedidoCartasETermos_TipoId",
                table: "PedidoCartasETermos",
                column: "TipoId");

            migrationBuilder.AddForeignKey(
                name: "FK_PedidoCartasETermos_TipoCartaETermo_TipoId",
                table: "PedidoCartasETermos",
                column: "TipoId",
                principalTable: "TipoCartaETermo",
                principalColumn: "Id");
        }
    }
}
