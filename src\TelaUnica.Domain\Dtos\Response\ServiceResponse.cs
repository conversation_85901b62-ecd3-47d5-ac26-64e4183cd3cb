namespace TelaUnica.Domain.Dtos.Response;

public class ServiceResponse<T>
{
    public T? Data { get; set; }
    public bool Success { get; set; } = true;
    public string Message { get; set; } = string.Empty;

    public void SetFailure(string message)
    {
        Success = false;
        Message = message;
    }
    public void SetFailure(string message, T data)
    {
        Data = data;
        Success = false;
        Message = message;
    }

    public ServiceResponse() { }

    public ServiceResponse(T data)
    {
        Data = data;
    }
}
