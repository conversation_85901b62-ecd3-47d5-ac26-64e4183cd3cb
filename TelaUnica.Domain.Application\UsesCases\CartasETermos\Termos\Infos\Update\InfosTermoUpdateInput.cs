namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Update;

public class InfosTermoUpdateInput
{
    public Guid Id { get; set; }
    public string? JurisdicaoAtual { get; set; }
    public string? NrAtual { get; set; }
    public string? ClientePrincipal { get; set; }
    public string? TipoAcao { get; set; }
    public string? AdversoPrincipal { get; set; }
    public string? GrupoCotaContrato { get; set; }
    public string? NrParcelasVencidas { get; set; }
    public decimal? ValorParcelasVencidas { get; set; }
    public decimal? MultaJuros { get; set; }
    public decimal? Custas { get; set; }
    public string? NrParcelasVincendas { get; set; }
    public decimal? ValorParcelasVincendas { get; set; }
    public decimal? Honorarios { get; set; }
    public decimal? Total { get; set; }
    public DateOnly? DataBase { get; set; }
    public int? QtdParcelasAcordadas { get; set; }
    public decimal? ValorAcordado { get; set; }
    public string? DescricaoVeiculo { get; set; }
    public List<InfosTermoParcelasUpdateInput>? Parcelas { get; set; }
}

public class InfosTermoParcelasUpdateInput
{
    public Guid? Id { get; set; }
    public int? Numero { get; set; }
    public DateOnly? Vencimento { get; set; }
    public decimal? Valor { get; set; }

    public void Validate()
    {
        if (Numero == null)
            throw new("Numero da parcela não pode ser vazio");
        if (Vencimento == null)
            throw new("Vencimento da parcela não pode ser vazio");
        if (Valor == null)
            throw new("Valor da parcela não pode ser vazio");
    }
}