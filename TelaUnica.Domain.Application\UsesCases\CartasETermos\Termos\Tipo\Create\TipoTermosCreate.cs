using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Tipo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Tipo.Create;

public class TipoTermosCreate(
    ITipoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : ITipoTermosCreate
{
    public ITipoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<bool>> Handle(TipoTermoCreateInput input, CancellationToken cancellationToken)
    {
        ServiceResponse<bool> response = new(true);
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            _ = (await _repo.Insert(input.ToModel(), cancellationToken)) ?? throw new();
        }, (ex) => response.SetFailure("Falha ao inserir tipo de termo"));
        return response;
    }
}
