﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AlterTabelaPedidosCartasETermosAddCrm : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Crm",
                table: "PedidoCartasETermos",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_PedidoCartasETermos_IdOperador",
                table: "PedidoCartasETermos",
                column: "IdOperador");

            migrationBuilder.AddForeignKey(
                name: "FK_PedidoCartasETermos_Users_IdOperador",
                table: "PedidoCartasETermos",
                column: "IdOperador",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PedidoCartasETermos_Users_IdOperador",
                table: "PedidoCartasETermos");

            migrationBuilder.DropIndex(
                name: "IX_PedidoCartasETermos_IdOperador",
                table: "PedidoCartasETermos");

            migrationBuilder.DropColumn(
                name: "Crm",
                table: "PedidoCartasETermos");
        }
    }
}
