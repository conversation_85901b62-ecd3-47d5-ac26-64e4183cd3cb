using System.ComponentModel.DataAnnotations.Schema;

namespace TelaUnica.Infra.Data.EF.Models.Datacob;

public class DadosCadastraisDatacob
{
    public int? Id_Contrato { get; set; }
    public string? Numero_Contrato { get; set; } = string.Empty;
    public bool? Contrato_Aberto { get; set; }
    public int? Id_Financiado { get; set; }
    public int? Id_Agrupamento { get; set; }
    public string? Nome { get; set; } = string.Empty;
    public string? CpfCnpj { get; set; } = string.Empty;
    public DateTime? Dt_Nascimento { get; set; }
    public DateTime? Data_Enriquecimento { get; set; }
    public string? Sexo { get; set; }
    public string? Est_Civil { get; set; }
    public int? Score_Serasa { get; set; }
    public byte? Tipo_Financiado { get; set; }
    public string? Rg { get; set; }
    public DateTime? Dt_Emiss_Rg { get; set; }
    public string? Orgao_Emiss_Rg { get; set; }
    public string? Uf_Emiss_Rg { get; set; }
    public string? Tipo_Pessoa { get; set; }
    public string? Conjugue { get; set; }
    public string? Mae { get; set; }
    public string? Pai { get; set; }
    public int? Id_Cliente { get; set; }
    public short? Id_Grupo { get; set; }
    public string? Cliente { get; set; }
    public string? Grupo { get; set; }
    public short? Id_Fase { get; set; }
    public string? Cod_Fase { get; set; }
    public string? Fase { get; set; }
    public string? Cor { get; set; }
    public string? Status { get; set; }
    public decimal? Vl_contr { get; set; }
    [NotMapped]
    public string? coddatacob { get; set; }

    [NotMapped]
    public short? IdLinkedGroup { get; set; }

    [NotMapped]
    public string? LinkedGroup { get; set; }
}