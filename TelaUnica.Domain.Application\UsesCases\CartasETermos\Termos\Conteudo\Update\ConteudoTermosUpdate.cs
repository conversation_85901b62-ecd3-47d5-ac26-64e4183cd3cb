using TelaUnica.Domain.Dtos.Response;
using TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;
using TelaUnica.Domain.Interfaces.Datacob;
using TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Common;
using TelaUnica.Domain.Utils;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Conteudo.Update;

public class ConteudoTermosUpdate(
    IConteudoTermoRepository repo,
    IDatacobDapperRepository dapperDCRep
) : IConteudoTermosUpdate
{
    public IConteudoTermoRepository _repo = repo;
    public readonly IDatacobDapperRepository _dapperDcRep = dapperDCRep;

    public async Task<ServiceResponse<ConteudoTermosResponse>> Handle(ConteudoTermoUpdateInput input, CancellationToken cancellationToken)
    {
        ServiceResponse<ConteudoTermosResponse> response = new();
        await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var model = await _repo.Get(input.Id, cancellationToken);
            if ((input.GrupoId != null && model.GrupoId == null) || (input.Crm != null && model.Crm == null))
                throw new("Não é possível alterar o grupo ou o CRM do termo padrão.");

            model.Html = input.Html ?? model.Html;
            model.GrupoId = input.GrupoId ?? model.GrupoId;
            model.Crm = input.Crm ?? model.Crm;

            if (input.CabecalhoImg != null && input.CabecalhoImg.Length > 0)
            {
                using var memoryStream = new MemoryStream();
                await input.CabecalhoImg.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();
                string base64String = Convert.ToBase64String(fileBytes);
                model.CabecalhoImg = base64String;
            }

            if (input.RodapeImg != null && input.RodapeImg.Length > 0)
            {
                using var memoryStream = new MemoryStream();
                await input.RodapeImg.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();
                string base64String = Convert.ToBase64String(fileBytes);
                model.RodapeImg = base64String;
            }

            await _repo.Update(model, cancellationToken);
            response.Data = ConteudoTermosResponse.FromModel(model);
        }, (ex) => response.SetFailure(ex.Message));
        return response;
    }
}
