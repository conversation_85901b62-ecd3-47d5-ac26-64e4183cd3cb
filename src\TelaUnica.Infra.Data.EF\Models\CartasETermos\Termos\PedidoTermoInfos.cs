using System;
using System.ComponentModel.DataAnnotations.Schema;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.SeedWork;

namespace TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

public class PedidoTermoInfos : Entity
{

    [ForeignKey("Pedido")]
    public Guid PedidoId { get; set; }
    public virtual PedidoCartasETermos? Pedido { get; set; }

    [ForeignKey("TipoTermo")]
    public Guid TipoTermoId { get; set; }
    public virtual TipoTermo? TipoTermo { get; set; }
    public virtual List<PedidoTermoParcelas>? PedidoTermoParcelas { get; set; }

    [Column(TypeName = "varchar(255)")]
    public string JurisdicaoAtual { get; set; } = string.Empty;

    [Column(TypeName = "varchar(255)")]
    public string NrAtual { get; set; } = string.Empty;
    
    [Column(TypeName = "varchar(255)")]
    public string ClientePrincipal { get; set; } = string.Empty;
    
    [Column(TypeName = "varchar(255)")]
    public string TipoAcao { get; set; } = string.Empty;
    
    [Column(TypeName = "varchar(255)")]
    public string AdversoPrincipal { get; set; } = string.Empty;
    
    [Column(TypeName = "varchar(255)")]
    public string GrupoCotaContrato { get; set; } = string.Empty;
    
    [Column(TypeName = "varchar(255)")]
    public string NrParcelasVencidas { get; set; } = string.Empty;

    [Column(TypeName = "decimal(18,2)")]
    public decimal ValorParcelasVencidas { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal MultaJuros { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal Custas { get; set; }
    
    [Column(TypeName = "varchar(255)")]
    public string? NrParcelasVincendas { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal? ValorParcelasVincendas { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal Honorarios { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal Total { get; set; }
    public DateOnly DataBase { get; set; }
    public int QtdParcelasAcordadas { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal ValorAcordado { get; set; }
    
    [Column(TypeName = "varchar(255)")]
    public string? DescricaoVeiculo { get; set; }

}
