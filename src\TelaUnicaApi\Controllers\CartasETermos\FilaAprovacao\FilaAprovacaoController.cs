
using Microsoft.AspNetCore.Mvc;
using TelaUnica.Domain.Dtos.Response;
using UCFilaGet = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Get;
using UCFilaUpdate = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Update;
using UCFilaGenerate = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Generate;
using UCFilaCommon = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;
using UCFila = TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao;

namespace TelaUnica.Api.Controllers.CartasETermos.FilaAprovacao
{
    [ApiController]
    [Route("api/CartasETermos/[controller]")]
    public class FilaAprovacaoController(
        UCFilaGet.IFilaAprovacaoCartasETermosGet filaAprovacaoCartasETermosGet,
        UCFilaUpdate.IFilaAprovacaoCartasETermosUpdateStatus filaAprovacaoCartasETermosUpdateStatus,
        UCFilaGenerate.ByPedido.IFilaAprovacaoCETGenerateByPedido filaAprovacaoCETGenerateByPedido,
        UCFilaGenerate.Preview.IFilaAprovacaoCETGeneratePreview filaAprovacaoCETGeneratePreview,
        UCFilaGenerate.Custom.IFilaAprovacaoCETGenerateCustom filaAprovacaoCETGenerateCustom,
        UCFila.Create.IFilaAprovacaoCartasETermosCreate filaAprovacaoCartasETermosCreate
        ) : ControllerBase
    {
        private readonly UCFilaGet.IFilaAprovacaoCartasETermosGet _FilaAprovacaoCartasETermosGet = filaAprovacaoCartasETermosGet;
        private readonly UCFilaUpdate.IFilaAprovacaoCartasETermosUpdateStatus _FilaAprovacaoCartasETermosUpdateStatus = filaAprovacaoCartasETermosUpdateStatus;
        private readonly UCFilaGenerate.ByPedido.IFilaAprovacaoCETGenerateByPedido _FilaAprovacaoCETGenerateByPedido = filaAprovacaoCETGenerateByPedido;
        private readonly UCFilaGenerate.Preview.IFilaAprovacaoCETGeneratePreview _FilaAprovacaoCETGeneratePreview = filaAprovacaoCETGeneratePreview;
        private readonly UCFilaGenerate.Custom.IFilaAprovacaoCETGenerateCustom _FilaAprovacaoCETGenerateCustom = filaAprovacaoCETGenerateCustom;
        private readonly UCFila.Create.IFilaAprovacaoCartasETermosCreate _FilaAprovacaoCartasETermosCreate = filaAprovacaoCartasETermosCreate;

        [HttpGet]
        public async Task<ActionResult<ServiceResponse<List<UCFilaCommon.ItemFilaAprovacaoCartasETermosResponse>>>> GetList(CancellationToken cancellationToken)
            => Ok(await _FilaAprovacaoCartasETermosGet.Handle(cancellationToken));
        

        [HttpPut("updateStatus")]
        public async Task<ActionResult<ServiceResponse<bool>>> UpdateStatus([FromBody] UCFilaUpdate.FilaAprovacaoCartasETermosUpdateStatusInput request, CancellationToken cancellationToken)
            => Ok(await _FilaAprovacaoCartasETermosUpdateStatus.Handle(request.Id, request.Status, cancellationToken));

        [HttpPost("Create")]
        public async Task<ActionResult<ServiceResponse<bool>>> Create([FromBody] UCFila.Create.FilaAprovacaoCartasETermosCreateStatusInput request, CancellationToken cancellationToken)
            => Ok(await _FilaAprovacaoCartasETermosCreate.Handle(request, cancellationToken));
        

        [HttpPost("Generate/ByPedido")]
        public async Task<IActionResult> Generate(Guid idPedido, CancellationToken cancellationToken)
        {
            return File((await _FilaAprovacaoCETGenerateByPedido.Handle(idPedido, cancellationToken)).Data, "application/pdf");
        }

        [HttpPost("Generate/Preview")]
        public async Task<IActionResult> GeneratePreview(UCFilaGenerate.Preview.FilaAprovacaoCETGeneratePreviewInput request, CancellationToken cancellationToken)
        {
            return File((await _FilaAprovacaoCETGeneratePreview.Handle(request, cancellationToken)).Data, "application/pdf");
        }

        [HttpPost("Generate/Custom")]
        public async Task<IActionResult> GenerateCustom(UCFilaGenerate.Custom.FilaAprovacaoCETGenerateCustomInput request, CancellationToken cancellationToken)
        {
            return File(_FilaAprovacaoCETGenerateCustom.Handle(request, cancellationToken).Data, "application/pdf");
        }
    }
}
