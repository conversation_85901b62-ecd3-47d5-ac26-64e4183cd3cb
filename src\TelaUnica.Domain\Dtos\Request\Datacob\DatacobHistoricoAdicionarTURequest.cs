
using TelaUnica.Infra.Data.EF.Enuns;

namespace TelaUnica.Domain.Dtos.Request.Datacob;

public class DatacobHistoricoAdicionarTURequest
{
    public required string Login { get; set; }
    public required int Id_Contrato { get; set; }
    public required int Id_Ocorrencia_Sistema { get; set; }
    public required string Complemento { get; set; }
    public required string Observacao { get; set; }
    public List<string>? Telefones { get; set; }
    public CallType? CallType { get; set; }
    public string? TelefoneParaRetorno { get; set; }
    public GvcRodobens? Crm { get; set; }
}