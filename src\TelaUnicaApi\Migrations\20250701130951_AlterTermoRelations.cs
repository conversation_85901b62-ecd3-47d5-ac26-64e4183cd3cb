﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TelaUnica.Infra.Data.EF.Migrations
{
    /// <inheritdoc />
    public partial class AlterTermoRelations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PedidoTermoInfos_PedidoId",
                table: "PedidoTermoInfos");

            migrationBuilder.CreateIndex(
                name: "IX_PedidoTermoInfos_PedidoId",
                table: "PedidoTermoInfos",
                column: "PedidoId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PedidoTermoInfos_PedidoId",
                table: "PedidoTermoInfos");

            migrationBuilder.CreateIndex(
                name: "IX_PedidoTermoInfos_PedidoId",
                table: "PedidoTermoInfos",
                column: "PedidoId");
        }
    }
}
