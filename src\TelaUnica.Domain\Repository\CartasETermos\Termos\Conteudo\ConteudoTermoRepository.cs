using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Data;
using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Repository.CartasETermos.Termos.Conteudo;

public class ConteudoTermoRepository(DataContext context, IHttpContextAccessor httpContextAccessor) : IConteudoTermoRepository
{
    private readonly DataContext _context = context;
    public readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public async Task Delete(ConteudoTermo aggregate, CancellationToken cancellationToken)
    {
        aggregate.Delete();
        _context.ConteudoTermo.Update(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<ConteudoTermo> Get(Guid id, CancellationToken cancellationToken)
    {
        return await _context.ConteudoTermo.Include(t => t.TipoTermo).FirstOrDefaultAsync(x => x.Id == id, cancellationToken) ?? throw new("ConteudoTermo not found");
    }

    public async Task<List<ConteudoTermo>> GetList(CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var items = await _context.ConteudoTermo
                 .Include(t => t.TipoTermo)
                 .AsNoTracking()
                 .ToListAsync();

            return items;
        }, []);
    }


    public async Task<object> Insert(ConteudoTermo aggregate, CancellationToken cancellationToken)
    {
        _context.ConteudoTermo.Add(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
        return aggregate;
    }

    public async Task Update(ConteudoTermo aggregate, CancellationToken cancellationToken)
    {
        aggregate.Update();
        _context.ConteudoTermo.Update(aggregate);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<ConteudoTermo?> GetByTipo(Guid tipoTermoId, int? grupoId, GvcRodobens? crm, CancellationToken cancellationToken)
    {
        return await _context.ConteudoTermo.FirstOrDefaultAsync(x => x.TipoTermoId == tipoTermoId && x.GrupoId == null && x.Crm == null, cancellationToken);
    }

    public async Task<ConteudoTermo> GetByTipoOrDefault(Guid tipoTermoId, int? grupoId, GvcRodobens? crm, CancellationToken cancellationToken)
    {
        var conteudo = await _context.ConteudoTermo.FirstOrDefaultAsync(x => x.TipoTermoId == tipoTermoId && x.GrupoId == grupoId && x.Crm == crm, cancellationToken);
        if (conteudo is null)
            return await _context.ConteudoTermo.FirstOrDefaultAsync(x => x.TipoTermoId == tipoTermoId && x.GrupoId == null && x.Crm == null, cancellationToken) ?? throw new("ConteudoTermo não encontrado.");
        return conteudo;
    }

}
