using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using TelaUnica.Domain.Utils;
using TelaUnica.Infra.Data.EF.Data;
using TelaUnica.Infra.Data.EF.Models.CartasETermos;

namespace TelaUnica.Domain.Repository.CartasETermos;

public class PedidoCartasETermosRepository(DataContext context, IHttpContextAccessor httpContextAccessor) : IPedidoCartasETermosRepository
{
    private readonly DataContext _context = context;
    public readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public Task Delete(PedidoCartasETermos aggregate, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public async Task<PedidoCartasETermos> Get(Guid id, CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            return await _context.PedidoCartasETermos
                .Include(t => t.PedidoTermoInfos)
                    .ThenInclude(t => t != null ? t.TipoTermo : null)
                .Include(t => t.DocCartasETermos)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken) ?? throw new("Pedido não encontrado.");
        }, null!);
    }

    public async Task<PedidoCartasETermos?> GetPedido(Guid id, CancellationToken cancellationToken)
    {        
        return await _context.PedidoCartasETermos
            .Include(t => t.PedidoTermoInfos)
                .ThenInclude(t => t != null ? t.TipoTermo : null)
            .Include(t => t.PedidoTermoInfos)
                .ThenInclude(t => t != null ? t.PedidoTermoParcelas : null)
            .Include(t => t.DocCartasETermos)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<List<PedidoCartasETermos>> GetList(CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            return await _context.PedidoCartasETermos
                .Include(t => t.PedidoTermoInfos)
                .AsNoTracking()
                .ToListAsync();
        }, []);
    }


    public async Task<object> Insert(PedidoCartasETermos aggregate, CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            _context.PedidoCartasETermos.Add(aggregate);
            await _context.SaveChangesAsync(cancellationToken);
            return aggregate;
        }, null!);
    }

    public Task Update(PedidoCartasETermos aggregate, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> UpdateStatus(Guid Id, string status, CancellationToken cancellationToken)
    {
        return await ExceptionHelpers.SafeExecuteAsync(async () =>
        {
            var pedido = await _context.PedidoCartasETermos.FirstOrDefaultAsync(x => x.Id == Id);
            if (pedido is null)
                return false;

            pedido.Status = status;
            pedido.Update();
            _context.PedidoCartasETermos.Update(pedido);
            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }, false);
    }
}
