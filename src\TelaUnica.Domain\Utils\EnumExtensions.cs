﻿using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace TelaUnica.Domain.Utils;
public static class EnumExtensions
{
    public static string GetDisplayName(this Enum enumValue)
    {
        return enumValue.GetType()
                        .GetMember(enumValue.ToString())[0]
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? string.Empty;
    }

    public static CotaGrupoVersao GetCotaGrupoVersao(this string value)
    {
        if (string.IsNullOrWhiteSpace(value)) return new CotaGrupoVersao();

        return new CotaGrupoVersao()
        {
            Cota = value.Split(" ")[0],
            Grupo = value.Split(" ")[1],
            Versao = value.Split(" ")[2]
        };

    }
}

public class CotaGrupoVersao
{
    public string Cota { get; set; } = string.Empty;
    public string Grupo { get; set; } = string.Empty;
    public string Versao { get; set; } = string.Empty;

}    
