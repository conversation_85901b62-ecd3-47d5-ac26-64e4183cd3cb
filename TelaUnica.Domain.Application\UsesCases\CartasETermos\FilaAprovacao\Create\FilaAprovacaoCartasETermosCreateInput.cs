﻿using TelaUnica.Infra.Data.EF.Enuns;
using Models = TelaUnica.Infra.Data.EF.Models.CartasETermos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Create;

public class FilaAprovacaoCartasETermosCreateStatusInput
{
    public int IdOperador { get; set; }
    public int IdFinanciado { get; set; }
    public int IdContrato { get; set; }
    public int IdCliente { get; set; }
    public int IdGrupo { get; set; }
    public GvcRodobens Crm { get; set; }
    public TipoCartaTermo Tipo { get; set; }

    public Models.PedidoCartasETermos ToModel() => new()
    {
        IdOperador = IdOperador,
        IdFinanciado = IdFinanciado,
        IdContrato = IdContrato,
        IdCliente = IdCliente,
        IdGrupo = IdGrupo,
        Crm = Crm,
        Tipo = Tipo
    };
}
