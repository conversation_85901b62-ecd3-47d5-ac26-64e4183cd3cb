﻿using TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Common;
using TelaUnica.Domain.Dtos.Response;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.FilaAprovacao.Create
{
    public interface IFilaAprovacaoCartasETermosCreate
    {
        public Task<ServiceResponse<ItemFilaAprovacaoCartasETermosResponse>> Handle(FilaAprovacaoCartasETermosCreateStatusInput input, CancellationToken cancellationToken);
    }
}
