using TelaUnica.Infra.Data.EF.Enuns;
using TelaUnica.Infra.Data.EF.Models.CartasETermos.Termos;

namespace TelaUnica.Domain.Application.UsesCases.CartasETermos.Termos.Infos.Common;

public record InfosTermosResponse(
    Guid Id, Guid TipoTermoId, Guid PedidoId, string JurisdicaoAtual, string NrAtual, string ClientePrincipal, string TipoAcao, string AdversoPrincipal,
    string GrupoCotaContrato, string NrParcelasVencidas, decimal ValorParcelasVencidas, decimal MultaJuros, decimal Custas, string? NrParcelasVincendas,
    decimal? ValorParcelasVincendas, decimal Honorarios, decimal Total, DateOnly DataBase, int QtdParcelasAcordadas, decimal ValorAcordado, string? DescricaoVeiculo,
    DateTime CreatedAt, List<InfosParcelasTermosResponse> Parcelas
)
{
    public static InfosTermosResponse FromModel(PedidoTermoInfos model)
    {
        return new(model.Id, model.TipoTermoId, model.PedidoId, model.JurisdicaoAtual, model.NrAtual, model.ClientePrincipal, model.TipoAcao,
            model.AdversoPrincipal, model.GrupoCotaContrato, model.NrParcelasVencidas, model.ValorParcelasVencidas, model.MultaJuros, model.Custas,
            model.NrParcelasVincendas, model.ValorParcelasVincendas, model.Honorarios, model.Total, model.DataBase, model.QtdParcelasAcordadas,
            model.ValorAcordado, model.DescricaoVeiculo, model.CreatedAt, model.PedidoTermoParcelas?.Select(InfosParcelasTermosResponse.FromModel)?.ToList() ?? []);    
    }
}

public record InfosParcelasTermosResponse(
    Guid Id, Guid InfosId, int Numero, DateOnly Vencimento, decimal Valor
)
{
    public static InfosParcelasTermosResponse FromModel(PedidoTermoParcelas model)
    {
        return new(model.Id, model.InfosId, model.Numero, model.Vencimento, model.Valor);
    }
}